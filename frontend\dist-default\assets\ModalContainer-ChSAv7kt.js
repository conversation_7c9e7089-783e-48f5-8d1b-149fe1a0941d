import{r as o,j as e,K as r,G as t,T as s,Y as a,aD as i,ai as n}from"./vendor-DvOQ6qlC.js";import{t as l}from"./index-C0IC_AUQ.js";const d={position:"absolute",top:"50%",left:"50%",transform:"translate(-50%, -50%)",bgcolor:l.palette.custom.darkBlue,boxShadow:24,borderRadius:"8px",width:{xs:"auto",sm:"auto"},padding:2,outline:"none !important"},c=o.forwardRef((({children:c,title:x,onClose:p,headerPosition:u="space-between",showDivider:h=!1},j)=>(o.useEffect((()=>{j.current&&j.current.focus()}),[j]),e.jsx(r,{ref:j,sx:d,tabIndex:0,role:"dialog",children:e.jsxs(t,{container:!0,gap:1,flexDirection:"column",color:"#FFFFFF",children:[e.jsxs(t,{container:!0,position:"relative",justifyContent:u,children:[e.jsx(t,{children:e.jsx(s,{variant:"h6",children:x})}),p&&e.jsx(t,{position:"absolute",right:-5,top:-5,children:e.jsx(a,{onClick:p,children:e.jsx(i,{})})})]}),h&&e.jsx(n,{sx:{backgroundColor:l.palette.custom.borderColor,marginBottom:2}}),e.jsx(t,{children:c})]})}))));c.displayName="ModalContainer";export{c as M};

import{b2 as e,r as s,V as a,j as r,G as t,T as o,W as n,L as i,a0 as l}from"./vendor-DvOQ6qlC.js";import{e as c}from"./index-C0IC_AUQ.js";import"./utils-guRmN1PB.js";import"./maps-R0vlfPHe.js";import"./charts-Bh3hGOgg.js";const d=()=>{const{token:d}=e(),[u,m]=s.useState(""),[h,p]=s.useState(""),[j,x]=s.useState(""),[f,w]=s.useState("error"),[g,v]=s.useState(!1),b=a();return r.jsxs(t,{container:!0,flexDirection:"column",maxWidth:591,gap:4,children:[r.jsx(t,{container:!0,flexDirection:"column",color:"#FFFFFF",children:r.jsx(t,{children:r.jsx(o,{variant:"h3",fontWeight:"bold",children:"Reset Password"})})}),r.jsxs(t,{container:!0,flexDirection:"column",component:"form",onSubmit:async e=>{if(e.preventDefault(),v(!0),x(""),u!==h)return w("error"),void x("Passwords do not match.");try{await c.post(`/users/reset-password/${d}`,{password:u},{meta:{showSnackbar:!1}}),w("success.main"),x("Password has been reset successfully."),setTimeout((()=>{b("/login")}),2e3),v(!1)}catch(s){w("error"),x(s.response.data?.message||s.message),v(!1)}},gap:4,children:[r.jsx(t,{children:r.jsx(n,{className:"input-login",type:"password",placeholder:"New Password",variant:"outlined",fullWidth:!0,value:u,onChange:e=>m(e.target.value),required:!0})}),r.jsx(t,{children:r.jsx(n,{className:"input-login",type:"password",placeholder:"Confirm New Password",variant:"outlined",fullWidth:!0,value:h,onChange:e=>p(e.target.value),required:!0})}),r.jsx(t,{display:j?"block":"none",children:r.jsx(o,{color:f,children:j})}),r.jsx(t,{children:r.jsx(i,{className:"btn-login",type:"submit",variant:"contained",color:"primary",fullWidth:!0,disabled:g,endIcon:g&&r.jsx(l,{}),children:"Reset Password"})})]})]})};export{d as default};

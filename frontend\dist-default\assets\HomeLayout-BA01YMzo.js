import{r as t,j as e,U as r,G as s,b0 as n}from"./vendor-DvOQ6qlC.js";import{t as o,r as i}from"./index-C0IC_AUQ.js";import"./utils-guRmN1PB.js";import"./maps-R0vlfPHe.js";import"./charts-Bh3hGOgg.js";const a=()=>{const[a,m]=t.useState(""),l=t.useRef();t.useEffect((()=>{a&&(clearTimeout(l.current),l.current=setTimeout((()=>m("")),3e3))}),[a]);return(t=>{if(!t)return!0;try{const e=i(t),r=Date.now()/1e3;return e.exp<r}catch(e){return!0}})(localStorage.getItem("jwt_token"))?e.jsxs(s,{container:!0,overflow:"auto",bgcolor:"primary.main",flexDirection:{lg:"row",xs:"column-reverse"},minHeight:"100vh",justifyContent:"start",children:[e.jsxs(s,{container:!0,display:{xs:"none",sm:"none",md:"none",lg:"flex"},justifyContent:"flex-end",alignItems:"center",flexDirection:"column",gap:{xs:2,sm:3},paddingBottom:6,borderRadius:"0 20px 0 0",sx:{background:`\n                        linear-gradient(\n                            180.03deg, \n                            rgba(0, 0, 0, 0) 51.41%, \n                            rgba(28, 38, 55, 0.929461) 84.58%, \n                            ${o.palette.primary.main} 99.97%\n                        ), \n                        url(/home-layout-bg.jpg)\n                    `,backgroundSize:"cover",backgroundPosition:"center"},size:{xs:"grow",lg:5},children:[e.jsx(s,{width:{xs:100,sm:200,md:300},children:e.jsx("img",{src:"/quartermaster-logo-home.svg",alt:"Quartermaster Logo",width:"100%"})}),e.jsx(s,{width:{xs:150,sm:250,md:400},children:e.jsx("img",{src:"/quartermaster-text-home.png",alt:"Quartermaster Logo",width:"100%"})})]}),e.jsxs(s,{container:!0,justifyContent:{lg:"flex-start",xs:"center"},alignItems:"center",position:"relative",paddingY:5,paddingLeft:{lg:15,md:0},size:{xs:"grow",lg:7},children:[e.jsx(s,{width:{xs:300},position:"absolute",bottom:0,right:0,children:e.jsx("img",{src:"/quartermaster-watermark.png",style:{position:"absolute",bottom:0,left:0,pointerEvents:"none"},width:"100%"})}),e.jsx(s,{size:{xs:9,sm:8},children:e.jsx(n,{})})]})]}):e.jsx(r,{to:"/dashboard/stream"})};export{a as default};

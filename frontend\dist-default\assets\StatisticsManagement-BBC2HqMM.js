import{j as e,G as t,T as s,r as o,R as n,aE as i,z as r,g as a,a0 as l,Y as d,c2 as c,c3 as u,K as h,bC as p,c4 as f,bE as x,bb as g,bc as y,bd as m,ac as b,by as v,L as j,ag as S,af as w,c5 as C,x as D}from"./vendor-DvOQ6qlC.js";import{t as k,u as M,e as T,p as O,F,a as A,b as _,l as P,h as V,G as z,H as I,c as E,I as W,D as R,J as H}from"./index-C0IC_AUQ.js";import{u as Y}from"./AppHook-DfcCRIWI.js";import{C as X,B as N,P as B,L,D as $,a as q,b as U,c as J,p as G,d as K,f as Q,A as Z,g as ee,h as te,t as se,r as oe,i as ne,j as ie,k as re,l as ae,m as le,S as de,M as ce,n as ue,o as he,q as pe,s as fe,u as xe,v as ge,w as ye,x as me,y as be,z as ve,E as je,F as Se,G as we,H as Ce,I as De,J as ke,K as Me,N as Te,O as Oe,Q as Fe,R as Ae,T as _e,U as Pe,V as Ve,W as ze,X as Ie,Y as Ee,Z as We,_ as Re,$ as He,a0 as Ye,a1 as Xe,a2 as Ne,a3 as Be,a4 as Le,a5 as $e,a6 as qe,a7 as Ue,a8 as Je,a9 as Ge,aa as Ke,ab as Qe,ac as Ze,ad as et,ae as tt,af as st,ag as ot,ah as nt,ai as it,aj as rt,ak as at,al as lt,am as dt,an as ct,ao as ut,ap as ht,aq as pt,ar as ft,as as xt,at as gt,au as yt,av as mt,aw as bt,ax as vt,ay as jt,az as St,aA as wt,aB as Ct,aC as Dt,aD as kt,aE as Mt,aF as Tt,aG as Ot,aH as Ft,aI as At,aJ as _t,aK as Pt,aL as Vt,aM as zt,aN as It,aO as Et,aP as Wt}from"./charts-Bh3hGOgg.js";import{u as Rt}from"./VesselInfoHook-CSuF_4V0.js";import{D as Ht}from"./DataGrid-DsxndKbs.js";import"./utils-guRmN1PB.js";import"./maps-R0vlfPHe.js";function Yt({title:o,value:n,values:i,icon:r}){const{screenSize:a}=Y();return e.jsxs(t,{container:!0,height:"100%",bgcolor:"primary.main",flexDirection:a.xs?"column":"row",justifyContent:a.xs?"center":"flex-start",alignItems:"center",borderRadius:"20px",paddingX:2,paddingY:1,boxShadow:2,minHeight:125,gap:2,children:[e.jsx(t,{display:"flex",alignItems:"center",children:e.jsx("img",{src:r,alt:"KPICard Icon"})}),e.jsxs(t,{container:!0,width:"auto",flexDirection:"column",justifyContent:"center",alignItems:a.xs?"center":"flex-start",children:[e.jsx(t,{children:e.jsx(s,{variant:"body1",fontWeight:"400",fontSize:"16px",color:"#A0AEC0",textAlign:a.xs?"center":"left",children:o})}),void 0!==n?e.jsx(t,{children:e.jsx(s,{variant:"h5",fontWeight:"600",fontSize:"24px",children:n<10?"0"+n:n})}):i?e.jsx(t,{container:!0,gap:2,flexDirection:a.xs?"column":"row",justifyContent:a.xs?"center":"flex-start",alignItems:a.xs?"center":"flex-start",children:i.map(((o,n)=>e.jsxs(t,{container:!0,flexDirection:"column",size:"auto",children:[e.jsx(t,{children:e.jsx(s,{variant:"caption",fontWeight:"400",fontSize:"12px",color:"#A0AEC0",children:o.title})}),e.jsx(t,{children:e.jsx(s,{variant:"h5",fontWeight:"600",fontSize:"24px",children:o.value<10?"0"+o.value:o.value})})]},n)))}):e.jsx(e.Fragment,{})]})]})}const Xt="label";function Nt(e,t){"function"==typeof e?e(t):e&&(e.current=t)}function Bt(e,t){e.labels=t}function Lt(e,t){let s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:Xt;const o=[];e.datasets=t.map((t=>{const n=e.datasets.find((e=>e[s]===t[s]));return n&&t.data&&!o.includes(n)?(o.push(n),Object.assign(n,t),n):{...t}}))}function $t(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Xt;const s={labels:[],datasets:[]};return Bt(s,e.labels),Lt(s,e.datasets,t),s}function qt(e,t){const{height:s=150,width:i=300,redraw:r=!1,datasetIdKey:a,type:l,data:d,options:c,plugins:u=[],fallbackContent:h,updateMode:p,...f}=e,x=o.useRef(null),g=o.useRef(null),y=()=>{x.current&&(g.current=new X(x.current,{type:l,data:$t(d,a),options:c&&{...c},plugins:u}),Nt(t,g.current))},m=()=>{Nt(t,null),g.current&&(g.current.destroy(),g.current=null)};return o.useEffect((()=>{!r&&g.current&&c&&function(e,t){const s=e.options;s&&t&&Object.assign(s,t)}(g.current,c)}),[r,c]),o.useEffect((()=>{!r&&g.current&&Bt(g.current.config.data,d.labels)}),[r,d.labels]),o.useEffect((()=>{!r&&g.current&&d.datasets&&Lt(g.current.config.data,d.datasets,a)}),[r,d.datasets]),o.useEffect((()=>{g.current&&(r?(m(),setTimeout(y)):g.current.update(p))}),[r,c,d.labels,d.datasets,p]),o.useEffect((()=>{g.current&&(m(),setTimeout(y))}),[l]),o.useEffect((()=>(y(),()=>m())),[]),n.createElement("canvas",{ref:x,role:"img",height:s,width:i,...f},h)}const Ut=o.forwardRef(qt);function Jt(e,t){return X.register(t),o.forwardRef(((t,s)=>n.createElement(Ut,{...t,ref:s,type:e})))}const Gt=Jt("line",L),Kt=Jt("bar",N),Qt=Jt("doughnut",$),Zt=Jt("pie",B);function es({data:t,options:s}){const o={responsive:!0,scales:{x:{grid:{display:!1},ticks:{color:"#FFFFFF",maxRotation:90}},y:{grid:{display:!1},ticks:{color:"#FFFFFF"}}},plugins:{legend:{labels:{color:"#FFFFFF",usePointStyle:!0,pointStyle:"circle"}}},...s};return e.jsx(Kt,{data:t,options:o})}function ts({data:t,options:s}){const o={responsive:!0,plugins:{legend:{position:"right",labels:{color:"#FFFFFF",usePointStyle:!0,pointStyle:"circle"}}},...s};return e.jsx(Zt,{data:t,options:o})}function ss({data:t,options:s}){const o={...{responsive:!0,scales:{x:{grid:{color:"#282C39"},ticks:{color:"#FFFFFF"}},y:{grid:{color:"#282C39"},ticks:{color:"#FFFFFF"}}},plugins:{legend:{labels:{color:"#FFFFFF",usePointStyle:!0,pointStyle:"circle"}}},elements:{line:{borderColor:k.palette.custom.mainBlue,borderWidth:1},point:{radius:4}}},...s};return e.jsx(Gt,{data:t,options:o})}X.register(q,U,J,G,K,Q),X.register(q,U,J,Z,G,K,Q),X.register(q,U,J,Z,ee,te,G,K,Q);var os={};const ns=i(se);var is,rs={};var as,ls={};var ds,cs={};var us,hs,ps={};const fs=a(function(){if(hs)return os;hs=1,Object.defineProperty(os,"__esModule",{value:!0});var e=ns,t=(0,e.__importStar)(r()),s=ne(),o=(is||(is=1,Object.defineProperty(rs,"__esModule",{value:!0}),rs.pick=void 0,rs.pick=function(e,t){var s={};return t.forEach((function(t){s[t]=e[t]})),s}),rs),n=(as||(as=1,Object.defineProperty(ls,"__esModule",{value:!0}),ls.isFunction=void 0,ls.isFunction=function(e){return"function"==typeof e}),ls),i=(ds||(ds=1,Object.defineProperty(cs,"__esModule",{value:!0}),cs.isString=void 0,cs.isString=function(e){return"string"==typeof e}),cs),a=function(){if(us)return ps;us=1,Object.defineProperty(ps,"__esModule",{value:!0}),ps.isEqual=void 0;var e=(0,ns.__importDefault)(oe());return ps.isEqual=e.default,ps}(),l=function(r){function l(e){var t=r.call(this,e)||this;return t.echarts=e.echarts,t.ele=null,t.isInitialResize=!0,t}return(0,e.__extends)(l,r),l.prototype.componentDidMount=function(){this.renderNewEcharts()},l.prototype.componentDidUpdate=function(e){var t=this.props.shouldSetOption;if(!(0,n.isFunction)(t)||t(e,this.props)){if(!(0,a.isEqual)(e.theme,this.props.theme)||!(0,a.isEqual)(e.opts,this.props.opts)||!(0,a.isEqual)(e.onEvents,this.props.onEvents))return this.dispose(),void this.renderNewEcharts();var s=["option","notMerge","lazyUpdate","showLoading","loadingOption"];(0,a.isEqual)((0,o.pick)(this.props,s),(0,o.pick)(e,s))||this.updateEChartsOption(),(0,a.isEqual)(e.style,this.props.style)&&(0,a.isEqual)(e.className,this.props.className)||this.resize()}},l.prototype.componentWillUnmount=function(){this.dispose()},l.prototype.getEchartsInstance=function(){return this.echarts.getInstanceByDom(this.ele)||this.echarts.init(this.ele,this.props.theme,this.props.opts)},l.prototype.dispose=function(){if(this.ele){try{(0,s.clear)(this.ele)}catch(e){}this.echarts.dispose(this.ele)}},l.prototype.renderNewEcharts=function(){var e=this,t=this.props,o=t.onEvents,i=t.onChartReady,r=this.updateEChartsOption();this.bindEvents(r,o||{}),(0,n.isFunction)(i)&&i(r),this.ele&&(0,s.bind)(this.ele,(function(){e.resize()}))},l.prototype.bindEvents=function(e,t){function s(t,s){(0,i.isString)(t)&&(0,n.isFunction)(s)&&e.on(t,(function(t){s(t,e)}))}for(var o in t)Object.prototype.hasOwnProperty.call(t,o)&&s(o,t[o])},l.prototype.updateEChartsOption=function(){var e=this.props,t=e.option,s=e.notMerge,o=void 0!==s&&s,n=e.lazyUpdate,i=void 0!==n&&n,r=e.showLoading,a=e.loadingOption,l=void 0===a?null:a,d=this.getEchartsInstance();return d.setOption(t,o,i),r?d.showLoading(l):d.hideLoading(),d},l.prototype.resize=function(){var e=this.getEchartsInstance();if(!this.isInitialResize)try{e.resize()}catch(t){}this.isInitialResize=!1},l.prototype.render=function(){var s=this,o=this.props,n=o.style,i=o.className,r=void 0===i?"":i,a=(0,e.__assign)({height:300},n);return t.default.createElement("div",{ref:function(e){s.ele=e},style:a,className:"echarts-for-react "+r})},l}(t.PureComponent);return os.default=l,os}()),xs=Object.freeze(Object.defineProperty({__proto__:null,Axis:ie,ChartView:re,ComponentModel:ae,ComponentView:le,List:de,Model:ce,PRIORITY:ue,SeriesModel:he,color:pe,connect:fe,dataTool:xe,dependencies:ge,disConnect:ye,disconnect:me,dispose:be,env:ve,extendChartView:je,extendComponentModel:Se,extendComponentView:we,extendSeriesModel:Ce,format:De,getCoordinateSystemDimensions:ke,getInstanceByDom:Me,getInstanceById:Te,getMap:Oe,graphic:Fe,helper:Ae,init:_e,innerDrawElementOnCanvas:Pe,matrix:Ve,number:ze,parseGeoJSON:Ie,parseGeoJson:Ie,registerAction:Ee,registerCoordinateSystem:We,registerLayout:Re,registerLoading:He,registerLocale:Ye,registerMap:Xe,registerPostInit:Ne,registerPostUpdate:Be,registerPreprocessor:Le,registerProcessor:$e,registerTheme:qe,registerTransform:Ue,registerUpdateLifecycle:Je,registerVisual:Ge,setCanvasCreator:Ke,setPlatformAPI:Qe,throttle:Ze,time:et,use:tt,util:st,vector:ot,version:nt,zrUtil:it,zrender:rt},Symbol.toStringTag,{value:"Module"}));tt([at,lt,dt,ct,ut]);const gs=({chartSeries:t})=>{const s={tooltip:{position:"top",backgroundColor:"rgba(50, 50, 50, 0.7)",textStyle:{color:"#FFFFFF"}},grid:{height:"50%",top:"10%"},xAxis:{type:"category",data:t[0].data.map((e=>e.x)),splitArea:{show:!1},axisLabel:{color:"#FFFFFF"},axisTick:{show:!1}},yAxis:{type:"category",data:t.map((e=>e.name)),splitArea:{show:!1},axisLabel:{color:"#FFFFFF"},axisTick:{show:!1}},visualMap:{min:0,max:100,calculable:!0,orient:"horizontal",left:"center",bottom:"15%",inRange:{color:["rgba(79, 89, 104, 0.5)",k.palette.custom.mainBlue]}},series:[{name:"",type:"heatmap",data:t.flatMap(((e,t)=>e.data.map(((e,s)=>[s,t,e.y])))),label:{show:!1,color:"#FFFFFF"},itemStyle:{borderColor:k.palette.primary.main,borderWidth:2,borderRadius:4},emphasis:{focus:"series",label:{show:!1},itemStyle:{shadowBlur:10,shadowColor:"rgba(0, 0, 0, 0.5)"}}}]};return e.jsx(fs,{echarts:xs,option:s,style:{height:350},opts:{renderer:"canvas"}})},ys={modes:{point:(e,t)=>bs(e,t,{intersect:!0}),nearest:(e,t,s)=>function(e,t,s){let o=Number.POSITIVE_INFINITY;return bs(e,t,s).reduce(((e,n)=>{const i=n.getCenterPoint(),r=function(e,t,s){if("x"===s)return{x:e.x,y:t.y};if("y"===s)return{x:t.x,y:e.y};return t}(t,i,s.axis),a=gt(t,r);return a<o?(e=[n],o=a):a===o&&e.push(n),e}),[]).sort(((e,t)=>e._index-t._index)).slice(0,1)}(e,t,s),x:(e,t,s)=>bs(e,t,{intersect:s.intersect,axis:"x"}),y:(e,t,s)=>bs(e,t,{intersect:s.intersect,axis:"y"})}};
/*!
* chartjs-plugin-annotation v3.1.0
* https://www.chartjs.org/chartjs-plugin-annotation/index
 * (c) 2024 chartjs-plugin-annotation Contributors
 * Released under the MIT License
 */function ms(e,t,s){return(ys.modes[s.mode]||ys.modes.nearest)(e,t,s)}function bs(e,t,s){return e.filter((e=>s.intersect?e.inRange(t.x,t.y):function(e,t,s){return"x"!==s&&"y"!==s?e.inRange(t.x,t.y,"x",!0)||e.inRange(t.x,t.y,"y",!0):e.inRange(t.x,t.y,s,!0)}(e,t,s.axis)))}function vs(e,t,s){const o=Math.cos(s),n=Math.sin(s),i=t.x,r=t.y;return{x:i+o*(e.x-i)-n*(e.y-r),y:r+n*(e.x-i)+o*(e.y-r)}}const js=(e,t)=>t>e||e.length>t.length&&e.slice(0,t.length)===t,Ss=.001,ws=(e,t,s)=>Math.min(s,Math.max(t,e)),Cs=(e,t)=>e.value>=e.start-t&&e.value<=e.end+t;function Ds(e,t,s){for(const o of Object.keys(e))e[o]=ws(e[o],t,s);return e}function ks(e,{x:t,y:s,x2:o,y2:n},i,{borderWidth:r,hitTolerance:a}){const l=(r+a)/2,d=e.x>=t-l-Ss&&e.x<=o+l+Ss,c=e.y>=s-l-Ss&&e.y<=n+l+Ss;return"x"===i?d:("y"===i||d)&&c}function Ms(e,{rect:t,center:s},o,{rotation:n,borderWidth:i,hitTolerance:r}){return ks(vs(e,s,xt(-n)),t,o,{borderWidth:i,hitTolerance:r})}function Ts(e,t){const{centerX:s,centerY:o}=e.getProps(["centerX","centerY"],t);return{x:s,y:o}}const Os=e=>"string"==typeof e&&e.endsWith("%"),Fs=e=>parseFloat(e)/100,As=e=>ws(Fs(e),0,1),_s=(e,t)=>({x:e,y:t,x2:e,y2:t,width:0,height:0}),Ps={box:e=>_s(e.centerX,e.centerY),doughnutLabel:e=>_s(e.centerX,e.centerY),ellipse:e=>({centerX:e.centerX,centerY:e.centerX,radius:0,width:0,height:0}),label:e=>_s(e.centerX,e.centerY),line:e=>_s(e.x,e.y),point:e=>({centerX:e.centerX,centerY:e.centerY,radius:0,width:0,height:0}),polygon:e=>_s(e.centerX,e.centerY)};function Vs(e,t){return"start"===t?0:"end"===t?e:Os(t)?As(t)*e:e/2}function zs(e,t,s=!0){return"number"==typeof t?t:Os(t)?(s?As(t):Fs(t))*e:e}function Is(e,t,{borderWidth:s,position:o,xAdjust:n,yAdjust:i},r){const a=ht(r),l=t.width+(a?r.width:0)+s,d=t.height+(a?r.height:0)+s,c=Es(o),u=Ys(e.x,l,n,c.x),h=Ys(e.y,d,i,c.y);return{x:u,y:h,x2:u+l,y2:h+d,width:l,height:d,centerX:u+l/2,centerY:h+d/2}}function Es(e,t="center"){return ht(e)?{x:Mt(e.x,t),y:Mt(e.y,t)}:{x:e=Mt(e,t),y:e}}const Ws=(e,t)=>e&&e.autoFit&&t<1;function Rs(e,t){const s=e.font,o=pt(s)?s:[s];return Ws(e,t)?o.map((function(e){const s=It(e);return s.size=Math.floor(e.size*t),s.lineHeight=e.lineHeight,It(s)})):o.map((e=>It(e)))}function Hs(e){return e&&(yt(e.xValue)||yt(e.yValue))}function Ys(e,t,s=0,o){return e-Vs(t,o)+s}function Xs(e,t,s){const o=s.init;if(o)return!0===o?Bs(t,s):function(e,t,s){const o=Ot(s.init,[{chart:e,properties:t,options:s}]);if(!0===o)return Bs(t,s);if(ht(o))return o}(e,t,s)}function Ns(e,t,s){let o=!1;return t.forEach((t=>{St(e[t])?(o=!0,s[t]=e[t]):yt(s[t])&&delete s[t]})),o}function Bs(e,t){const s=t.type||"line";return Ps[s](e)}const Ls=new Map;function $s(e){if(e&&"object"==typeof e){const t=e.toString();return"[object HTMLImageElement]"===t||"[object HTMLCanvasElement]"===t}}function qs(e,{x:t,y:s},o){o&&(e.translate(t,s),e.rotate(xt(o)),e.translate(-t,-s))}function Us(e,t){if(t&&t.borderWidth)return e.lineCap=t.borderCapStyle||"butt",e.setLineDash(t.borderDash),e.lineDashOffset=t.borderDashOffset,e.lineJoin=t.borderJoinStyle||"miter",e.lineWidth=t.borderWidth,e.strokeStyle=t.borderColor,!0}function Js(e,t){e.shadowColor=t.backgroundShadowColor,e.shadowBlur=t.shadowBlur,e.shadowOffsetX=t.shadowOffsetX,e.shadowOffsetY=t.shadowOffsetY}function Gs(e,t){const s=t.content;if($s(s)){return{width:zs(s.width,t.width),height:zs(s.height,t.height)}}const o=Rs(t),n=t.textStrokeWidth,i=pt(s)?s:[s],r=i.join()+(e=>e.reduce((function(e,t){return e+t.string}),""))(o)+n+(e._measureText?"-spriting":"");return Ls.has(r)||Ls.set(r,function(e,t,s,o){e.save();const n=t.length;let i=0,r=o;for(let a=0;a<n;a++){const n=s[Math.min(a,s.length-1)];e.font=n.string;const l=t[a];i=Math.max(i,e.measureText(l).width+o),r+=n.lineHeight}return e.restore(),{width:i,height:r}}(e,i,o,n)),Ls.get(r)}function Ks(e,t,s){const{x:o,y:n,width:i,height:r}=t;e.save(),Js(e,s);const a=Us(e,s);e.fillStyle=s.backgroundColor,e.beginPath(),Ct(e,{x:o,y:n,w:i,h:r,radius:Ds(Dt(s.borderRadius),0,Math.min(i,r)/2)}),e.closePath(),e.fill(),a&&(e.shadowColor=s.borderShadowColor,e.stroke()),e.restore()}function Qs(e,t,s,o){const n=s.content;if($s(n))return e.save(),e.globalAlpha=function(e,t){const s=zt(e)?e:t;return zt(s)?ws(s,0,1):1}(s.opacity,n.style.opacity),e.drawImage(n,t.x,t.y,t.width,t.height),void e.restore();const i=pt(n)?n:[n],r=Rs(s,o),a=s.color,l=pt(a)?a:[a],d=function(e,t){const{x:s,width:o}=e,n=t.textAlign;return"center"===n?s+o/2:"end"===n||"right"===n?s+o:s}(t,s),c=t.y+s.textStrokeWidth/2;e.save(),e.textBaseline="middle",e.textAlign=s.textAlign,function(e,t){if(t.textStrokeWidth>0)return e.lineJoin="round",e.miterLimit=2,e.lineWidth=t.textStrokeWidth,e.strokeStyle=t.textStrokeColor,!0}(e,s)&&function(e,{x:t,y:s},o,n){e.beginPath();let i=0;o.forEach((function(o,r){const a=n[Math.min(r,n.length-1)],l=a.lineHeight;e.font=a.string,e.strokeText(o,t,s+l/2+i),i+=l})),e.stroke()}(e,{x:d,y:c},i,r),function(e,{x:t,y:s},o,{fonts:n,colors:i}){let r=0;o.forEach((function(o,a){const l=i[Math.min(a,i.length-1)],d=n[Math.min(a,n.length-1)],c=d.lineHeight;e.beginPath(),e.font=d.string,e.fillStyle=l,e.fillText(o,t,s+c/2+r),r+=c,e.fill()}))}(e,{x:d,y:c},i,{fonts:r,colors:l}),e.restore()}function Zs(e,t,s,o){const{radius:n,options:i}=t,r=i.pointStyle,a=i.rotation;let l=(a||0)*wt;if($s(r))return e.save(),e.translate(s,o),e.rotate(l),e.drawImage(r,-r.width/2,-r.height/2,r.width,r.height),void e.restore();(e=>isNaN(e)||e<=0)(n)||function(e,{x:t,y:s,radius:o,rotation:n,style:i,rad:r}){let a,l,d,c;switch(e.beginPath(),i){default:e.arc(t,s,o,0,Pt),e.closePath();break;case"triangle":e.moveTo(t+Math.sin(r)*o,s-Math.cos(r)*o),r+=_t,e.lineTo(t+Math.sin(r)*o,s-Math.cos(r)*o),r+=_t,e.lineTo(t+Math.sin(r)*o,s-Math.cos(r)*o),e.closePath();break;case"rectRounded":c=.516*o,d=o-c,a=Math.cos(r+Ft)*d,l=Math.sin(r+Ft)*d,e.arc(t-a,s-l,c,r-bt,r-At),e.arc(t+l,s-a,c,r-At,r),e.arc(t+a,s+l,c,r,r+At),e.arc(t-l,s+a,c,r+At,r+bt),e.closePath();break;case"rect":if(!n){d=Math.SQRT1_2*o,e.rect(t-d,s-d,2*d,2*d);break}r+=Ft;case"rectRot":a=Math.cos(r)*o,l=Math.sin(r)*o,e.moveTo(t-a,s-l),e.lineTo(t+l,s-a),e.lineTo(t+a,s+l),e.lineTo(t-l,s+a),e.closePath();break;case"crossRot":r+=Ft;case"cross":a=Math.cos(r)*o,l=Math.sin(r)*o,e.moveTo(t-a,s-l),e.lineTo(t+a,s+l),e.moveTo(t+l,s-a),e.lineTo(t-l,s+a);break;case"star":a=Math.cos(r)*o,l=Math.sin(r)*o,e.moveTo(t-a,s-l),e.lineTo(t+a,s+l),e.moveTo(t+l,s-a),e.lineTo(t-l,s+a),r+=Ft,a=Math.cos(r)*o,l=Math.sin(r)*o,e.moveTo(t-a,s-l),e.lineTo(t+a,s+l),e.moveTo(t+l,s-a),e.lineTo(t-l,s+a);break;case"line":a=Math.cos(r)*o,l=Math.sin(r)*o,e.moveTo(t-a,s-l),e.lineTo(t+a,s+l);break;case"dash":e.moveTo(t,s),e.lineTo(t+Math.cos(r)*o,s+Math.sin(r)*o)}e.fill()}(e,{x:s,y:o,radius:n,rotation:a,style:r,rad:l})}const eo=["left","bottom","top","right"];function to(e,t){const{pointX:s,pointY:o,options:n}=t,i=n.callout,r=i&&i.display&&function(e,t){const s=t.position;if(eo.includes(s))return s;return function(e,t){const{x:s,y:o,x2:n,y2:i,width:r,height:a,pointX:l,pointY:d,centerX:c,centerY:u,rotation:h}=e,p={x:c,y:u},f=t.start,x=zs(r,f),g=zs(a,f),y=[s,s+x,s+x,n],m=[o+g,i,o,i],b=[];for(let v=0;v<4;v++){const e=vs({x:y[v],y:m[v]},p,xt(h));b.push({position:eo[v],distance:gt(e,{x:l,y:d})})}return b.sort(((e,t)=>e.distance-t.distance))[0].position}(e,t)}(t,i);if(!r||function(e,t,s){const{pointX:o,pointY:n}=e,i=t.margin;let r=o,a=n;"left"===s?r+=i:"right"===s?r-=i:"top"===s?a+=i:"bottom"===s&&(a-=i);return e.inRange(r,a)}(t,i,r))return;e.save(),e.beginPath();if(!Us(e,i))return e.restore();const{separatorStart:a,separatorEnd:l}=function(e,t){const{x:s,y:o,x2:n,y2:i}=e,r=function(e,t){const{width:s,height:o,options:n}=e,i=n.callout.margin+n.borderWidth/2;if("right"===t)return s+i;if("bottom"===t)return o+i;return-i}(e,t);let a,l;"left"===t||"right"===t?(a={x:s+r,y:o},l={x:a.x,y:i}):(a={x:s,y:o+r},l={x:n,y:a.y});return{separatorStart:a,separatorEnd:l}}(t,r),{sideStart:d,sideEnd:c}=function(e,t,s){const{y:o,width:n,height:i,options:r}=e,a=r.callout.start,l=function(e,t){const s=t.side;if("left"===e||"top"===e)return-s;return s}(t,r.callout);let d,c;"left"===t||"right"===t?(d={x:s.x,y:o+zs(i,a)},c={x:d.x+l,y:d.y}):(d={x:s.x+zs(n,a),y:s.y},c={x:d.x,y:d.y+l});return{sideStart:d,sideEnd:c}}(t,r,a);(i.margin>0||0===n.borderWidth)&&(e.moveTo(a.x,a.y),e.lineTo(l.x,l.y)),e.moveTo(d.x,d.y),e.lineTo(c.x,c.y);const u=vs({x:s,y:o},t.getCenterPoint(),xt(-t.rotation));e.lineTo(u.x,u.y),e.stroke(),e.restore()}const so={xScaleID:{min:"xMin",max:"xMax",start:"left",end:"right",startProp:"x",endProp:"x2"},yScaleID:{min:"yMin",max:"yMax",start:"bottom",end:"top",startProp:"y",endProp:"y2"}};function oo(e,t,s){return t="number"==typeof t?t:e.parse(t),Tt(t)?e.getPixelForValue(t):s}function no(e,t,s){const o=t[s];if(o||"scaleID"===s)return o;const n=s.charAt(0),i=Object.values(e).filter((e=>e.axis&&e.axis===n));return i.length?i[0].id:n}function io(e,t){if(e){const s=e.options.reverse;return{start:oo(e,t.min,s?t.end:t.start),end:oo(e,t.max,s?t.start:t.end)}}}function ro(e,t){const{chartArea:s,scales:o}=e,n=o[no(o,t,"xScaleID")],i=o[no(o,t,"yScaleID")];let r=s.width/2,a=s.height/2;return n&&(r=oo(n,t.xValue,n.left+n.width/2)),i&&(a=oo(i,t.yValue,i.top+i.height/2)),{x:r,y:a}}function ao(e,t){const s=e.scales,o=s[no(s,t,"xScaleID")],n=s[no(s,t,"yScaleID")];if(!o&&!n)return{};let{left:i,right:r}=o||e.chartArea,{top:a,bottom:l}=n||e.chartArea;const d=ho(o,{min:t.xMin,max:t.xMax,start:i,end:r});i=d.start,r=d.end;const c=ho(n,{min:t.yMin,max:t.yMax,start:l,end:a});return a=c.start,l=c.end,{x:i,y:a,x2:r,y2:l,width:r-i,height:l-a,centerX:i+(r-i)/2,centerY:a+(l-a)/2}}function lo(e,t){if(!Hs(t)){const s=ao(e,t);let o=t.radius;o&&!isNaN(o)||(o=Math.min(s.width,s.height)/2,t.radius=o);const n=2*o,i=s.centerX+t.xAdjust,r=s.centerY+t.yAdjust;return{x:i-o,y:r-o,x2:i+o,y2:r+o,centerX:i,centerY:r,width:n,height:n,radius:o}}return function(e,t){const s=ro(e,t),o=2*t.radius;return{x:s.x-t.radius+t.xAdjust,y:s.y-t.radius+t.yAdjust,x2:s.x+t.radius+t.xAdjust,y2:s.y+t.radius+t.yAdjust,centerX:s.x+t.xAdjust,centerY:s.y+t.yAdjust,radius:t.radius,width:o,height:o}}(e,t)}function co(e,t){const{scales:s,chartArea:o}=e,n=s[t.scaleID],i={x:o.left,y:o.top,x2:o.right,y2:o.bottom};return n?function(e,t,s){const o=oo(e,s.value,NaN),n=oo(e,s.endValue,o);e.isHorizontal()?(t.x=o,t.x2=n):(t.y=o,t.y2=n)}(n,i,t):function(e,t,s){for(const o of Object.keys(so)){const n=e[no(e,s,o)];if(n){const{min:e,max:i,start:r,end:a,startProp:l,endProp:d}=so[o],c=io(n,{min:s[e],max:s[i],start:n[r],end:n[a]});t[l]=c.start,t[d]=c.end}}}(s,i,t),i}function uo(e,t){const s=ao(e,t);return s.initProperties=Xs(e,s,t),s.elements=[{type:"label",optionScope:"label",properties:fo(e,s,t),initProperties:s.initProperties}],s}function ho(e,t){const s=io(e,t)||t;return{start:Math.min(s.start,s.end),end:Math.max(s.start,s.end)}}function po(e,t){const{start:s,end:o,borderWidth:n}=e,{position:i,padding:{start:r,end:a},adjust:l}=t;return s+n/2+l+Vs(o-n-s-r-a-t.size,i)}function fo(e,t,s){const o=s.label;o.backgroundColor="transparent",o.callout.display=!1;const n=Es(o.position),i=mt(o.padding),r=Gs(e.ctx,o),a=function({properties:e,options:t},s,o,n){const{x:i,x2:r,width:a}=e;return po({start:i,end:r,borderWidth:t.borderWidth},{position:o.x,padding:{start:n.left,end:n.right},adjust:t.label.xAdjust,size:s.width})}({properties:t,options:s},r,n,i),l=function({properties:e,options:t},s,o,n){const{y:i,y2:r,height:a}=e;return po({start:i,end:r,borderWidth:t.borderWidth},{position:o.y,padding:{start:n.top,end:n.bottom},adjust:t.label.yAdjust,size:s.height})}({properties:t,options:s},r,n,i),d=r.width+i.width,c=r.height+i.height;return{x:a,y:l,x2:a+d,y2:l+c,width:d,height:c,centerX:a+d/2,centerY:l+c/2,rotation:o.rotation}}const xo=["enter","leave"],go=xo.concat("click");function yo(e,t,s){if(e.listened)switch(t.type){case"mousemove":case"mouseout":return function(e,t,s){if(!e.moveListened)return;let o;o="mousemove"===t.type?ms(e.visibleElements,t,s.interaction):[];const n=e.hovered;e.hovered=o;const i={state:e,event:t};let r=mo(i,"leave",n,o);return mo(i,"enter",o,n)||r}(e,t,s);case"click":return function(e,t,s){const o=e.listeners,n=ms(e.visibleElements,t,s.interaction);let i;for(const r of n)i=bo(r.options.click||o.click,r,t)||i;return i}(e,t,s)}}function mo({state:e,event:t},s,o,n){let i;for(const r of o)n.indexOf(r)<0&&(i=bo(r.options[s]||e.listeners[s],r,t)||i);return i}function bo(e,t,s){return!0===Ot(e,[t.$context,s])}const vo=["afterDraw","beforeDraw"];function jo(e,t,s){if(e.hooked){const o=t.options[s]||e.hooks[s];return Ot(o,[t.$context])}}function So(e,t,s){const o=function(e,t,s){const o=t.axis,n=t.id,i=o+"ScaleID",r={min:Mt(t.min,Number.NEGATIVE_INFINITY),max:Mt(t.max,Number.POSITIVE_INFINITY)};for(const a of s)a.scaleID===n?ko(a,t,["value","endValue"],r):no(e,a,i)===n&&ko(a,t,[o+"Min",o+"Max",o+"Value"],r);return r}(e.scales,t,s);let n=wo(t,o,"min","suggestedMin");n=wo(t,o,"max","suggestedMax")||n,n&&St(t.handleTickRangeOptions)&&t.handleTickRangeOptions()}function wo(e,t,s,o){if(Tt(t[s])&&!function(e,t,s){return yt(e[t])||yt(e[s])}(e.options,s,o)){const o=e[s]!==t[s];return e[s]=t[s],o}}function Co(e,t){for(const s of["scaleID","xScaleID","yScaleID"]){const o=no(t,e,s);o&&!t[o]&&Do(e,s)}}function Do(e,t){if("scaleID"===t)return!0;const s=t.charAt(0);for(const o of["Min","Max","Value"])if(yt(e[s+o]))return!0;return!1}function ko(e,t,s,o){for(const n of s){const s=e[n];if(yt(s)){const e=t.parse(s);o.min=Math.min(o.min,e),o.max=Math.max(o.max,e)}}}class Mo extends ft{inRange(e,t,s,o){const{x:n,y:i}=vs({x:e,y:t},this.getCenterPoint(o),xt(-this.options.rotation));return ks({x:n,y:i},this.getProps(["x","y","x2","y2"],o),s,this.options)}getCenterPoint(e){return Ts(this,e)}draw(e){e.save(),qs(e,this.getCenterPoint(),this.options.rotation),Ks(e,this,this.options),e.restore()}get label(){return this.elements&&this.elements[0]}resolveElementProperties(e,t){return uo(e,t)}}Mo.id="boxAnnotation",Mo.defaults={adjustScaleRange:!0,backgroundShadowColor:"transparent",borderCapStyle:"butt",borderDash:[],borderDashOffset:0,borderJoinStyle:"miter",borderRadius:0,borderShadowColor:"transparent",borderWidth:1,display:!0,init:void 0,hitTolerance:0,label:{backgroundColor:"transparent",borderWidth:0,callout:{display:!1},color:"black",content:null,display:!1,drawTime:void 0,font:{family:void 0,lineHeight:void 0,size:void 0,style:void 0,weight:"bold"},height:void 0,hitTolerance:void 0,opacity:void 0,padding:6,position:"center",rotation:void 0,textAlign:"start",textStrokeColor:void 0,textStrokeWidth:0,width:void 0,xAdjust:0,yAdjust:0,z:void 0},rotation:0,shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,xMax:void 0,xMin:void 0,xScaleID:void 0,yMax:void 0,yMin:void 0,yScaleID:void 0,z:0},Mo.defaultRoutes={borderColor:"color",backgroundColor:"color"},Mo.descriptors={label:{_fallback:!0}};class To extends ft{inRange(e,t,s,o){return Ms({x:e,y:t},{rect:this.getProps(["x","y","x2","y2"],o),center:this.getCenterPoint(o)},s,{rotation:this.rotation,borderWidth:0,hitTolerance:this.options.hitTolerance})}getCenterPoint(e){return Ts(this,e)}draw(e){const t=this.options;t.display&&t.content&&(!function(e,t){const{_centerX:s,_centerY:o,_radius:n,_startAngle:i,_endAngle:r,_counterclockwise:a,options:l}=t;e.save();const d=Us(e,l);e.fillStyle=l.backgroundColor,e.beginPath(),e.arc(s,o,n,i,r,a),e.closePath(),e.fill(),d&&e.stroke();e.restore()}(e,this),e.save(),qs(e,this.getCenterPoint(),this.rotation),Qs(e,this,t,this._fitRatio),e.restore())}resolveElementProperties(e,t){const s=function(e,t){return e.getSortedVisibleDatasetMetas().reduce((function(s,o){const n=o.controller;return n instanceof $&&function(e,t,s){if(!t.autoHide)return!0;for(let o=0;o<s.length;o++)if(!s[o].hidden&&e.getDataVisibility(o))return!0}(e,t,o.data)&&(!s||n.innerRadius<s.controller.innerRadius)&&n.options.circumference>=90?o:s}),void 0)}(e,t);if(!s)return{};const{controllerMeta:o,point:n,radius:i}=function({chartArea:e},t,s){const{left:o,top:n,right:i,bottom:r}=e,{innerRadius:a,offsetX:l,offsetY:d}=s.controller,c=(o+i)/2+l,u=(n+r)/2+d,h={left:Math.max(c-a,o),right:Math.min(c+a,i),top:Math.max(u-a,n),bottom:Math.min(u+a,r)},p={x:(h.left+h.right)/2,y:(h.top+h.bottom)/2},f=t.spacing+t.borderWidth/2,x=a-f,g=p.y>u,y=function(e,t,s,o){const n=Math.pow(s-e,2),i=Math.pow(o,2),r=-2*t,a=Math.pow(t,2)+n-i,l=Math.pow(r,2)-4*a;if(l<=0)return{_startAngle:0,_endAngle:Pt};const d=(-r-Math.sqrt(l))/2,c=(-r+Math.sqrt(l))/2;return{_startAngle:Et({x:t,y:s},{x:d,y:e}).angle,_endAngle:Et({x:t,y:s},{x:c,y:e}).angle}}(g?n+f:r-f,c,u,x),m={_centerX:c,_centerY:u,_radius:x,_counterclockwise:g,...y};return{controllerMeta:m,point:p,radius:Math.min(a,Math.min(h.right-h.left,h.bottom-h.top)/2)}}(e,t,s);let r=Gs(e.ctx,t);const a=function({width:e,height:t},s){const o=Math.sqrt(Math.pow(e,2)+Math.pow(t,2));return 2*s/o}(r,i);Ws(t,a)&&(r={width:r.width*a,height:r.height*a});const{position:l,xAdjust:d,yAdjust:c}=t,u=Is(n,r,{borderWidth:0,position:l,xAdjust:d,yAdjust:c});return{initProperties:Xs(e,u,t),...u,...o,rotation:t.rotation,_fitRatio:a}}}To.id="doughnutLabelAnnotation",To.defaults={autoFit:!0,autoHide:!0,backgroundColor:"transparent",backgroundShadowColor:"transparent",borderColor:"transparent",borderDash:[],borderDashOffset:0,borderJoinStyle:"miter",borderShadowColor:"transparent",borderWidth:0,color:"black",content:null,display:!0,font:{family:void 0,lineHeight:void 0,size:void 0,style:void 0,weight:void 0},height:void 0,hitTolerance:0,init:void 0,opacity:void 0,position:"center",rotation:0,shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,spacing:1,textAlign:"center",textStrokeColor:void 0,textStrokeWidth:0,width:void 0,xAdjust:0,yAdjust:0},To.defaultRoutes={};class Oo extends ft{inRange(e,t,s,o){return Ms({x:e,y:t},{rect:this.getProps(["x","y","x2","y2"],o),center:this.getCenterPoint(o)},s,{rotation:this.rotation,borderWidth:this.options.borderWidth,hitTolerance:this.options.hitTolerance})}getCenterPoint(e){return Ts(this,e)}draw(e){const t=this.options,s=!yt(this._visible)||this._visible;t.display&&t.content&&s&&(e.save(),qs(e,this.getCenterPoint(),this.rotation),to(e,this),Ks(e,this,t),Qs(e,function({x:e,y:t,width:s,height:o,options:n}){const i=n.borderWidth/2,r=mt(n.padding);return{x:e+r.left+i,y:t+r.top+i,width:s-r.left-r.right-n.borderWidth,height:o-r.top-r.bottom-n.borderWidth}}(this),t),e.restore())}resolveElementProperties(e,t){let s;if(Hs(t))s=ro(e,t);else{const{centerX:o,centerY:n}=ao(e,t);s={x:o,y:n}}const o=mt(t.padding),n=Is(s,Gs(e.ctx,t),t,o);return{initProperties:Xs(e,n,t),pointX:s.x,pointY:s.y,...n,rotation:t.rotation}}}Oo.id="labelAnnotation",Oo.defaults={adjustScaleRange:!0,backgroundColor:"transparent",backgroundShadowColor:"transparent",borderCapStyle:"butt",borderDash:[],borderDashOffset:0,borderJoinStyle:"miter",borderRadius:0,borderShadowColor:"transparent",borderWidth:0,callout:{borderCapStyle:"butt",borderColor:void 0,borderDash:[],borderDashOffset:0,borderJoinStyle:"miter",borderWidth:1,display:!1,margin:5,position:"auto",side:5,start:"50%"},color:"black",content:null,display:!0,font:{family:void 0,lineHeight:void 0,size:void 0,style:void 0,weight:void 0},height:void 0,hitTolerance:0,init:void 0,opacity:void 0,padding:6,position:"center",rotation:0,shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,textAlign:"center",textStrokeColor:void 0,textStrokeWidth:0,width:void 0,xAdjust:0,xMax:void 0,xMin:void 0,xScaleID:void 0,xValue:void 0,yAdjust:0,yMax:void 0,yMin:void 0,yScaleID:void 0,yValue:void 0,z:0},Oo.defaultRoutes={borderColor:"color"};const Fo=(e,t,s)=>({x:e.x+s*(t.x-e.x),y:e.y+s*(t.y-e.y)}),Ao=(e,t,s)=>Fo(t,s,Math.abs((e-t.y)/(s.y-t.y))).x,_o=(e,t,s)=>Fo(t,s,Math.abs((e-t.x)/(s.x-t.x))).y,Po=e=>e*e,Vo=(e,t,s,o)=>(1-o)*(1-o)*e+2*(1-o)*o*t+o*o*s,zo=(e,t,s,o)=>({x:Vo(e.x,t.x,s.x,o),y:Vo(e.y,t.y,s.y,o)}),Io=(e,t,s,o)=>2*(1-o)*(t-e)+2*o*(s-t),Eo=(e,t,s,o)=>-Math.atan2(Io(e.x,t.x,s.x,o),Io(e.y,t.y,s.y,o))+.5*bt;class Wo extends ft{inRange(e,t,s,o){const n=(this.options.borderWidth+this.options.hitTolerance)/2;if("x"!==s&&"y"!==s){const s={mouseX:e,mouseY:t},{path:i,ctx:r}=this;if(i){Us(r,this.options),r.lineWidth+=this.options.hitTolerance;const{chart:n}=this.$context,a=e*n.currentDevicePixelRatio,l=t*n.currentDevicePixelRatio,d=r.isPointInStroke(i,a,l)||Yo(this,s,o);return r.restore(),d}return function(e,{mouseX:t,mouseY:s},o=.001,n){const{x:i,y:r,x2:a,y2:l}=e.getProps(["x","y","x2","y2"],n),d=a-i,c=l-r,u=Po(d)+Po(c),h=0===u?-1:((t-i)*d+(s-r)*c)/u;let p,f;h<0?(p=i,f=r):h>1?(p=a,f=l):(p=i+h*d,f=r+h*c);return Po(t-p)+Po(s-f)<=o}(this,s,Po(n),o)||Yo(this,s,o)}return function(e,{mouseX:t,mouseY:s},o,{hitSize:n,useFinalPosition:i}){const r=((e,t,{x:s,y:o,x2:n,y2:i},r)=>"y"===r?{start:Math.min(o,i),end:Math.max(o,i),value:t}:{start:Math.min(s,n),end:Math.max(s,n),value:e})(t,s,e.getProps(["x","y","x2","y2"],i),o);return Cs(r,n)||Yo(e,{mouseX:t,mouseY:s},i,o)}(this,{mouseX:e,mouseY:t},s,{hitSize:n,useFinalPosition:o})}getCenterPoint(e){return Ts(this,e)}draw(e){const{x:t,y:s,x2:o,y2:n,cp:i,options:r}=this;if(e.save(),!Us(e,r))return e.restore();Js(e,r);const a=Math.sqrt(Math.pow(o-t,2)+Math.pow(n-s,2));if(r.curve&&i)return function(e,t,s,o){const{x:n,y:i,x2:r,y2:a,options:l}=t,{startOpts:d,endOpts:c,startAdjust:u,endAdjust:h}=Bo(t),p={x:n,y:i},f={x:r,y:a},x=Eo(p,s,f,0),g=Eo(p,s,f,1)-bt,y=zo(p,s,f,u/o),m=zo(p,s,f,1-h/o),b=new Path2D;e.beginPath(),b.moveTo(y.x,y.y),b.quadraticCurveTo(s.x,s.y,m.x,m.y),e.shadowColor=l.borderShadowColor,e.stroke(b),t.path=b,t.ctx=e,qo(e,y,{angle:x,adjust:u},d),qo(e,m,{angle:g,adjust:h},c)}(e,this,i,a),e.restore();const{startOpts:l,endOpts:d,startAdjust:c,endAdjust:u}=Bo(this),h=Math.atan2(n-s,o-t);e.translate(t,s),e.rotate(h),e.beginPath(),e.moveTo(0+c,0),e.lineTo(a-u,0),e.shadowColor=r.borderShadowColor,e.stroke(),$o(e,0,c,l),$o(e,a,-u,d),e.restore()}get label(){return this.elements&&this.elements[0]}resolveElementProperties(e,t){const s=co(e,t),{x:o,y:n,x2:i,y2:r}=s,a=function({x:e,y:t,x2:s,y2:o},{top:n,right:i,bottom:r,left:a}){return!(e<a&&s<a||e>i&&s>i||t<n&&o<n||t>r&&o>r)}(s,e.chartArea),l=a?function(e,t,s){const{x:o,y:n}=Ho(e,t,s),{x:i,y:r}=Ho(t,e,s);return{x:o,y:n,x2:i,y2:r,width:Math.abs(i-o),height:Math.abs(r-n)}}({x:o,y:n},{x:i,y:r},e.chartArea):{x:o,y:n,x2:i,y2:r,width:Math.abs(i-o),height:Math.abs(r-n)};if(l.centerX=(i+o)/2,l.centerY=(r+n)/2,l.initProperties=Xs(e,l,t),t.curve){const e={x:l.x,y:l.y},s={x:l.x2,y:l.y2};l.cp=function(e,t,s){const{x:o,y:n,x2:i,y2:r,centerX:a,centerY:l}=e,d=Math.atan2(r-n,i-o),c=Es(t.controlPoint,0);return vs({x:a+zs(s,c.x,!1),y:l+zs(s,c.y,!1)},{x:a,y:l},d)}(l,t,gt(e,s))}const d=function(e,t,s){const o=s.borderWidth,n=mt(s.padding),i=Gs(e.ctx,s),r=i.width+n.width+o,a=i.height+n.height+o;return function(e,t,s,o){const{width:n,height:i,padding:r}=s,{xAdjust:a,yAdjust:l}=t,d={x:e.x,y:e.y},c={x:e.x2,y:e.y2},u="auto"===t.rotation?function(e){const{x:t,y:s,x2:o,y2:n}=e,i=Math.atan2(n-s,o-t);return i>bt/2?i-bt:i<bt/-2?i+bt:i}(e):xt(t.rotation),h=function(e,t,s){const o=Math.cos(s),n=Math.sin(s);return{w:Math.abs(e*o)+Math.abs(t*n),h:Math.abs(e*n)+Math.abs(t*o)}}(n,i,u),p=function(e,t,s,o){let n;const i=function(e,t){const{x:s,x2:o,y:n,y2:i}=e,r=Math.min(n,i)-t.top,a=Math.min(s,o)-t.left,l=t.bottom-Math.max(n,i),d=t.right-Math.max(s,o);return{x:Math.min(a,d),y:Math.min(r,l),dx:a<=d?1:-1,dy:r<=l?1:-1}}(e,o);n="start"===t.position?Xo({w:e.x2-e.x,h:e.y2-e.y},s,t,i):"end"===t.position?1-Xo({w:e.x-e.x2,h:e.y-e.y2},s,t,i):Vs(1,t.position);return n}(e,t,{labelSize:h,padding:r},o),f=e.cp?zo(d,e.cp,c,p):Fo(d,c,p),x={size:h.w,min:o.left,max:o.right,padding:r.left},g={size:h.h,min:o.top,max:o.bottom,padding:r.top},y=No(f.x,x)+a,m=No(f.y,g)+l;return{x:y-n/2,y:m-i/2,x2:y+n/2,y2:m+i/2,centerX:y,centerY:m,pointX:f.x,pointY:f.y,width:n,height:i,rotation:Vt(u)}}(t,s,{width:r,height:a,padding:n},e.chartArea)}(e,l,t.label);return d._visible=a,l.elements=[{type:"label",optionScope:"label",properties:d,initProperties:l.initProperties}],l}}Wo.id="lineAnnotation";const Ro={backgroundColor:void 0,backgroundShadowColor:void 0,borderColor:void 0,borderDash:void 0,borderDashOffset:void 0,borderShadowColor:void 0,borderWidth:void 0,display:void 0,fill:void 0,length:void 0,shadowBlur:void 0,shadowOffsetX:void 0,shadowOffsetY:void 0,width:void 0};function Ho({x:e,y:t},s,{top:o,right:n,bottom:i,left:r}){return e<r&&(t=_o(r,{x:e,y:t},s),e=r),e>n&&(t=_o(n,{x:e,y:t},s),e=n),t<o&&(e=Ao(o,{x:e,y:t},s),t=o),t>i&&(e=Ao(i,{x:e,y:t},s),t=i),{x:e,y:t}}function Yo(e,{mouseX:t,mouseY:s},o,n){const i=e.label;return i.options.display&&i.inRange(t,s,n,o)}function Xo(e,t,s,o){const{labelSize:n,padding:i}=t,r=e.w*o.dx,a=e.h*o.dy,l=r>0&&(n.w/2+i.left-o.x)/r,d=a>0&&(n.h/2+i.top-o.y)/a;return ws(Math.max(l,d),0,.25)}function No(e,t){const{size:s,min:o,max:n,padding:i}=t,r=s/2;return s>n-o?(n+o)/2:(o>=e-i-r&&(e=o+i+r),n<=e+i+r&&(e=n-i-r),e)}function Bo(e){const t=e.options,s=t.arrowHeads&&t.arrowHeads.start,o=t.arrowHeads&&t.arrowHeads.end;return{startOpts:s,endOpts:o,startAdjust:Lo(e,s),endAdjust:Lo(e,o)}}function Lo(e,t){if(!t||!t.display)return 0;const{length:s,width:o}=t,n=e.options.borderWidth/2,i={x:s,y:o+n},r={x:0,y:n};return Math.abs(Ao(0,i,r))}function $o(e,t,s,o){if(!o||!o.display)return;const{length:n,width:i,fill:r,backgroundColor:a,borderColor:l}=o,d=Math.abs(t-n)+s;e.beginPath(),Js(e,o),Us(e,o),e.moveTo(d,-i),e.lineTo(t+s,0),e.lineTo(d,i),!0===r?(e.fillStyle=a||l,e.closePath(),e.fill(),e.shadowColor="transparent"):e.shadowColor=o.borderShadowColor,e.stroke()}function qo(e,{x:t,y:s},{angle:o,adjust:n},i){i&&i.display&&(e.save(),e.translate(t,s),e.rotate(o),$o(e,0,-n,i),e.restore())}Wo.defaults={adjustScaleRange:!0,arrowHeads:{display:!1,end:Object.assign({},Ro),fill:!1,length:12,start:Object.assign({},Ro),width:6},borderDash:[],borderDashOffset:0,borderShadowColor:"transparent",borderWidth:2,curve:!1,controlPoint:{y:"-50%"},display:!0,endValue:void 0,init:void 0,hitTolerance:0,label:{backgroundColor:"rgba(0,0,0,0.8)",backgroundShadowColor:"transparent",borderCapStyle:"butt",borderColor:"black",borderDash:[],borderDashOffset:0,borderJoinStyle:"miter",borderRadius:6,borderShadowColor:"transparent",borderWidth:0,callout:Object.assign({},Oo.defaults.callout),color:"#fff",content:null,display:!1,drawTime:void 0,font:{family:void 0,lineHeight:void 0,size:void 0,style:void 0,weight:"bold"},height:void 0,hitTolerance:void 0,opacity:void 0,padding:6,position:"center",rotation:0,shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,textAlign:"center",textStrokeColor:void 0,textStrokeWidth:0,width:void 0,xAdjust:0,yAdjust:0,z:void 0},scaleID:void 0,shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,value:void 0,xMax:void 0,xMin:void 0,xScaleID:void 0,yMax:void 0,yMin:void 0,yScaleID:void 0,z:0},Wo.descriptors={arrowHeads:{start:{_fallback:!0},end:{_fallback:!0},_fallback:!0}},Wo.defaultRoutes={borderColor:"color"};class Uo extends ft{inRange(e,t,s,o){const n=this.options.rotation,i=(this.options.borderWidth+this.options.hitTolerance)/2;if("x"!==s&&"y"!==s)return function(e,t,s,o){const{width:n,height:i,centerX:r,centerY:a}=t,l=n/2,d=i/2;if(l<=0||d<=0)return!1;const c=xt(s||0),u=Math.cos(c),h=Math.sin(c),p=Math.pow(u*(e.x-r)+h*(e.y-a),2),f=Math.pow(h*(e.x-r)-u*(e.y-a),2);return p/Math.pow(l+o,2)+f/Math.pow(d+o,2)<=1.0001}({x:e,y:t},this.getProps(["width","height","centerX","centerY"],o),n,i);const{x:r,y:a,x2:l,y2:d}=this.getProps(["x","y","x2","y2"],o),c="y"===s?{start:a,end:d}:{start:r,end:l},u=vs({x:e,y:t},this.getCenterPoint(o),xt(-n));return u[s]>=c.start-i-Ss&&u[s]<=c.end+i+Ss}getCenterPoint(e){return Ts(this,e)}draw(e){const{width:t,height:s,centerX:o,centerY:n,options:i}=this;e.save(),qs(e,this.getCenterPoint(),i.rotation),Js(e,this.options),e.beginPath(),e.fillStyle=i.backgroundColor;const r=Us(e,i);e.ellipse(o,n,s/2,t/2,bt/2,0,2*bt),e.fill(),r&&(e.shadowColor=i.borderShadowColor,e.stroke()),e.restore()}get label(){return this.elements&&this.elements[0]}resolveElementProperties(e,t){return uo(e,t)}}Uo.id="ellipseAnnotation",Uo.defaults={adjustScaleRange:!0,backgroundShadowColor:"transparent",borderDash:[],borderDashOffset:0,borderShadowColor:"transparent",borderWidth:1,display:!0,hitTolerance:0,init:void 0,label:Object.assign({},Mo.defaults.label),rotation:0,shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,xMax:void 0,xMin:void 0,xScaleID:void 0,yMax:void 0,yMin:void 0,yScaleID:void 0,z:0},Uo.defaultRoutes={borderColor:"color",backgroundColor:"color"},Uo.descriptors={label:{_fallback:!0}};class Jo extends ft{inRange(e,t,s,o){const{x:n,y:i,x2:r,y2:a,width:l}=this.getProps(["x","y","x2","y2","width"],o),d=(this.options.borderWidth+this.options.hitTolerance)/2;if("x"!==s&&"y"!==s)return function(e,t,s,o){return!(!e||!t||s<=0)&&Math.pow(e.x-t.x,2)+Math.pow(e.y-t.y,2)<=Math.pow(s+o,2)}({x:e,y:t},this.getCenterPoint(o),l/2,d);return Cs("y"===s?{start:i,end:a,value:t}:{start:n,end:r,value:e},d)}getCenterPoint(e){return Ts(this,e)}draw(e){const t=this.options,s=t.borderWidth;if(t.radius<.1)return;e.save(),e.fillStyle=t.backgroundColor,Js(e,t);const o=Us(e,t);Zs(e,this,this.centerX,this.centerY),o&&!$s(t.pointStyle)&&(e.shadowColor=t.borderShadowColor,e.stroke()),e.restore(),t.borderWidth=s}resolveElementProperties(e,t){const s=lo(e,t);return s.initProperties=Xs(e,s,t),s}}Jo.id="pointAnnotation",Jo.defaults={adjustScaleRange:!0,backgroundShadowColor:"transparent",borderDash:[],borderDashOffset:0,borderShadowColor:"transparent",borderWidth:1,display:!0,hitTolerance:0,init:void 0,pointStyle:"circle",radius:10,rotation:0,shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,xAdjust:0,xMax:void 0,xMin:void 0,xScaleID:void 0,xValue:void 0,yAdjust:0,yMax:void 0,yMin:void 0,yScaleID:void 0,yValue:void 0,z:0},Jo.defaultRoutes={borderColor:"color",backgroundColor:"color"};class Go extends ft{inRange(e,t,s,o){if("x"!==s&&"y"!==s)return this.options.radius>=.1&&this.elements.length>1&&function(e,t,s,o){let n=!1,i=e[e.length-1].getProps(["bX","bY"],o);for(const r of e){const e=r.getProps(["bX","bY"],o);e.bY>s!=i.bY>s&&t<(i.bX-e.bX)*(s-e.bY)/(i.bY-e.bY)+e.bX&&(n=!n),i=e}return n}(this.elements,e,t,o);const n=vs({x:e,y:t},this.getCenterPoint(o),xt(-this.options.rotation)),i=this.elements.map((e=>"y"===s?e.bY:e.bX)),r=Math.min(...i),a=Math.max(...i);return n[s]>=r&&n[s]<=a}getCenterPoint(e){return Ts(this,e)}draw(e){const{elements:t,options:s}=this;e.save(),e.beginPath(),e.fillStyle=s.backgroundColor,Js(e,s);const o=Us(e,s);let n=!0;for(const i of t)n?(e.moveTo(i.x,i.y),n=!1):e.lineTo(i.x,i.y);e.closePath(),e.fill(),o&&(e.shadowColor=s.borderShadowColor,e.stroke()),e.restore()}resolveElementProperties(e,t){const s=lo(e,t),{sides:o,rotation:n}=t,i=[],r=2*bt/o;let a=n*wt;for(let l=0;l<o;l++,a+=r){const o=Ko(s,t,a);o.initProperties=Xs(e,s,t),i.push(o)}return s.elements=i,s}}function Ko({centerX:e,centerY:t},{radius:s,borderWidth:o,hitTolerance:n},i){const r=(o+n)/2,a=Math.sin(i),l=Math.cos(i),d={x:e+a*s,y:t-l*s};return{type:"point",optionScope:"point",properties:{x:d.x,y:d.y,centerX:d.x,centerY:d.y,bX:e+a*(s+r),bY:t-l*(s+r)}}}Go.id="polygonAnnotation",Go.defaults={adjustScaleRange:!0,backgroundShadowColor:"transparent",borderCapStyle:"butt",borderDash:[],borderDashOffset:0,borderJoinStyle:"miter",borderShadowColor:"transparent",borderWidth:1,display:!0,hitTolerance:0,init:void 0,point:{radius:0},radius:10,rotation:0,shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,sides:3,xAdjust:0,xMax:void 0,xMin:void 0,xScaleID:void 0,xValue:void 0,yAdjust:0,yMax:void 0,yMin:void 0,yScaleID:void 0,yValue:void 0,z:0},Go.defaultRoutes={borderColor:"color",backgroundColor:"color"};const Qo={box:Mo,doughnutLabel:To,ellipse:Uo,label:Oo,line:Wo,point:Jo,polygon:Go};Object.keys(Qo).forEach((e=>{Wt.describe(`elements.${Qo[e].id}`,{_fallback:"plugins.annotation.common"})}));const Zo={update:Object.assign},en=go.concat(vo),tn=(e,t)=>ht(t)?cn(e,t):e,sn=e=>"color"===e||"font"===e;function on(e="line"){return Qo[e]?e:"line"}function nn(e,t,s,o){const n=function(e,t,s){if("reset"===s||"none"===s||"resize"===s)return Zo;return new kt(e,t)}(e,s.animations,o),i=t.annotations,r=function(e,t){const s=t.length,o=e.length;if(o<s){const t=s-o;e.splice(o,0,...new Array(t))}else o>s&&e.splice(s,o-s);return e}(t.elements,i);for(let a=0;a<i.length;a++){const t=i[a],s=ln(r,a,t.type),o=t.setContext(un(e,s,r,t)),l=s.resolveElementProperties(e,o);l.skip=rn(l),"elements"in l&&(an(s,l.elements,o,n),delete l.elements),yt(s.x)||Object.assign(s,l),Object.assign(s,l.initProperties),l.options=dn(o),n.update(s,l)}}function rn(e){return isNaN(e.x)||isNaN(e.y)}function an(e,t,s,o){const n=e.elements||(e.elements=[]);n.length=t.length;for(let i=0;i<t.length;i++){const e=t[i],r=e.properties,a=ln(n,i,e.type,e.initProperties),l=s[e.optionScope].override(e);r.options=dn(l),o.update(a,r)}}function ln(e,t,s,o){const n=Qo[on(s)];let i=e[t];return i&&i instanceof n||(i=e[t]=new n,Object.assign(i,o)),i}function dn(e){const t=Qo[on(e.type)],s={};s.id=e.id,s.type=e.type,s.drawTime=e.drawTime,Object.assign(s,cn(e,t.defaults),cn(e,t.defaultRoutes));for(const o of en)s[o]=e[o];return s}function cn(e,t){const s={};for(const o of Object.keys(t)){const n=t[o],i=e[o];sn(o)&&pt(i)?s[o]=i.map((e=>tn(e,n))):s[o]=tn(i,n)}return s}function un(e,t,s,o){return t.$context||(t.$context=Object.assign(Object.create(e.getContext()),{element:t,get elements(){return s.filter((e=>e&&e.options))},id:o.id,type:"annotation"}))}const hn=new Map,pn=e=>"doughnutLabel"!==e.type,fn=go.concat(vo);var xn={id:"annotation",version:"3.1.0",beforeRegister(){!function(e,t,s,o=!0){const n=s.split(".");let i=0;for(const r of t.split(".")){const a=n[i++];if(parseInt(r,10)<parseInt(a,10))break;if(js(a,r)){if(o)throw new Error(`${e} v${s} is not supported. v${t} or newer is required.`);return!1}}}("chart.js","4.0",X.version)},afterRegister(){X.register(Qo)},afterUnregister(){X.unregister(Qo)},beforeInit(e){hn.set(e,{annotations:[],elements:[],visibleElements:[],listeners:{},listened:!1,moveListened:!1,hooks:{},hooked:!1,hovered:[]})},beforeUpdate(e,t,s){const o=hn.get(e).annotations=[];let n=s.annotations;ht(n)?Object.keys(n).forEach((e=>{const t=n[e];ht(t)&&(t.id=e,o.push(t))})):pt(n)&&o.push(...n),function(e,t){for(const s of e)Co(s,t)}(o.filter(pn),e.scales)},afterDataLimits(e,t){const s=hn.get(e);So(e,t.scale,s.annotations.filter(pn).filter((e=>e.display&&e.adjustScaleRange)))},afterUpdate(e,t,s){const o=hn.get(e);!function(e,t,s){t.listened=Ns(s,go,t.listeners),t.moveListened=!1,xo.forEach((e=>{St(s[e])&&(t.moveListened=!0)})),t.listened&&t.moveListened||t.annotations.forEach((e=>{!t.listened&&St(e.click)&&(t.listened=!0),t.moveListened||xo.forEach((s=>{St(e[s])&&(t.listened=!0,t.moveListened=!0)}))}))}(0,o,s),nn(e,o,s,t.mode),o.visibleElements=o.elements.filter((e=>!e.skip&&e.options.display)),function(e,t,s){const o=t.visibleElements;t.hooked=Ns(s,vo,t.hooks),t.hooked||o.forEach((e=>{t.hooked||vo.forEach((s=>{St(e.options[s])&&(t.hooked=!0)}))}))}(0,o,s)},beforeDatasetsDraw(e,t,s){gn(e,"beforeDatasetsDraw",s.clip)},afterDatasetsDraw(e,t,s){gn(e,"afterDatasetsDraw",s.clip)},beforeDatasetDraw(e,t,s){gn(e,t.index,s.clip)},beforeDraw(e,t,s){gn(e,"beforeDraw",s.clip)},afterDraw(e,t,s){gn(e,"afterDraw",s.clip)},beforeEvent(e,t,s){yo(hn.get(e),t.event,s)&&(t.changed=!0)},afterDestroy(e){hn.delete(e)},getAnnotations(e){const t=hn.get(e);return t?t.elements:[]},_getAnnotationElementsAtEventForMode:(e,t,s)=>ms(e,t,s),defaults:{animations:{numbers:{properties:["x","y","x2","y2","width","height","centerX","centerY","pointX","pointY","radius"],type:"number"},colors:{properties:["backgroundColor","borderColor"],type:"color"}},clip:!0,interaction:{mode:void 0,axis:void 0,intersect:void 0},common:{drawTime:"afterDatasetsDraw",init:!1,label:{}}},descriptors:{_indexable:!1,_scriptable:e=>!fn.includes(e)&&"init"!==e,annotations:{_allKeys:!1,_fallback:(e,t)=>`elements.${Qo[on(t.type)].id}`},interaction:{_fallback:!0},common:{label:{_indexable:sn,_fallback:!0},_indexable:sn}},additionalOptionScopes:[""]};function gn(e,t,s){const{ctx:o,chartArea:n}=e,i=hn.get(e);s&&vt(o,n);const r=function(e,t){const s=[];for(const o of e)if(o.options.drawTime===t&&s.push({element:o,main:!0}),o.elements&&o.elements.length)for(const e of o.elements)e.options.display&&e.options.drawTime===t&&s.push({element:e});return s}(i.visibleElements,t).sort(((e,t)=>e.element.options.z-t.element.options.z));for(const a of r)yn(o,n,i,a);s&&jt(o)}function yn(e,t,s,o){const n=o.element;o.main?(jo(s,n,"beforeDraw"),n.draw(e,t),jo(s,n,"afterDraw")):n.draw(e,t)}function mn({data:t,options:s,fontSizes:o}){const n={...{responsive:!0,maintainAspectRatio:!1,cutout:"70%",plugins:{annotation:{animations:!1,annotations:{dLabel:{type:"doughnutLabel",content:({chart:e})=>["Total Detections",e.data.datasets[0].data.reduce(((e,t)=>e+t),0).toString()],font:o=o||(({chart:e})=>{const t=e?.width||200;return[{size:Math.max(Math.floor(t/45),10),family:"Arial"},{size:Math.max(Math.floor(t/20),14),family:"Arial"}]}),color:"#FFFFFF"}}},legend:{position:"bottom",labels:{color:"#FFFFFF",usePointStyle:!0,pointStyle:"circle"}},tooltip:{callbacks:{label:function(e){return`${e.label}: ${e.raw}`}}}}},...s};return e.jsx(Qt,{data:t,options:n})}X.register(q,U,J,Z,G,K,Q,xn);const bn={xs:200,sm:300,lg:400},vn=o.forwardRef(((n,i)=>{const{devMode:r,timezone:a,showIDs:p}=Y(),{user:f}=M(),{vesselInfo:x,fetchVesselsInfo:g}=Rt(),[y,m]=o.useState(),[b,v]=o.useState(),[j,S]=o.useState();o.useEffect((()=>{w()}),[x]);const w=async()=>{x&&Array.isArray(x)?S(x):g()};o.useEffect((()=>{T.get("/statistics",{params:{type:"weekly"}}).then((e=>v(e.data))).catch(console.error)}),[]),o.useImperativeHandle(i,(()=>({getAllStats:()=>b}))),o.useEffect((()=>{b&&0!==b.length&&m(b[0])}),[b]);const C=e=>{m((t=>{var s=b.findIndex((e=>e._id===t._id));return"increment"===e?s+=1:"decrement"===e&&(s-=1),b[s]?b[s]:t}))},D=e=>Object.fromEntries(Object.entries(e).map((([e,t])=>{const s=j.find((t=>t.vessel_id===e));return s?(r||p)&&s.unit_id?[`${s.name} (${s.unit_id})`,t]:[s.name,t]:[e,t]}))),R=o.useMemo((()=>{if(!y||!j||!f)return null;let e=y.stats;if(r||j.forEach((t=>{!t.is_active&&t.vessel_id&&(delete e.totalSensorsOnlineDuration?.[t.vessel_id],delete e.totalSensorsDurationAtSea?.[t.vessel_id],delete e.totalSmartmastsDistanceTraveled?.[t.vessel_id],delete e.totalVesselsDetectedbySensors?.[t.vessel_id])})),!f.hasPermissions([O.accessAllVessels])){let t={totalSensorsOnlineDuration:{},totalSensorsDurationAtSea:{},totalSmartmastsDistanceTraveled:{},totalVesselsDetectedbySensors:{}};const s=f.allowed_vessels||[];j.forEach((o=>{s.some((e=>e.toString()===o.vessel_id))&&o.vessel_id&&e.totalVesselsDetectedbySensors?.[o.vessel_id]&&(t.totalSensorsOnlineDuration[o.vessel_id]=e.totalSensorsOnlineDuration?.[o.vessel_id],t.totalSensorsDurationAtSea[o.vessel_id]=e.totalSensorsDurationAtSea?.[o.vessel_id],t.totalSmartmastsDistanceTraveled[o.vessel_id]=e.totalSmartmastsDistanceTraveled?.[o.vessel_id],t.totalVesselsDetectedbySensors[o.vessel_id]=e.totalVesselsDetectedbySensors?.[o.vessel_id])})),e={...e,...t}}return e.totalVesselsByHoursLocal={},Object.keys(e.totalVesselsByHoursUTC).forEach((t=>{const s=(Number(t)+8)%24;e.totalVesselsByHoursLocal[s]=e.totalVesselsByHoursUTC[t]})),e.totalSensorsOnlineDurationHours={},Object.keys(e.totalSensorsOnlineDuration||{}).forEach((t=>{e.totalSensorsOnlineDurationHours[t]=parseFloat((e.totalSensorsOnlineDuration[t]/1e3/60/60).toFixed(2))})),e.totalSensorsDurationAtSeaHours={},Object.keys(e.totalSensorsDurationAtSea||{}).forEach((t=>{e.totalSensorsDurationAtSeaHours[t]=parseFloat((e.totalSensorsDurationAtSea[t]/1e3/60/60).toFixed(2))})),e.totalSmartmastsDistanceTraveledMiles={},Object.keys(e.totalSmartmastsDistanceTraveled||{}).forEach((t=>{e.totalSmartmastsDistanceTraveledMiles[t]=parseFloat((62137e-8*e.totalSmartmastsDistanceTraveled[t]).toFixed(2))})),e.totalSmartmastsDistanceTraveledbyAllMiles=parseFloat(62137e-8*Object.values(e.totalSmartmastsDistanceTraveled||{}).reduce(((e,t)=>e+t),0)).toFixed(2),e.listOfTextsExtractedArray=e.listOfTextsExtracted.map(((e,t)=>({id:t,value:e}))),e.totalVesselsByWeekDayHoursLocal={},Object.keys(e.totalVesselsByWeekDayHoursUTC).forEach((t=>{const s=new Date(new Date(t).getTime()+288e5).toISOString().replace("Z","+08:00");e.totalVesselsByWeekDayHoursLocal[s]=e.totalVesselsByWeekDayHoursUTC[t]})),e.totalVesselsSuperCategorized=F(e.totalVesselsSuperCategorized),e.totalVesselsSubCategorized=F(e.totalVesselsSubCategorized),e.totalVesselsDetectedbySensors=F(e.totalVesselsDetectedbySensors||{}),e.totalSensorsOnlineDurationHours=F(e.totalSensorsOnlineDurationHours),e.totalSmartmastsDistanceTraveledMiles=F(e.totalSmartmastsDistanceTraveledMiles),e}),[y,j,r,f]);return R?e.jsxs(t,{container:!0,spacing:2,color:"#FFFFFF",flexDirection:"column",wrap:"nowrap",overflow:"auto",width:"100%",children:[e.jsxs(t,{container:!0,alignItems:"center",justifyContent:"space-between",wrap:"nowrap",bgcolor:"primary.main",borderRadius:"20px",padding:2,children:[e.jsx(t,{children:e.jsx(d,{sx:{p:0},disabled:y._id===b[0]._id,onClick:()=>C("decrement"),children:e.jsx(c,{})})}),e.jsx(t,{children:e.jsxs(s,{sx:{typography:{xs:"caption",sm:"body1"}},fontWeight:"bold",textAlign:"center",display:"flex",children:["Data for the time period from:"," ",A(y.fromTimestamp).tz(a).format(_.dateTimeFormat(f,{exclude_seconds:!0}))," ","to:"," ",A(y.toTimestamp).tz(a).format(_.dateTimeFormat(f,{exclude_seconds:!0}))," ","(",a,")"]})}),e.jsx(t,{children:e.jsx(d,{sx:{p:0},disabled:y._id===b[b.length-1]._id,onClick:()=>C("increment"),children:e.jsx(u,{})})})]}),e.jsxs(t,{container:!0,spacing:2,children:[e.jsx(t,{size:{xs:12,md:6,lg:6},children:e.jsx(Yt,{title:"Artifacts with At Least One Vessel",values:[{title:"AI Confidence > 40%",value:R.totalArtifactsWithAtleastOneVessel.confidenceAbove40},{title:"AI Confidence > 80%",value:R.totalArtifactsWithAtleastOneVessel.confidenceAbove80}],icon:"/stats-icon-4.svg"})}),e.jsx(t,{size:{xs:12,md:6,lg:6},children:e.jsx(Yt,{title:"Distance Traveled by All Smartmasts (miles)",value:R.totalSmartmastsDistanceTraveledbyAllMiles,icon:"/stats-icon-3.svg"})})]}),e.jsxs(t,{container:!0,spacing:2,columns:12,children:[e.jsxs(t,{display:P(V.stagingAndProduction)&&!r?"none":"flex",flexDirection:"column",minHeight:bn,justifyContent:"center",backgroundColor:"primary.main",borderRadius:"20px",sx:{padding:3},size:{xs:12,lg:6},children:[e.jsx(s,{fontWeight:"600",fontSize:{xs:"14px",md:"20px"},children:"Total Vessels Categorized by Super Category"}),Object.keys(R.totalVesselsSuperCategorized).length>0?e.jsx(es,{data:{labels:Object.keys(R.totalVesselsSuperCategorized),datasets:[{label:"Total Vessels",data:Object.values(R.totalVesselsSuperCategorized),backgroundColor:k.palette.custom.mainBlue}].filter((e=>e))},options:{responsive:!0}}):e.jsx(h,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:"93%"},children:e.jsx(s,{children:"No data available for specified time range"})})]}),e.jsxs(t,{display:P(V.stagingAndProduction)&&!r?"none":"flex",flexDirection:"column",minHeight:bn,justifyContent:"center",backgroundColor:"primary.main",borderRadius:"20px",sx:{padding:3},size:{xs:12,lg:6},children:[e.jsx(s,{fontWeight:"600",fontSize:{xs:"14px",md:"20px"},children:"Total Vessels Categorized by Sub Category"}),Object.keys(R.totalVesselsSubCategorized).length>0?e.jsx(es,{data:{labels:Object.keys(R.totalVesselsSubCategorized),datasets:[{label:"Total Vessels",data:Object.values(R.totalVesselsSubCategorized),backgroundColor:k.palette.custom.mainBlue}]},options:{responsive:!0}}):e.jsx(h,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:"93%"},children:e.jsx(s,{children:"No data available for specified time range"})})]}),e.jsxs(t,{display:P(V.stagingAndProduction)&&!r?"none":"flex",flexDirection:"column",minHeight:{xs:400,sm:600,lg:550},maxHeight:bn,justifyContent:"center",backgroundColor:"primary.main",borderRadius:"20px",sx:{padding:3,height:"330 !important"},size:{xs:12,lg:6},children:[e.jsx(s,{fontWeight:"600",fontSize:{xs:"14px",md:"20px"},children:"Total Vessels Categorized by Country Flag"}),Object.keys(R.totalVesselsWithCountryFlag).length>0?e.jsx(t,{justifyContent:"center",alignItems:"center",display:"flex",height:"100%",width:"100%",children:e.jsx(ts,{data:{labels:Object.keys(R.totalVesselsWithCountryFlag),datasets:[{label:"Total Vessels",data:Object.values(R.totalVesselsWithCountryFlag),backgroundColor:Array.from({length:Object.keys(R.totalVesselsWithCountryFlag).length}).map(((e,t)=>z(10+t,1))),borderColor:Array.from({length:Object.keys(R.totalVesselsWithCountryFlag).length}).map(((e,t)=>z(10+t,1))),borderWidth:1}]},options:{responsive:!0}})}):e.jsx(h,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:"93% !important"},children:e.jsx(s,{children:"No data available for specified time range"})})]}),e.jsxs(t,{flexDirection:"column",minHeight:{xs:400,sm:600,lg:550},maxHeight:"none",height:"auto",justifyContent:"center",display:"flex",backgroundColor:"primary.main",borderRadius:"20px",sx:{padding:3,gap:3},size:{xs:12,lg:6},children:[e.jsx(s,{fontWeight:"600",fontSize:{xs:"14px",md:"20px"},children:"Total Vessels Detected by Sensors"}),Object.keys(R.totalVesselsDetectedbySensors).length>0?e.jsx(t,{justifyContent:"center",alignItems:"center",display:"flex",height:"90%",width:"100%",children:e.jsx(mn,{data:{labels:Object.keys(D(R.totalVesselsDetectedbySensors)),datasets:[{label:"Total Vessels",data:Object.values(R.totalVesselsDetectedbySensors),backgroundColor:Array.from({length:Object.keys(R.totalVesselsDetectedbySensors).length}).map(((e,t)=>z(10+t,1))),borderColor:Array.from({length:Object.keys(R.totalVesselsDetectedbySensors).length}).map(((e,t)=>z(10+t,1))),borderWidth:1}]},options:{responsive:!0}})}):e.jsx(h,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:"93%"},children:e.jsx(s,{children:"No data available for specified time range"})})]}),e.jsxs(t,{display:P(V.stagingAndProduction)&&!r?"none":"flex",flexDirection:"column",minHeight:bn,justifyContent:"center",backgroundColor:"primary.main",borderRadius:"20px",sx:{padding:3},size:{xs:12,lg:6},children:[e.jsx(s,{fontWeight:"600",fontSize:{xs:"14px",md:"20px"},children:"Total Vessels Observed by Hour of Day"}),Object.keys(R.totalVesselsByHoursLocal).length>0?e.jsx(ss,{data:{labels:Object.keys(R.totalVesselsByHoursLocal),datasets:[{label:"Total Vessels",data:Object.values(R.totalVesselsByHoursLocal),backgroundColor:k.palette.custom.mainBlue,stepped:!0}]},options:{responsive:!0}}):e.jsx(h,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:"93%"},children:e.jsx(s,{children:"No data available for specified time range"})})]}),e.jsxs(t,{flexDirection:"column",minHeight:bn,justifyContent:"center",display:"flex",backgroundColor:"primary.main",borderRadius:"20px",sx:{padding:3},size:{xs:12,lg:6},children:[e.jsx(s,{fontWeight:"600",fontSize:{xs:"14px",md:"20px"},children:"Smartmasts Activity Time (hours)"}),Object.keys(R.totalSensorsOnlineDurationHours).length>0?e.jsx(es,{data:{labels:Object.keys(D(R.totalSensorsOnlineDurationHours)),datasets:[{label:"Total Time Online",data:Object.values(R.totalSensorsOnlineDurationHours),backgroundColor:k.palette.custom.mainBlue},{label:"Total Time at Sea",data:Object.keys(R.totalSensorsOnlineDurationHours).map((e=>R.totalSensorsDurationAtSeaHours[e]||0)),backgroundColor:"#10B981"}].filter((e=>e))},options:{responsive:!0}}):e.jsx(h,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:"93%"},children:e.jsx(s,{children:"No data available for specified time range"})})]}),e.jsxs(t,{flexDirection:"column",minHeight:bn,justifyContent:"center",display:"flex",backgroundColor:"primary.main",borderRadius:"20px",sx:{padding:3},size:{xs:12,lg:6},children:[e.jsx(s,{fontWeight:"600",fontSize:{xs:"14px",md:"20px"},children:"Smartmasts Distance Traveled (miles)"}),Object.keys(R.totalSmartmastsDistanceTraveledMiles).length>0?e.jsx(es,{data:{labels:Object.keys(D(R.totalSmartmastsDistanceTraveledMiles)),datasets:[{label:"Total Distance",data:Object.values(R.totalSmartmastsDistanceTraveledMiles),backgroundColor:k.palette.custom.mainBlue}].filter((e=>e))},options:{responsive:!0}}):e.jsx(h,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:"93%"},children:e.jsx(s,{children:"No data available for specified time range"})})]}),e.jsxs(t,{display:P(V.stagingAndProduction)&&!r?"none":"flex",flexDirection:"column",minHeight:bn,backgroundColor:"primary.main",borderRadius:"20px",sx:{padding:3},size:{xs:12,lg:6},children:[e.jsx(s,{fontWeight:"600",fontSize:{xs:"14px",md:"20px"},children:"Vessels Observation Heatmap"}),e.jsx(h,{height:"100%",width:"100%",display:"flex",justifyContent:"center",alignItems:"center",children:Object.keys(R.totalVesselsByWeekDayHoursLocal).length>0?e.jsx(h,{width:"100%",children:e.jsx(gs,{chartSeries:I(A(y.fromTimestamp).tz(a).format(E.dateTimeFormat({exclude_hours:!0,exclude_minutes:!0,exclude_seconds:!0})),A(y.toTimestamp).tz(a).format(E.dateTimeFormat({exclude_hours:!0,exclude_minutes:!0,exclude_seconds:!0}))).sort(((e,t)=>new Date(t)-new Date(e))).map((e=>({name:W(new Date(e).getUTCDay()),data:Array.from({length:24}).map(((t,s)=>({x:s,y:R.totalVesselsByWeekDayHoursLocal[`${e}T${s.toString().padStart(2,"0")}:00:00.000+08:00`]||0})))})))})}):e.jsx(h,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:"93%"},children:e.jsx(s,{children:"No data available for specified time range"})})})]}),e.jsx(t,{display:P(V.stagingAndProduction)&&!r?"none":"flex",maxHeight:400,justifyContent:"center",backgroundColor:"primary.main",borderRadius:"20px",sx:{padding:3},minHeight:400,size:{xs:12,lg:6},children:Object.keys(R.totalVesselsByWeekDayHoursLocal).length>0?e.jsx(Ht,{disableRowSelectionOnClick:!0,rows:R.listOfTextsExtractedArray,columns:[{field:"value",headerName:"List of Texts Extracted",flex:1}],sx:{backgroundColor:k.palette.custom.darkBlue,height:340}}):e.jsx(h,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:"93%"},children:e.jsx(s,{children:"No data available for specified time range"})})})]})]}):e.jsx(l,{})}));vn.displayName="StatisticsPastWeeks";const jn={xs:200,sm:300,lg:400};function Sn(){const{devMode:n,timezone:i,showIDs:r}=Y(),{user:a}=M(),{vesselInfo:p,fetchVesselsInfo:f}=Rt(),[x,g]=o.useState({}),[y,m]=o.useState([]),[b,v]=o.useState([]);o.useEffect((()=>{j()}),[p]);const j=async()=>{p&&Array.isArray(p)?v(p):f()};o.useEffect((()=>{T.get("/statistics",{params:{type:"daily"}}).then((e=>m(e.data))).catch(console.error)}),[]),o.useEffect((()=>{y&&0!==y.length&&g(y[0])}),[y]);const S=e=>{g((t=>{var s=y.findIndex((e=>e._id===t._id));return"increment"===e?s+=1:"decrement"===e&&(s-=1),y[s]?y[s]:t}))},w=o.useMemo((()=>{if(!x||!x.stats||0===b.length||!a)return null;let e=x.stats;if(n||b.forEach((t=>{!t.is_active&&t.vessel_id&&delete e.totalVesselsDetectedbySensors?.[t.vessel_id]})),!a.hasPermissions([O.accessAllVessels])){let t={totalVesselsDetectedbySensors:{}};const s=a.allowed_vessels||[];b.forEach((o=>{s.some((e=>e.toString()===o.vessel_id))&&o.vessel_id&&e.totalVesselsDetectedbySensors?.[o.vessel_id]&&(t.totalVesselsDetectedbySensors[o.vessel_id]=e.totalVesselsDetectedbySensors?.[o.vessel_id])})),e={...e,...t}}return e.totalVesselsDetectedbySensors=F(e.totalVesselsDetectedbySensors||{}),e.totalVesselsSuperCategorized=F(e.totalVesselsSuperCategorized),e.totalVesselsSubCategorized=F(e.totalVesselsSubCategorized),e}),[x,n,b,a]);return w?e.jsxs(t,{container:!0,gap:2,color:"#FFFFFF",flexDirection:"column",wrap:"nowrap",overflow:"auto",width:"100%",children:[e.jsxs(t,{container:!0,alignItems:"center",justifyContent:"space-between",wrap:"nowrap",bgcolor:"primary.main",borderRadius:"20px",padding:2,children:[e.jsx(t,{children:e.jsx(d,{sx:{p:0},disabled:x._id===y[0]._id,onClick:()=>S("decrement"),children:e.jsx(c,{})})}),e.jsx(t,{children:e.jsxs(s,{sx:{typography:{xs:"caption",sm:"body1"}},fontWeight:"bold",textAlign:"center",display:"flex",children:["Data for the time period from:"," ",A(x.fromTimestamp).tz(i).format(_.dateTimeFormat(a,{exclude_seconds:!0}))," ","to:"," ",A(x.toTimestamp).tz(i).format(_.dateTimeFormat(a,{exclude_seconds:!0}))," ","(",i,")"]})}),e.jsx(t,{children:e.jsx(d,{sx:{p:0},disabled:x._id===y[y.length-1]._id,onClick:()=>S("increment"),children:e.jsx(u,{})})})]}),e.jsxs(t,{width:"100%",container:!0,spacing:2,display:P(V.stagingAndProduction)&&!n?"none":"flex",children:[e.jsx(t,{size:{xs:12,md:5.8,lg:4},children:e.jsx(Yt,{title:"Vessels Detected",value:w.totalVesselsDetected,icon:"/stats-icon-4.svg"})}),e.jsx(t,{size:{xs:12,md:5.8,lg:4},children:e.jsx(Yt,{title:"SmartMasts at Sea",value:w.totalSmartmastsAtSea,icon:"/stats-icon-1.svg"})}),e.jsx(t,{size:{xs:12,md:5.8,lg:4},children:e.jsx(Yt,{title:"SmartMasts Online",value:w.totalSmartmastsOnline,icon:"/stats-icon-2.svg"})})]}),e.jsxs(t,{container:!0,spacing:2,columns:12,children:[e.jsxs(t,{display:"flex",flexDirection:"column",justifyContent:"flex-start",minHeight:jn,maxHeight:"none",height:"auto",backgroundColor:"primary.main",borderRadius:"20px",sx:{padding:3,gap:3},size:{xs:12,lg:6},children:[e.jsx(s,{fontWeight:"600",fontSize:{xs:"14px",md:"20px"},children:"Total Vessels Detected By Sensors"}),Object.keys(w.totalVesselsDetectedbySensors).length>0?e.jsx(t,{justifyContent:"center",alignItems:"center",display:"flex",height:"90%",children:e.jsx(mn,{data:{labels:Object.keys((C=w.totalVesselsDetectedbySensors,Object.fromEntries(Object.entries(C).map((([e,t])=>{const s=b.find((t=>t.vessel_id===e));return s?(n||r)&&s.unit_id?[`${s.name} (${s.unit_id})`,t]:[s.name,t]:[e,t]}))))),datasets:[{label:"Vessels Detected",data:Object.values(w.totalVesselsDetectedbySensors),backgroundColor:Array.from({length:Object.keys(w.totalVesselsDetectedbySensors).length}).map(((e,t)=>z(10+t,1))),borderColor:Array.from({length:Object.keys(w.totalVesselsDetectedbySensors).length}).map(((e,t)=>z(10+t,1))),borderWidth:1}]},options:{responsive:!0}})}):e.jsx(h,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:"93% !important"},children:e.jsx(s,{children:"No data available for specified time range"})})]}),e.jsxs(t,{display:P(V.stagingAndProduction)&&!n?"none":"flex",flexDirection:"column",justifyContent:"flex-start",minHeight:jn,backgroundColor:"primary.main",borderRadius:"20px",sx:{padding:3},size:{xs:12,lg:6},children:[e.jsx(s,{fontWeight:"600",fontSize:{xs:"14px",md:"20px"},children:"Total Vessels Categorized by Super Category"}),Object.keys(w.totalVesselsSuperCategorized).length>0?e.jsx(es,{data:{labels:Object.keys(w.totalVesselsSuperCategorized),datasets:[{label:"Total Vessels",data:Object.values(w.totalVesselsSuperCategorized),backgroundColor:k.palette.custom.mainBlue}].filter((e=>e))},options:{responsive:!0}}):e.jsx(h,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:"93%"},children:e.jsx(s,{fontWeight:"600",fontSize:{xs:"14px",md:"20px"},children:"No data available for specified time range"})})]}),e.jsxs(t,{display:P(V.stagingAndProduction)&&!n?"none":"flex",flexDirection:"column",justifyContent:"center",minHeight:jn,backgroundColor:"primary.main",borderRadius:"20px",sx:{padding:3},size:{xs:12,lg:6},children:[e.jsxs(s,{fontWeight:"600",fontSize:{xs:"14px",md:"20px"},children:["Total Vessels Categorized by Sub Category"," "]}),Object.keys(w.totalVesselsSubCategorized).length>0?e.jsx(es,{data:{labels:Object.keys(w.totalVesselsSubCategorized),datasets:[{label:"Total Vessels",data:Object.values(w.totalVesselsSubCategorized),backgroundColor:k.palette.custom.mainBlue}].filter((e=>e))},options:{responsive:!0}}):e.jsx(h,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:"93%"},children:e.jsx(s,{fontWeight:"600",fontSize:{xs:"14px",md:"20px"},children:"No data available for specified time range"})})]})]})]}):e.jsx(l,{});var C}const wn=Array.from({length:24},((e,t)=>t+1));function Cn({open:t,onClose:s,onSubmit:n,initialValue:i="0"}){const[r,a]=o.useState(i);o.useEffect((()=>{t&&a(i)}),[t,i]);const l=()=>{s()};return e.jsxs(p,{sx:{"& .MuiDialog-paper":{backgroundColor:"#020716"}},open:t,onClose:l,"aria-labelledby":"report-duration-dialog-title",fullWidth:!0,maxWidth:"xs",children:[e.jsx(f,{sx:{color:"#FFFFFF"},id:"report-duration-dialog-title",children:"Choose Report Coverage"}),e.jsx(x,{children:e.jsx(h,{component:"form",noValidate:!0,autoComplete:"off",sx:{mt:1},children:e.jsxs(g,{fullWidth:!0,required:!0,children:[e.jsx(y,{sx:{color:"#FFFFFF"},id:"duration-select-label",children:"Weeks of Coverage"}),e.jsxs(m,{labelId:"duration-select-label",id:"duration-select",value:r,label:"Weeks of Coverage",onChange:e=>{a(e.target.value)},children:[e.jsx(b,{value:"0",children:"All Time"}),wn.map((t=>e.jsxs(b,{value:t,children:[t," Week",t>1?"s":""," "]},t)))]})]})})}),e.jsxs(v,{sx:{padding:"16px 24px"},children:[e.jsx(j,{onClick:l,sx:{color:"#FFFFFF",textTransform:"none",border:"1px solid grey",padding:"10px"},children:"Cancel"}),e.jsx(j,{onClick:()=>{n(r)},variant:"contained",color:"primary",children:"Submit"})]})]})}function Dn(){const{user:s}=M(),{vesselInfo:n,fetchVesselsInfo:i}=Rt(),[r,a]=o.useState(!1),[c,u]=o.useState(""),[h,p]=o.useState(!1),f=o.useRef(null),x=R(),g=o.useMemo((()=>[{value:"past_daylights",label:"Past 24 Hours",component:e.jsx(Sn,{}),display:s?.hasPermissions([O.viewStatistics])},{value:"past_weeks",label:"Total",component:e.jsx(vn,{ref:f}),display:s?.hasPermissions([O.viewStatistics])}]),[s]);o.useEffect((()=>{u(g.find((e=>e.display))?.value||"")}),[g]);const y=o.useCallback((async e=>{try{a(!0),p(!1);const t=f.current.getAllStats();e=Number(e);let o=n;o&&0!==o.length||(o=await i());const{csvString:r,filename:l}=((e,t,s)=>{if(!t)return{};const o=["vessel, sensorId, timestamp,total detections,total time online (h),total time at sea (h),distance travelled (miles)"],n={},i=e.hasPermissions([O.accessAllVessels])?"all":e.allowed_vessels||[];for(const l of t){const e=l.stats,t=`${l.fromTimestamp.split("T")[0]} - ${l.toTimestamp.split("T")[0]}`;("all"===i?Object.keys(e.totalVesselsDetectedbySensors):i).forEach((o=>{const i=s.find((e=>e.vessel_id===o)),r=i?.name??"N/A",a=e.totalVesselsDetectedbySensors?.[o]??"N/A",l=e.totalSensorsOnlineDuration?.[o]?parseFloat(e.totalSensorsOnlineDuration?.[o]/1e3/60/60).toFixed(2)??"N/A":0,d=e.totalSensorsDurationAtSea?.[o]?parseFloat((e.totalSensorsDurationAtSea?.[o]/1e3/60/60).toFixed(2))??"N/A":0,c=e.totalSmartmastsDistanceTraveled?.[o]?parseFloat((62137e-8*e.totalSmartmastsDistanceTraveled?.[o]).toFixed(2))??"N/A":0;(n[o]||=[]).push([r,i?.vessel_id,t,a,l,d,isNaN(c)?"N/A":c].join(","))}))}if(0===Object.keys(n).length)return{};for(const l of Object.keys(n))for(const e of n[l])o.push(e);const r=t[0].toTimestamp.split("T")[0],a=t[t.length-1].fromTimestamp.split("T")[0];return{csvString:o.join("\n"),filename:`smartmast_stats_${a}_to_${r}.csv`}})(s,0===e?t:t.slice(0,e),o);if(!r||0===r.length)return x("No statistics data available for download.",{variant:"success"}),void a(!1);const d=new Blob([r],{type:"text/csv;charset=utf-8;"});H(d,l)}catch(t){x("Error downloading CSV",{variant:"error"})}finally{a(!1)}}),[s]);return s&&g.some((e=>e.display))&&c&&e.jsxs(t,{container:!0,color:"#FFFFFF",flexDirection:"column",width:"100%",height:"100%",overflow:"auto",sx:{backgroundColor:k.palette.custom.darkBlue},children:[e.jsxs(t,{size:12,container:!0,alignItems:"center",justifyContent:"space-between",wrap:"nowrap",borderRadius:"20px",paddingX:2,paddingTop:2,paddingRight:4,children:[e.jsx(t,{size:9,container:!0,paddingBottom:0,display:"flex",rowGap:2,justifyContent:"space-between",alignItems:"center",flexWrap:"wrap",children:e.jsx(t,{size:{xs:12,lg:3.9},children:e.jsx(S,{value:c,onChange:(e,t)=>u(t),sx:{width:"100%",padding:"4px",border:`2px solid ${k.palette.custom.borderColor}`,borderRadius:"8px",backgroundColor:"transparent","& .MuiTabs-flexContainer":{height:"100%"},"& .MuiButtonBase-root":{width:"50%",borderRadius:"8px"},"& .MuiButtonBase-root.Mui-selected":{backgroundColor:k.palette.custom.mainBlue}},children:g.filter((e=>e.display)).map((t=>e.jsx(w,{label:t.label,value:t.value,sx:{maxWidth:"none",textTransform:"none"}},t.value)))})})}),!P(V.stagingAndProduction)&&!r&&e.jsx(t,{size:3,sx:{textAlign:"right"},children:e.jsx(d,{sx:{p:"5px 10px",borderRadius:"16px",border:"1px solid white"},onClick:()=>p(!0),variant:"contained",children:e.jsx(C,{})})}),!P(V.stagingAndProduction)&&r&&e.jsx(t,{item:!0,sx:{width:"auto",paddingRight:"15px"},children:e.jsx(l,{size:30,sx:{color:D("#FFFFFF",.9),marginRight:2}})})]}),e.jsx(t,{container:!0,flexDirection:"column",overflow:"auto",width:"100%",size:"grow",children:g.filter((e=>e.display)).map((s=>e.jsx(t,{display:c!==s.value&&"none",padding:2,width:"100%",size:"auto",children:s.component},s.value)))}),e.jsx(Cn,{open:h,onClose:()=>p(!1),onSubmit:y})]})}export{Dn as default};

import{r as e,j as t,bF as r,G as o,T as n,bb as i,bf as a,x as s,X as l,Y as c,c9 as d,ca as u,am as h,K as p,aA as g,az as x,cb as f,aC as m,a0 as v,cc as b,cd as F,ce as w,cf as j,cg as C,ch as y,ci as k,cj as S,ck as A,aD as R,L as E,R as L,ap as I,cl as T,cm as D,cn as P}from"./vendor-DvOQ6qlC.js";import{M}from"./ModalContainer-B8xU4Z17.js";import{D as z,t as B,P as O,u as U,Q as H,R as $,h as W,e as _,q as V,T as N,i as G}from"./index-BuG1pLrZ.js";import{u as X}from"./AppHook-CeBj5aBe.js";import{a as Y}from"./ArtifactFlag.controller-ehZ-A4E3.js";const q=({state:h,onClose:p,shareLink:g,isImageLink:x,shareEventLink:f})=>{const[m,v]=e.useState(!1),b=z();return t.jsx(r,{open:h,onClose:p,children:t.jsx(M,{title:"Share Link",onClose:p,children:t.jsxs(o,{container:!0,flexDirection:"column",gap:2,width:{xs:300,sm:500},children:[t.jsx(o,{container:!0,justifyContent:"center",height:"100%",size:"grow",children:x?t.jsx("img",{src:g,alt:"share",loading:"lazy",style:{width:"100%",height:"100%",borderRadius:8}}):t.jsx("video",{src:g,controls:!0,autoPlay:!0,muted:!0,preload:"auto",style:{width:"100%",height:"100%",borderRadius:8}})}),t.jsx(o,{children:t.jsx(n,{fontWeight:"100",children:"Copy the link below to share:"})}),t.jsx(o,{container:!0,alignItems:"center",gap:1,children:t.jsx(i,{sx:{m:1,width:"100%"},variant:"outlined",children:t.jsx(a,{value:f||g,sx:{color:s("#FFFFFF",.5),fontWeight:300},endAdornment:t.jsx(l,{position:"end",children:t.jsx(c,{edge:"end",onClick:e=>{e.stopPropagation(),navigator.clipboard.writeText(f||g),b("Link copied to clipboard!",{variant:"success"}),v(!0),setTimeout((()=>v(!1)),5e3)},disabled:m,children:m?t.jsx(d,{sx:{color:"#FFFFFF"}}):t.jsx(u,{sx:{color:"#FFFFFF"}})})})})})})]})})})},J=e.forwardRef((function({value:r=0,onChange:o,onChangeCommitted:n,onMouseDown:i,marks:a=[],height:s=24,sx:l={},totalSeconds:c,getHoverTitle:d,tooltipContainer:u},g){const x=e.useRef(null),f=e.useRef(null),m=e.useRef(null),v=e.useRef(!1),[b,F]=e.useState(null),w=e.useRef({x:0,y:0}),j=e.useRef(null),C=e=>Math.max(0,Math.min(100,e)),y=e=>{const t=C(e);f.current&&(f.current.style.width=t+"%"),m.current&&(m.current.style.left=`calc(${t}% - 4px)`)},k=e.useCallback((e=>{const t=x.current;if(!t)return 0;const r=t.getBoundingClientRect(),o=e.touches?e.touches[0].clientX:e.clientX;return C((o-r.left)/r.width*100)}),[]),S=e.useCallback((e=>{i&&i(e),v.current=!0;const t=k(e);y(t),o&&o(e,t),e.preventDefault?.()}),[i,o,k]),A=e.useCallback((e=>{if(!v.current)return;const t=k(e);y(t),o&&o(e,t),e.preventDefault?.()}),[o,k]),R=e.useCallback((e=>{if(!v.current)return;v.current=!1;const t=e.changedTouches?e.changedTouches[0]:e,r=k(t);y(r),n&&n(e,r),e.preventDefault?.()}),[n,k]),E=e.useCallback((e=>{if(w.current={x:e.clientX,y:e.clientY},null!=j.current&&j.current.update(),!v.current){const t=k(e);F(t/100*(c||100))}}),[k,c]);return e.useEffect((()=>{v.current||y(r)}),[r]),e.useEffect((()=>{const e=e=>A(e),t=e=>R(e),r=e=>A(e),o=e=>R(e);return window.addEventListener("mousemove",e),window.addEventListener("mouseup",t),window.addEventListener("touchmove",r,{passive:!1}),window.addEventListener("touchend",o,{passive:!1}),()=>{window.removeEventListener("mousemove",e),window.removeEventListener("mouseup",t),window.removeEventListener("touchmove",r),window.removeEventListener("touchend",o)}}),[A,R]),t.jsx(h,{open:null!==b,title:d?d(b||0):`${Math.round(b||0)}s`,placement:"top",arrow:!0,slotProps:{popper:{popperRef:j,anchorEl:{getBoundingClientRect:()=>new DOMRect(w.current.x,x.current?.getBoundingClientRect().y||0,0,0)},...u?{container:u}:{}}},children:t.jsxs(p,{ref:e=>{"function"==typeof g?g(e):g&&(g.current=e),x.current=e},onMouseDown:S,onTouchStart:S,onMouseEnter:e=>{const t=k(e);F(t/100*(c||100)),w.current={x:e.clientX,y:e.clientY}},onMouseMove:E,onMouseLeave:()=>{v.current||F(null)},sx:{position:"relative",width:"100%",height:s,cursor:"pointer",userSelect:"none","& .scrubber-rail":{position:"absolute",left:0,top:0,bottom:0,right:0,backgroundColor:"#FFFFFF4D",opacity:.28},"& .scrubber-track":{position:"absolute",left:0,top:0,bottom:0,width:`${C(r)}%`,backgroundColor:B.palette.custom.mainBlue,willChange:"width",transition:v.current?"none":"width 0.3s ease-out"},"& .scrubber-thumb":{position:"absolute",top:0,bottom:0,left:`calc(${C(r)}% - 4px)`,width:8,backgroundColor:"#FFFFFF",willChange:"left",transition:v.current?"none":"left 0.3s ease-out"},"& .scrubber-mark":{position:"absolute",top:0,bottom:0,width:4,backgroundColor:"rgba(255,255,255,0.6)"},...l},children:[t.jsx(p,{className:"scrubber-rail"}),t.jsx(p,{className:"scrubber-track",ref:f}),Array.isArray(a)&&a?.length>0&&a.map(((e,r)=>t.jsx(p,{className:"scrubber-mark",sx:{left:`calc(${C(e.value)}% - 2px)`}},`${r}-${e.value}`))),t.jsx(p,{className:"scrubber-thumb",ref:m})]})})})),K=e.forwardRef((({src:r,onFullscreen:n,isFullscreen:i=!1,styles:a={}},s)=>{const l=e.useRef(null),{screenSize:d}=X(),[u,h]=e.useState(!1),[v,b]=e.useState(0),[F,w]=e.useState(0),j={width:"100%",height:i?"100%":"auto",maxHeight:i?"auto":d.sm||d.md?"110px":"100px",objectFit:i?"contain":"cover"};e.useEffect((()=>{const e=l.current;if(e){const t=()=>{b(e.currentTime),e.duration&&isFinite(e.duration)&&w(e.duration)};return e.addEventListener("timeupdate",t),()=>{e.removeEventListener("timeupdate",t)}}}),[]);const C=()=>{const e=l.current;e&&(u?e.pause():e.play(),h(!u))},y=(e,t)=>{const r=l.current;r&&(r.currentTime=t/100*r.duration)};return t.jsxs(p,{sx:{background:"#000000",borderRadius:i?0:"10px",overflow:"hidden",position:"relative",height:i?"100%":"auto"},children:[t.jsx(p,{onClick:()=>{C()},children:t.jsx("video",{ref:e=>{l.current=e,"function"==typeof s?s(e):s&&"object"==typeof s&&(s.current=e)},src:r,preload:"auto",style:{...j,...a}})}),t.jsxs(o,{container:!0,alignItems:"center",gap:1,sx:{position:i?"absolute":"static",bottom:0,width:"100%",background:"rgba(0, 0, 0, 0.5)",paddingX:1,paddingBottom:.5,paddingTop:i?.5:0,marginTop:-.5},children:[t.jsx(o,{display:"flex",justifyContent:"center",alignItems:"center",children:t.jsx(c,{onClick:C,sx:{color:"white",padding:0},children:u?t.jsx(g,{sx:{fontSize:i?25:15}}):t.jsx(x,{sx:{fontSize:i?25:15}})})}),t.jsx(o,{display:"flex",justifyContent:"center",alignItems:"center",size:"grow",children:t.jsx(J,{value:F>0?v/F*100:0,onChange:y,onChangeCommitted:y,height:i?24:16,totalSeconds:F&&isFinite(F)?F:0,getHoverTitle:e=>F>0?(e=>{if(null==e||e<0||!isFinite(e))return"00:00";const t=Math.floor(e),r=Math.floor(t/60),o=t%60;return`${String(r).padStart(2,"0")}:${String(o).padStart(2,"0")}`})(e):"00:00"})}),t.jsx(o,{display:"flex",justifyContent:"center",alignItems:"center",children:t.jsx(c,{onClick:n,sx:{color:"white",padding:0},children:i?t.jsx(f,{sx:{fontSize:i?25:15}}):t.jsx(m,{sx:{fontSize:i?25:15}})})})]})]})}));K.displayName="VideoPlayer";const Q=({buttonsToShow:e=[],buttonHandlers:r={},buttonStates:o={},containerStyle:n={},direction:i="column",containerRef:a})=>t.jsxs(p,{sx:{position:"absolute",top:8,right:8,display:"flex",flexDirection:i,flexWrap:"wrap",gap:1,zIndex:1e3,...n},children:[e.includes(O.FAVOURITE)&&t.jsx(h,{...a?{slotProps:{popper:{container:a.current}}}:{},title:o.isFavourite?"Remove from favorites":"Add to favorites",arrow:!0,placement:"left",children:t.jsx(c,{onClick:e=>{e.stopPropagation(),o.isFavourite?r.removeFavourite():r.addFavourite()},sx:{height:27,width:27,padding:0,color:o.isFavourite?"yellow":"white",backgroundColor:"rgba(0, 0, 0, 0.5)",borderRadius:"50%","&:hover":{backgroundColor:"rgba(0, 0, 0, 0.7)"},"&.Mui-disabled":{backgroundColor:"rgba(0, 0, 0, 0.5)"}},disableRipple:!0,disabled:o.isFavouriteLoading,children:o.isFavouriteLoading?t.jsx(v,{sx:{color:"white"},size:18}):o.isFavourite?t.jsx(b,{sx:{height:18}}):t.jsx(F,{sx:{height:18}})})}),e.includes(O.SHARE)&&t.jsx(h,{...a?{slotProps:{popper:{container:a.current}}}:{},title:"Share this event",arrow:!0,placement:"left",children:t.jsx(c,{className:"icon-button",onClick:r.toggleShare,sx:{height:27,width:27,padding:0,color:"#fff",backgroundColor:"rgba(0, 0, 0, 0.5)",borderRadius:"50%","&:hover":{backgroundColor:"rgba(0, 0, 0, 0.7)"}},children:t.jsx(w,{sx:{height:18}})})}),e.includes(O.DOWNLOAD)&&!o.isDownloading&&t.jsx(h,{...a?{slotProps:{popper:{container:a.current}}}:{},title:"Download",arrow:!0,placement:"left",children:t.jsx(c,{onClick:e=>{e.stopPropagation(),r.downloadArtifact()},sx:{height:27,width:27,padding:0,color:"#fff",backgroundColor:"rgba(0, 0, 0, 0.5)",borderRadius:"50%","&:hover":{backgroundColor:"rgba(0, 0, 0, 0.7)"}},children:t.jsx(j,{sx:{height:18}})})}),e.includes(O.DOWNLOAD)&&o.isDownloading&&t.jsx(v,{sx:{fontSize:18,color:"white",filter:"drop-shadow(0px 2px 3px rgba(0,0,0,0.5))"}}),e.includes(O.FULLSCREEN)&&r.handleFullscreenOpen&&o.showFullscreenIconForMap&&t.jsx(h,{...a?{slotProps:{popper:{container:a.current}}}:{},title:"View Full Screen",arrow:!0,placement:"left",children:t.jsx(c,{onClick:r.handleFullscreenOpen,sx:{height:27,width:27,padding:0,color:"#fff",backgroundColor:"rgba(0, 0, 0, 0.5)",borderRadius:"50%","&:hover":{backgroundColor:"rgba(0, 0, 0, 0.7)"}},children:t.jsx(m,{sx:{height:18}})})}),e.includes(O.ARCHIVE)&&r.handleArchiveClick&&t.jsx(h,{...a?{slotProps:{popper:{container:a.current}}}:{},title:o.isArchiving?null:o.archived?"Unarchive":"Archive",arrow:!0,placement:"left",children:t.jsx(c,{size:"small",sx:{height:27,width:27,padding:0,color:"#fff",backgroundColor:"rgba(0,0,0,0.5)",borderRadius:"50%","&:hover":{backgroundColor:"rgba(0,0,0,0.7)"}},onClick:r.handleArchiveClick,disabled:o.isArchiving,children:o.isArchiving?t.jsx(v,{sx:{color:"white"},size:18}):o.archived?t.jsx(C,{sx:{height:18,color:"#F59E0B"}}):t.jsx(y,{sx:{height:18,color:"#F59E0B"}})})}),e.includes(O.GROUP_ARCHIVE)&&r.handleArchiveClick&&t.jsx(h,{...a?{slotProps:{popper:{container:a.current}}}:{},title:o.isArchiving?null:o.archived?o.isUnified?"Unarchive Multiple Detections":"Unarchive Group":o.isUnified?"Archive Multiple Detections":"Archive Group",arrow:!0,placement:"left",children:t.jsx(c,{size:"small",sx:{height:27,width:27,padding:0,color:"#fff",backgroundColor:"rgba(0,0,0,0.5)",borderRadius:"50%","&:hover":{backgroundColor:"rgba(0,0,0,0.7)"}},onClick:r.handleArchiveClick,disabled:o.isArchiving,children:o.isArchiving?t.jsx(v,{sx:{color:"white"},size:18}):t.jsx(p,{component:"img",src:"/icons/group_artifact.archive_icon.svg",alt:o.archived?o.isUnified?"Unarchive Unified Group":"Unarchive Group":o.isUnified?"Archive Unified Group":"Archive Group",sx:{height:18,width:18}})})})]});function Z({src:r,style:o,onLoadedData:n,onCurrentTimeChange:i,currentTime:a=0,fullscreenOpen:l=!1,isInFullScreen:d=!1,showFullscreenIcon:u=!1,setFullscreenOpen:h}){const p=e.useRef(null),[g,x]=e.useState(!1),[f,b]=e.useState(0),[F,w]=e.useState(0),[j,C]=e.useState(!0);e.useEffect((()=>{const e=p.current;if(!e)return;a&&(e.currentTime=a),d&&(e.play(),x(!0));const t=()=>{e.duration&&isFinite(e.duration)&&(b(e.currentTime/e.duration*100),w(e.duration)),i(e.currentTime),e.currentTime===e.duration&&x(!1)};return e.addEventListener("timeupdate",t),()=>e.removeEventListener("timeupdate",t)}),[]);const y=()=>{const e=p.current;e&&(e.paused?(e.play(),x(!0)):(e.pause(),x(!1)))};e.useEffect((()=>{if(!d){const e=p.current;if(!e)return;!e.paused&&l?(e.pause(),x(!1)):a>0&&e.paused&&!l&&(e.play(),e.currentTime=a,x(!0))}}),[l]);const R=e=>{const t=p.current,r=+e.target.value;t.currentTime=r/100*t.duration,b(r)};e.useEffect((()=>{const e=p.current;if(!e)return;const t=()=>C(!0),r=e=>{C(!1),n&&n(e)},o=()=>C(!0),i=()=>C(!1);return e.addEventListener("loadstart",t),e.addEventListener("loadeddata",r),e.addEventListener("waiting",o),e.addEventListener("playing",i),()=>{e.removeEventListener("loadstart",t),e.removeEventListener("loadeddata",r),e.removeEventListener("waiting",o),e.removeEventListener("playing",i)}}),[r]);const E={playerWrapper:{position:"relative",backgroundColor:"#000000",overflow:"hidden",width:"100%",display:"flex",alignItems:"center",justifyContent:"center"},controlsWrapper:{overflow:"hidden",background:"#FFFFFF4D",display:"flex",alignItems:"center",justifyContent:"space-between",paddingX:1},playButton:{color:"white",borderRadius:0,"&:hover":{color:B.palette.custom.mainBlue,backgroundColor:"#FFFFFF"}},sliderStyles:{color:B.palette.custom.mainBlue,padding:"4px 0",height:24,flexGrow:1,"& .MuiSlider-thumb":{width:8,height:28,borderRadius:0,backgroundColor:"#FFFFFF",transform:"translate(-50%, -50%)",transition:"all 0.5s ease !important","&:hover, &.Mui-focusVisible":{boxShadow:"none"}},"& .MuiSlider-track":{height:24,borderRadius:0,transition:"all 0.5s ease !important",color:B.palette.custom.mainBlue},"& .MuiSlider-rail":{height:24,borderRadius:0,opacity:.28,backgroundColor:"#FFFFFF4D"},"& .MuiSlider-mark":{height:24,width:4}},fullscreenButton:{color:"white",borderRadius:0,"&:hover":{color:B.palette.custom.mainBlue,backgroundColor:"#FFFFFF"}},loadingIndicator:{position:"absolute",inset:0,display:"flex",alignItems:"center",justifyContent:"center",color:B.palette.custom.mainBlue},centerPlayButton:{position:"absolute",top:"50%",left:"50%",transform:"translate(-50%, -50%)",backgroundColor:s(B.palette.custom.mainBlue,.7),color:"white",width:80,height:80,zIndex:2,display:"flex",alignItems:"center",justifyContent:"center","&:hover":{backgroundColor:B.palette.custom.mainBlue},boxShadow:"0px 0px 15px rgba(0, 0, 0, 0.3)"}};return t.jsxs("div",{style:{...E.playerWrapper,width:"100%",...o},children:[t.jsx("video",{ref:p,src:r,preload:"auto",style:{...o,width:"100%",objectFit:"contain"},onLoadedData:n,onClick:y}),j&&t.jsx("div",{style:E.loadingIndicator,children:t.jsx(v,{sx:{color:B.palette.custom.mainBlue},size:50})}),!g&&!j&&t.jsx(c,{sx:E.centerPlayButton,onClick:y,"aria-label":"play video",children:t.jsx(k,{sx:{fontSize:48}})}),t.jsxs("div",{style:{...E.controlsWrapper,position:"absolute",bottom:0,left:0,right:0,zIndex:1},onClick:e=>e.stopPropagation(),children:[t.jsx(c,{sx:E.playButton,onClick:y,children:g?t.jsx(S,{}):t.jsx(k,{})}),t.jsx(J,{value:f,onChange:(e,t)=>R({target:{value:t}}),onChangeCommitted:(e,t)=>R({target:{value:t}}),height:24,totalSeconds:F&&isFinite(F)?F:0,getHoverTitle:e=>F>0?(e=>{if(null==e||e<0||!isFinite(e))return"00:00";const t=Math.floor(e),r=Math.floor(t/60),o=t%60;return`${String(r).padStart(2,"0")}:${String(o).padStart(2,"0")}`})(e):"00:00",tooltipContainer:document.body}),u&&(l?t.jsx(c,{sx:E.fullscreenButton,onClick:()=>h(!l),children:t.jsx(A,{sx:{height:18}})}):t.jsx(c,{sx:E.fullscreenButton,onClick:()=>h(!l),children:t.jsx(m,{sx:{height:18}})}))]})]})}const ee=({isLoading:e,isFavourite:o,removeFavourite:n,addFavourite:i,toggleShare:a,downloadArtifact:s,open:l,onClose:d,mediaUrl:u,isImage:h,handleCurrentTimeChange:g=()=>{},currentTime:x=0,setFullscreenOpen:f})=>t.jsx(r,{open:l,onClose:d,"aria-labelledby":"fullscreen-media","aria-describedby":"fullscreen-media-description",children:t.jsxs(p,{sx:{position:"absolute",top:"50%",left:"50%",transform:"translate(-50%, -50%)",width:"90%",height:"90%",bgcolor:"#000",boxShadow:24,p:0,display:"flex",alignItems:"center",justifyContent:"center"},children:[t.jsx(c,{onClick:d,sx:{position:"absolute",top:8,right:8,zIndex:1,color:"white",backgroundColor:"rgba(0, 0, 0, 0.5)",transition:"all 0.3s",border:"1px solid #FFFFFF","&:hover":{backgroundColor:"#FFFFFF",color:"#000",border:"1px solid #000"}},children:t.jsx(R,{})}),h?t.jsx("img",{src:u,alt:"Fullscreen",style:{maxWidth:"100%",maxHeight:"100%",objectFit:"contain"}}):t.jsxs(p,{sx:{position:"relative",width:"100%",height:"100%",backgroundColor:"#000"},children:[void 0!==e&&!e&&t.jsx(Q,{buttonsToShow:[O.FAVOURITE,O.SHARE,O.DOWNLOAD],buttonHandlers:{addFavourite:i,removeFavourite:n,toggleShare:a,downloadArtifact:s},buttonStates:{isFavourite:o},containerStyle:{top:80,right:15}}),t.jsx(Z,{src:u,style:{maxWidth:"100%",maxHeight:"100%",objectFit:"contain",height:"100%",width:"100%"},onLoadedData:()=>()=>{},onCurrentTimeChange:g,currentTime:x,fullscreenOpen:l,isInFullScreen:!0,showFullscreenIcon:!0,setFullscreenOpen:f})]})]})}),te=({initialState:e,onClose:i,onConfirm:a,isArchive:s=!0})=>t.jsx(r,{open:e,onClose:i,children:t.jsx(M,{title:s?"Move to Archive":"Unarchive",onClose:i,headerPosition:"center",children:t.jsxs(o,{container:!0,flexDirection:"column",gap:2,width:{xs:300,sm:"auto"},children:[t.jsx(o,{children:t.jsx(n,{fontWeight:"100",children:s?"Are you sure you want to move to archive this event?":"Are you sure you want to unarchive this event?"})}),t.jsxs(o,{container:!0,gap:1,justifyContent:"center",children:[t.jsx(o,{justifyContent:"center",display:"flex",children:t.jsx(E,{variant:"contained",sx:{background:"#FFFFFF !important",color:B.palette.primary.main},onClick:i,children:"Cancel"})}),t.jsx(o,{justifyContent:"center",display:"flex",children:t.jsx(E,{variant:"contained",onClick:a,sx:{background:"#F59E0B !important"},children:s?"Archive":"Unarchive"})})]})]})})}),re=({thumbnailLink:o,originalLink:n,isImage:i,isBounding:a=!1,det_nbbox:s,showVideoModal:l=!1,style:d={},skeletonStyle:u={borderRadius:"8px",height:200},cardId:g,showFullscreenIcon:x=!1,showVideoThumbnail:f=!1,showArchiveButton:b=!1,showFlagButton:F=!0,showFullscreenIconForMap:w,onThumbnailClick:j,isArchived:C=!1,vesselId:y,direction:k,buttonsToShow:S,handleUnflagClick:A,flaggedArtifact:R,isGrouped:E=!1,groupArtifacts:M=[],isUnified:z=!1,unifiedArtifacts:B=[]})=>{const[X,J]=e.useState([]);e.useEffect((()=>{J(localStorage.getItem("favouritesArtifacts")?JSON.parse(localStorage.getItem("favouritesArtifacts")):[]);const e=e=>{"favouritesArtifacts"===e.detail.key&&J(e.detail.newValue?JSON.parse(e.detail.newValue):[])};return window.addEventListener("localStorageChange",e),()=>{window.removeEventListener("localStorageChange",e)}}),[]);const[re,oe]=e.useState(!1),[ne,ie]=e.useState(!1),[ae,se]=e.useState(!0),[le,ce]=e.useState(!0),[de,ue]=e.useState(!1),[he,pe]=e.useState(!1),[ge,xe]=e.useState(!1),fe=L.useRef(null),{user:me}=U(),[ve,be]=e.useState(0),[Fe,we]=e.useState(!1),[je,Ce]=e.useState(null),[ye,ke]=e.useState(!1),[Se,Ae]=e.useState(!1),Re=L.useRef(null),[Ee,Le]=e.useState(!1),[Ie,Te]=e.useState(!1),[De,Pe]=e.useState({left:0,top:0,width:0,height:0});e.useEffect((()=>{const e=X?.some((e=>e===g));xe(e),ce(!1)}),[g,X]),e.useEffect((()=>{Ae(C)}),[C]);const Me=e=>{e.stopPropagation(),oe((e=>!e))},ze=(e=0)=>{be(e)},Be=e=>{e.stopPropagation(),pe(!0)},Oe=e=>{e.stopPropagation(),j?j(e):Be(e)},Ue=async()=>{ie(!0),await _.post(`artifacts/${g}/download`,{},{responseType:"blob",timeout:12e4}).then(V).catch((e=>{})).finally((()=>{ie(!1)}))},He=async()=>{if(!g&&!me._id)return;ce(!0);const e={artifact_id:g};201===(await N.addFavouriteArtifact(e)).status&&xe(!0),ce(!1)},$e=async()=>{if(!g&&!me._id)return;ce(!0);const e={artifact_id:g};200===(await N.removeFavouriteArtifact(e)).status&&xe(!1),ce(!1)},We=async(e,t=!1)=>{e.stopPropagation(),t||we(!1),ke(!0);try{const e="archive"===je||t;if(E&&M?.length>0){const t=M.map((t=>{const r=e?"archive":"unarchive";return _.post(`/artifacts/${t._id}/${r}`)}));if(await Promise.all(t),y){const e=M.map((e=>G.deleteItem(y+"_artifact",e._id).catch((e=>{}))));await Promise.all(e)}}else if(z&&B?.length>0){const t=B.map((t=>{const r=e?"archive":"unarchive";return _.post(`/artifacts/${t._id}/${r}`)}));await Promise.all(t)}else e?await _.post(`/artifacts/${g}/archive`):await _.post(`/artifacts/${g}/unarchive`),y&&await G.deleteItem(y+"_artifact",g).catch((e=>{}))}catch(r){}finally{ke(!1)}},_e=async e=>{if(e.stopPropagation(),A)A(e);else{Te(!0);try{if(Ee)return await Y.unflagArtifact(g),void Le(!1);await Y.flagArtifact(g),Le(!0)}catch(t){}finally{Te(!1)}}};return e.useEffect((()=>{!i&&l&&se(!1)}),[l]),e.useEffect((()=>{(async()=>{if(g)try{const e=Y.isArtifactFlaggedByUser(g);Le(e)}catch(e){}})()}),[g,me,Y.userFlaggedArtifactIds.size,R]),t.jsxs(p,{position:"relative",sx:{width:"100%",height:"100%"},ref:Re,children:[ae&&i&&t.jsxs(t.Fragment,{children:[t.jsx(p,{sx:{display:"flex",justifyContent:"center",alignItems:"center",position:"absolute",top:0,left:0,width:"100%",height:"100%",zIndex:1},children:t.jsx(v,{})}),t.jsx(I,{variant:"rectangular",width:"100%",sx:{...u}})]}),i?t.jsxs(t.Fragment,{children:[t.jsx("img",{src:o,ref:fe,alt:"media",style:{width:"100%",height:"100%",objectFit:"cover",display:ae?"none":"block",...d},onLoad:e=>{se(!1);try{const t=Re.current;if(!t)return;const r=H({containerWidth:t.clientWidth,containerHeight:t.clientHeight,naturalWidth:e.target.naturalWidth,naturalHeight:e.target.naturalHeight,fitMode:d&&d.objectFit||"cover"});Pe(r);const o=()=>{const r=H({containerWidth:t.clientWidth,containerHeight:t.clientHeight,naturalWidth:e.target.naturalWidth,naturalHeight:e.target.naturalHeight,fitMode:d&&d.objectFit||"cover"});Pe(r)};window.addEventListener("resize",o),e.target.__qmwHandleResize=o}catch(t){}},onError:()=>se(!1)}),!ae&&a&&s&&t.jsx(p,{sx:{position:"absolute",top:0,left:0,width:"100%",height:"100%",pointerEvents:"none",zIndex:2},children:(()=>{const e=$({containerRef:Re,imageDisplayRect:De,det_nbbox:s});return t.jsx(p,{sx:{position:"absolute",border:"2px solid #00ff00",boxShadow:"0 0 0 1px rgba(0,0,0,0.4) inset",borderRadius:"2px",left:`${e.left}px`,top:`${e.top}px`,width:`${e.width}px`,height:`${e.height}px`}})})()}),!ae&&x&&!w&&t.jsx(h,{...Re?{slotProps:{popper:{container:Re.current}}}:{},title:"View Full Screen",arrow:!0,placement:"right",children:t.jsx(c,{onClick:Be,sx:{position:"absolute",bottom:8,right:8,height:27,width:27,padding:0,color:"#fff",backgroundColor:"rgba(0, 0, 0, 0.5)",borderRadius:"50%","&:hover":{backgroundColor:"rgba(0, 0, 0, 0.7)"}},children:t.jsx(m,{sx:{height:18}})})})]}):l?t.jsxs(t.Fragment,{children:[t.jsx(K,{ref:fe,src:n,onFullscreen:()=>ue(!0)}),t.jsx(r,{open:de,onClose:()=>ue(!1),children:t.jsx(p,{sx:{width:"100%",height:"100%",backgroundColor:"#000"},children:t.jsx(K,{src:n,isFullscreen:!0,onFullscreen:()=>ue(!1)})})})]}):f?t.jsxs(t.Fragment,{children:[t.jsx("img",{src:o,alt:"media",style:{width:"100%",height:"100%",objectFit:"cover",display:ae?"none":"block",...d},onLoad:()=>se(!1),onClick:Oe}),!ae&&t.jsx(p,{sx:{position:"absolute",top:0,left:0,width:"100%",height:"100%",display:"flex",alignItems:"center",justifyContent:"center",backgroundColor:"rgba(0, 0, 0, 0.3)",borderRadius:"8px",cursor:"pointer"},onClick:Oe,children:t.jsx(T,{sx:{fontSize:50,color:"white",filter:"drop-shadow(0px 2px 3px rgba(0,0,0,0.5))"}})})]}):t.jsx(t.Fragment,{children:t.jsx(Z,{src:n,style:{width:"100%",height:"100%",objectFit:"cover",...d},onLoadedData:()=>se(!1),onCurrentTimeChange:ze,currentTime:ve,fullscreenOpen:he,isInFullScreen:!1,showFullscreenIcon:!0,setFullscreenOpen:pe})}),!ae&&!C&&F&&_e&&t.jsx(h,{...Re?{slotProps:{popper:{container:Re.current}}}:{},title:Ie?null:Ee||A?"Remove flag":"Flag for cleanup and/or review",arrow:!0,placement:"right",children:t.jsx(c,{size:"small",sx:{position:"absolute",top:8,left:8,height:27,width:27,padding:0,color:"#fff",backgroundColor:Ee||A?"#E60000CC":"rgba(0,0,0,0.5)",borderRadius:"50%","&:hover":{backgroundColor:"rgba(0,0,0,0.7)"}},onClick:_e,disabled:Ie,children:Ie?t.jsx(v,{sx:{color:"white"},size:18}):Ee||A?t.jsx(D,{sx:{height:18}}):t.jsx(P,{sx:{height:18}})})}),!ae&&t.jsx(Q,{buttonsToShow:S||[O.FAVOURITE,O.SHARE,O.DOWNLOAD,...b?[E||z?O.GROUP_ARCHIVE:O.ARCHIVE]:[],...w?[O.FULLSCREEN]:[]],buttonHandlers:{addFavourite:He,removeFavourite:$e,toggleShare:Me,downloadArtifact:Ue,handleFullscreenOpen:Be,handleArchiveClick:e=>{e.stopPropagation(),Se?(Ce("unarchive"),we(!0)):We(e,!0)}},buttonStates:{isFavourite:ge,isFavouriteLoading:le,isDownloading:ne,showFullscreenIconForMap:w,archived:Se,isArchiving:ye,isUnified:z},direction:k,containerRef:Re}),t.jsx(q,{state:re,onClose:Me,shareLink:n||o,isImageLink:i,shareEventLink:g?`${W.VITE_API_URL}/dashboard/events/${g}`:""}),t.jsx(te,{initialState:Fe,onClose:e=>{e.stopPropagation(),we(!1)},onConfirm:e=>We(e),isArchive:"archive"===je}),he&&t.jsx(ee,{isLoading:ae,isFavourite:ge,removeFavourite:$e,addFavourite:He,toggleShare:Me,downloadArtifact:Ue,open:he,onClose:()=>{pe(!1)},mediaUrl:n,isImage:i,handleCurrentTimeChange:ze,currentTime:ve,showFullscreenIcon:!0,setFullscreenOpen:pe})]})};export{re as P};

import{r as e,j as r,G as s,T as t,W as o,L as n,a0 as a,a1 as i}from"./vendor-DvOQ6qlC.js";import{e as l}from"./index-BuG1pLrZ.js";import"./utils-guRmN1PB.js";import"./maps-R0vlfPHe.js";import"./charts-Bh3hGOgg.js";const c=()=>{const[c,d]=e.useState(""),[h,m]=e.useState(""),[u,x]=e.useState("error"),[p,j]=e.useState(!1);return r.jsxs(s,{container:!0,flexDirection:"column",maxWidth:591,gap:4,children:[r.jsx(s,{container:!0,flexDirection:"column",color:"#FFFFFF",children:r.jsx(s,{children:r.jsx(t,{variant:"h3",fontWeight:"bold",children:"Forgot Password"})})}),r.jsxs(s,{container:!0,flexDirection:"column",component:"form",onSubmit:async e=>{e.preventDefault(),m(""),j(!0);try{const e=await l.post("/users/forgot-password",{email:c},{meta:{showSnackbar:!1}});x(200===e.status?"success.main":"error"),m(e.data.message),j(!1)}catch(r){x("error"),m(r.response.data.message),j(!1)}},gap:4,children:[r.jsx(s,{children:r.jsx(o,{className:"input-login",type:"email",placeholder:"Enter your email",variant:"outlined",fullWidth:!0,value:c,onChange:e=>d(e.target.value),required:!0})}),r.jsx(s,{display:h?"block":"none",children:r.jsx(t,{color:u,children:h})}),r.jsx(s,{children:r.jsx(n,{className:"btn-login",type:"submit",variant:"contained",color:"primary",fullWidth:!0,disabled:p,endIcon:p&&r.jsx(a,{}),children:"Send Reset Link"})})]}),r.jsx(s,{color:"#FFFFFF",children:r.jsxs(t,{fontSize:"18px",lineHeight:"30px",fontWeight:"light",children:["Remembered your password?"," ",r.jsx(i,{href:"/login",color:"#FFFFFF",fontWeight:"bold",sx:{textDecoration:"none",":hover":{textDecoration:"underline"}},children:"Login"})]})})]})};export{c as default};

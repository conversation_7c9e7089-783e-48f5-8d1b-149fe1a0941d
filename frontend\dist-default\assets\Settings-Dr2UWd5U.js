import{r as e,j as t,G as o,a0 as i,T as r,am as a,ba as n,aI as s,bd as l,ac as c,bg as d,x as p,L as m}from"./vendor-DvOQ6qlC.js";import{e as h,u as x,t as u,K as F,c as f,E as g}from"./index-BuG1pLrZ.js";import{C as b}from"./ConfirmModal-inS1rlwZ.js";import{u as w}from"./AppHook-CeBj5aBe.js";import"./utils-guRmN1PB.js";import"./maps-R0vlfPHe.js";import"./charts-Bh3hGOgg.js";import"./ModalContainer-B8xU4Z17.js";const j=new class{async getLatestUserNotificationAlerts(){try{return await h.get("/notificationsAlerts/testing",{meta:{showSnackbar:!0}})}catch(e){return e.response.data}}};function S(){const{devMode:S,setDevMode:_,showIDs:k,setShowIDs:y}=w(),{user:C,fetchUser:M}=x(),[v,z]=e.useState(!1),[R,B]=e.useState(C?.date_time_format),[I,T]=e.useState(C?.home_port_filter_mode||"ONLY_NON_HOME_PORTS"),[W,D]=e.useState(!!C?.use_MGRS),[L,O]=e.useState(!1),[A,G]=e.useState({title:"",message:""}),[E,N]=e.useState(!1);e.useEffect((()=>{void 0!==C?.email_verification_enabled&&z(C.email_verification_enabled),B(C?.date_time_format),T(C?.home_port_filter_mode||"ONLY_NON_HOME_PORTS")}),[C]);const Y=()=>{z(C.email_verification_enabled)},P=e.useMemo((()=>v!==C?.email_verification_enabled||R!==C?.date_time_format||W!==!!C?.use_MGRS||I!==C?.home_port_filter_mode||k!==("true"===localStorage.getItem("showIDs"))),[v,C?.email_verification_enabled,C?.date_time_format,R,W,C?.use_MGRS,I,C?.home_port_filter_mode,k]);return C?t.jsxs(o,{container:!0,overflow:"auto",height:"100%",width:"100%",flexDirection:{xs:"row",lg:"column"},paddingX:{xs:3,md:10},paddingY:{xs:5,md:6},sx:{backgroundColor:u.palette.custom.darkBlue},children:[t.jsxs(o,{container:!0,direction:"column",gap:"30px",children:[t.jsxs(o,{children:[t.jsx(r,{variant:"h4",component:"h1",color:"#FFFFFF",children:"Profile Settings"}),t.jsx(r,{component:"h6",color:"#FFFFFF",fontSize:"18px",fontWeight:"400",children:"Update your profile."})]}),t.jsxs(o,{container:!0,direction:"column",gap:"30px",children:[t.jsxs(o,{container:!0,paddingBottom:"20px",borderBottom:u.palette.custom.borderColor,borderRadius:"10px",children:[t.jsxs(o,{size:{md:5,sm:12},children:[t.jsx(r,{component:"h6",color:"#FFFFFF",fontSize:"18px",fontWeight:"600",children:"Two Factor Authentication"}),t.jsx(r,{component:"p",color:"#FFFFFF",fontSize:"14px",fontWeight:"400",children:"You will have to enter OTP while logging into the account."})]}),t.jsx(o,{size:{md:"grow",sm:12},children:t.jsx(a,{enterDelay:300,title:C?.email?"":"Your Account is not linked with an email",placement:"bottom",children:t.jsx(n,{control:t.jsx(s,{checked:v,onChange:e=>{z(e.target.checked)},disabled:!C?.email,color:"primary",disableRipple:!0,sx:{height:"50px",width:"80px",borderRadius:"50px","& .MuiSwitch-switchBase":{padding:"15px 4px",transform:"translate(9px, -2px)"},"& .MuiSwitch-track":{backgroundColor:"#FFFFFF",height:"30px",borderRadius:"50px"},"& .Mui-checked+.MuiSwitch-track":{backgroundColor:u.palette.custom.mainBlue+" !important",opacity:"1 !important"},"& .Mui-checked.MuiSwitch-switchBase":{transform:"translate(36px, -2px)"},"& .MuiSwitch-thumb":{backgroundColor:"#FFFFFF",height:"28px",width:"28px"},"& .Mui-disabled":{opacity:.4},"& .Mui-disabled+.MuiSwitch-track":{opacity:"0.3 !important"}}}),label:"Enable Two Factor Authentication",sx:{"& .MuiFormControlLabel-label":{color:"#FFFFFF !important",fontSize:"18px",fontWeight:"400"}}})})})]}),t.jsxs(o,{container:!0,paddingBottom:"20px",borderBottom:u.palette.custom.borderColor,borderRadius:"10px",children:[t.jsxs(o,{size:{md:5,sm:12},children:[t.jsx(r,{component:"h6",color:"#FFFFFF",fontSize:"18px",fontWeight:"600",children:"Location Format"}),t.jsx(r,{component:"p",color:"#FFFFFF",fontSize:"14px",fontWeight:"400",children:"Choose your preferred location format."})]}),t.jsx(o,{size:{md:"grow",sm:12},children:t.jsx(n,{control:t.jsx(s,{checked:W,onChange:e=>{D(e.target.checked)},color:"primary",disableRipple:!0,sx:{height:"50px",width:"80px",borderRadius:"50px","& .MuiSwitch-switchBase":{padding:"15px 4px",transform:"translate(9px, -2px)"},"& .MuiSwitch-track":{backgroundColor:u.palette.custom.mainBlue+" !important",height:"30px",borderRadius:"50px",opacity:"1 !important"},"& .Mui-checked+.MuiSwitch-track":{backgroundColor:u.palette.custom.mainBlue+" !important",opacity:"1 !important"},"& .Mui-checked.MuiSwitch-switchBase":{transform:"translate(36px, -2px)"},"& .MuiSwitch-thumb":{backgroundColor:"#FFFFFF",height:"28px",width:"28px"},"& .Mui-disabled":{opacity:.4},"& .Mui-disabled+.MuiSwitch-track":{opacity:"0.3 !important"}}}),label:W?"MGRS":"Lat/Lng",sx:{"& .MuiFormControlLabel-label":{color:"#FFFFFF !important",fontSize:"18px",fontWeight:"400"}}})})]}),t.jsxs(o,{container:!0,paddingBottom:"20px",borderBottom:u.palette.custom.borderColor,borderRadius:"10px",children:[t.jsxs(o,{size:{md:5,sm:12},children:[t.jsx(r,{component:"h6",color:"#FFFFFF",fontSize:"18px",fontWeight:"600",children:"Date/ Time Format"}),t.jsx(r,{component:"p",color:"#FFFFFF",fontSize:"14px",fontWeight:"400",children:"Choose your preferred date/time format."})]}),t.jsx(o,{size:{md:"grow",sm:12},children:t.jsx(n,{control:t.jsx(l,{labelId:"duration-select-label",id:"duration-select",value:R??Object.keys(F)[0],onChange:e=>{B(e.target.value)},variant:"filled",children:Object.entries(F).map((([e,o])=>t.jsx(c,{value:e,children:o},e)))})})})]}),t.jsxs(o,{container:!0,paddingBottom:"20px",borderBottom:u.palette.custom.borderColor,borderRadius:"10px",children:[t.jsxs(o,{size:{md:5,sm:12},children:[t.jsx(r,{component:"h6",color:"#FFFFFF",fontSize:"18px",fontWeight:"600",children:"Home Port filtering"}),t.jsx(r,{component:"p",color:"#FFFFFF",fontSize:"14px",fontWeight:"400",children:"View Artifacts that are near or away from home port."})]}),t.jsx(o,{size:{md:"grow",sm:12},children:t.jsx(n,{control:t.jsx(l,{labelId:"duration-select-label",id:"duration-select",value:I??Object.keys(f.homePortsFilterModes)[0],onChange:e=>{T(e.target.value)},variant:"filled",children:Object.entries(f.homePortsFilterModes).map((([e,o])=>t.jsx(c,{value:e,children:e},e)))})})})]})]}),t.jsx(o,{item:!0,container:!0,flexDirection:"column",gap:"30px",display:C&&C.role_id===g.super_admin?"flex":"none",children:t.jsxs(o,{children:[t.jsx(r,{variant:"h4",component:"h1",color:"#FFFFFF",children:"Dashboard Settings"}),t.jsx(r,{component:"h6",color:"#FFFFFF",fontSize:"18px",fontWeight:"400",children:"Customize the behavior of the dashboard."})]})}),t.jsxs(o,{container:!0,display:!C||C.role_id!==g.super_admin&&C.role_id!==g.internal_admin?"none":"flex",paddingBottom:"20px",borderBottom:e=>e.palette.custom.borderColor,borderRadius:"10px",children:[t.jsxs(o,{size:{md:5,sm:12},children:[t.jsxs(r,{component:"h6",color:"#FFFFFF",fontSize:"18px",fontWeight:"600",children:["Developer Mode",t.jsx(a,{enterDelay:300,title:"Shows technical details throughout the app, such as stream names and unit IDs on the map, stream pages and other developer related information as well. This mode also unlocks (shows) non-production units on certain screens (i.e. stream sensors list)\r\n                                        In certain cases it might show some new buggy features that are hidden only for devMode (currently none have been hidden).\r\n                                        This option is only available for super admins and internal admins.",children:t.jsx(d,{sx:{color:u.palette.custom.mediumGrey,backgroundColor:p(u.palette.custom.offline,0),fontSize:"18px",cursor:"pointer",marginLeft:"10px"}})})]}),t.jsx(r,{component:"p",color:"#FFFFFF",fontSize:"14px",fontWeight:"400",children:"View technical details on the dashboard."})]}),t.jsx(o,{size:{md:"grow",sm:12},children:t.jsx(n,{control:t.jsx(s,{checked:S,onChange:e=>{return t=e.target.checked,_(t),void window.location.reload();var t},color:"primary",disableRipple:!0,sx:{height:"50px",width:"80px",borderRadius:"50px","& .MuiSwitch-switchBase":{padding:"15px 4px",transform:"translate(9px, -2px)"},"& .MuiSwitch-track":{backgroundColor:"#FFFFFF",height:"30px",borderRadius:"50px"},"& .Mui-checked+.MuiSwitch-track":{backgroundColor:e=>e.palette.custom.mainBlue+" !important",opacity:"1 !important"},"& .Mui-checked.MuiSwitch-switchBase":{transform:"translate(36px, -2px)"},"& .MuiSwitch-thumb":{backgroundColor:"#FFFFFF",height:"28px",width:"28px"},"& .Mui-disabled":{opacity:.4},"& .Mui-disabled+.MuiSwitch-track":{opacity:"0.3 !important"}}}),label:"Enable Developer Mode",sx:{"& .MuiFormControlLabel-label":{color:"#FFFFFF !important",fontSize:"18px",fontWeight:"400"}}})})]}),t.jsxs(o,{container:!0,paddingBottom:"20px",borderBottom:u.palette.custom.borderColor,borderRadius:"10px",display:!C||C.role_id!==g.super_admin&&C.role_id!==g.internal_admin?"none":"flex",children:[t.jsxs(o,{size:{md:5,sm:12},children:[t.jsxs(r,{component:"h6",color:"#FFFFFF",fontSize:"18px",fontWeight:"600",children:["Show IDs",t.jsx(a,{enterDelay:300,title:"Displays the vessel unit ID alongside the vessel name, but only in the stream vessel list.\r\n                                    This option is only available for super admins and internal admins.",children:t.jsx(d,{sx:{color:u.palette.custom.mediumGrey,backgroundColor:p(u.palette.custom.offline,.08),fontSize:"18px",cursor:"pointer",marginLeft:"10px"}})})]}),t.jsx(r,{component:"p",color:"#FFFFFF",fontSize:"14px",fontWeight:"400",children:"Toggle to display Unit IDs in the vessel list."})]}),t.jsx(o,{size:{md:5,sm:12},children:t.jsx(n,{control:t.jsx(s,{checked:k,onChange:e=>{return t=e.target.checked,y(t),void localStorage.setItem("showIDs",t);var t},color:"primary",disableRipple:!0,sx:{height:"50px",width:"80px",borderRadius:"50px","& .MuiSwitch-switchBase":{padding:"15px 4px",transform:"translate(9px, -2px)"},"& .MuiSwitch-track":{backgroundColor:"#FFFFFF",height:"30px",borderRadius:"50px"},"& .Mui-checked+.MuiSwitch-track":{backgroundColor:u.palette.custom.mainBlue+" !important",opacity:"1 !important"},"& .Mui-checked.MuiSwitch-switchBase":{transform:"translate(36px, -2px)"},"& .MuiSwitch-thumb":{backgroundColor:"#FFFFFF",height:"28px",width:"28px"},"& .Mui-disabled":{opacity:.4},"& .Mui-disabled+.MuiSwitch-track":{opacity:"0.3 !important"}}}),label:"Enable Show IDs",sx:{"& .MuiFormControlLabel-label":{color:"#FFFFFF !important",fontSize:"18px",fontWeight:"400"}}})})]}),t.jsxs(o,{item:!0,container:!0,display:C&&C.permissions.some((e=>"TEST_NOTIFICATION_ALERTS"===e.permission_name))?"flex":"none",paddingBottom:"20px",borderBottom:e=>e.palette.custom.borderColor,borderRadius:"10px",children:[t.jsxs(o,{size:{md:5,sm:12},children:[t.jsxs(r,{component:"h6",color:"#FFFFFF",fontSize:"18px",fontWeight:"600",children:["Get Test Notification Alerts",t.jsx(a,{enterDelay:300,title:"This is used to verify if the notification system is working correctly. It's mainly for testing the flow from the microservice server.\r\n                                    This will deliver some testing notifications to the user's email.\r\n                                    This option is only available for users with TEST_NOTIFICATION_ALERTS permission.",children:t.jsx(d,{sx:{color:u.palette.custom.mediumGrey,backgroundColor:p(u.palette.custom.offline,.08),fontSize:"18px",cursor:"pointer",marginLeft:"10px"}})})]}),t.jsx(r,{component:"p",color:"#FFFFFF",fontSize:"14px",fontWeight:"400",children:"Get the test notification alerts manually by clicking this button."})]}),t.jsx(o,{size:{md:"grow",sm:12},children:t.jsx(m,{variant:"contained",onClick:async()=>{N(!0);try{const e=await j.getLatestUserNotificationAlerts();return e.status,N(!1),e}catch(e){return N(!1),e.response.data}},disabled:E,sx:{color:"#FFFFFF",fontSize:"16px",padding:"5px 20px",borderRadius:"10px",backgroundColor:E?u.palette.grey[500]:u.palette.primary.blue,"&:hover":{backgroundColor:E?u.palette.grey[500]:u.palette.primary.dark}},children:E?"Loading...":"Get Alerts"})})]}),P&&t.jsxs(o,{container:!0,justifyContent:"flex-end",alignItems:"center",gap:2,sx:{position:"fixed",bottom:20,right:20,backgroundColor:p(u.palette.background.paper,.95),backdropFilter:"blur(10px)",padding:"12px 20px",borderRadius:"12px",boxShadow:u.shadows[8],border:`1px solid ${p(u.palette.primary.main,.2)}`,zIndex:1300,minWidth:"200px",animation:"slideInUp 0.3s ease-out","@keyframes slideInUp":{"0%":{transform:"translateY(100%)",opacity:0},"100%":{transform:"translateY(0)",opacity:1}}},children:[t.jsx(m,{variant:"outlined",onClick:Y,sx:{color:"#FFFFFF",borderColor:p("#FFFFFF",.3),fontSize:"14px",fontWeight:500,padding:"8px 24px",borderRadius:"8px",textTransform:"none",minWidth:"80px","&:hover":{borderColor:"#FFFFFF",backgroundColor:p("#FFFFFF",.08)}},children:"Cancel"}),t.jsx(m,{onClick:async()=>{if(v!==C?.email_verification_enabled){if(!(await(({title:e,message:t})=>new Promise((o=>{G({title:e,message:t,onConfirm:()=>{o(!0),O(!1)},onCancel:()=>{o(!1),O(!1)}}),O(!0)})))({title:"Confirm",message:t.jsxs(t.Fragment,{children:["Are you sure you want to ",t.jsx("b",{children:v?"Enable":"Disable"})," the Two Factor Authentication."]})})))return void Y();h.patch("/users/userEmailVerification",null,{params:{email_verification_enabled:v}}).then((()=>M().catch(console.error))).catch(console.error)}if(R!==C?.date_time_format||W!==!!C?.use_MGRS||I!==C?.home_port_filter_mode){const e={};W!==C?.use_MGRS&&(e.use_MGRS=W),R!==C?.date_time_format&&(e.date_time_format=R);const t=I!==C?.home_port_filter_mode;t&&(e.home_port_filter_mode=I),h.patch("/users/updateSettings",null,{params:e}).then((()=>{M().catch(console.error),t&&window.location.reload()})).catch(console.error)}k!==("true"===localStorage.getItem("showIDs"))&&localStorage.setItem("showIDs",k)},variant:"contained",sx:{fontSize:"14px",fontWeight:600,padding:"8px 24px",borderRadius:"8px",textTransform:"none",minWidth:"80px",backgroundColor:u.palette.custom.mainBlue,color:"#FFFFFF",boxShadow:`0 4px 12px ${p(u.palette.custom.mainBlue,.3)}`,"&:hover":{backgroundColor:"#2c5cc5",boxShadow:`0 6px 16px ${p(u.palette.custom.mainBlue,.4)}`,transform:"translateY(-1px)"},"&:active":{transform:"translateY(0)"}},children:"Save Changes"})]})]}),t.jsx(b,{title:A.title,message:A.message,initialState:L,onClose:A.onCancel,onConfirm:A.onConfirm})]}):t.jsx(o,{container:!0,justifyContent:"center",alignItems:"center",height:"100%",sx:{backgroundColor:u.palette.custom.darkBlue},children:t.jsx(i,{size:30,sx:{color:"white"}})})}export{S as default};

import{V as e,r as t,j as n,bF as r,G as o,K as a,a0 as i,ap as s,T as l,Y as c,b3 as d,x as u,c6 as p,R as h,am as f,c7 as m,a as g,a5 as v,aM as y,bB as x,L as w,bb as b,bL as D,b5 as C,be as S,W as k,bI as _,b2 as M,c8 as E,X as F,bO as T,bw as j,ay as P,ag as R,af as I,aR as A}from"./vendor-DvOQ6qlC.js";import{u as N}from"./AppHook-DfcCRIWI.js";import{u as L,p as O,d as Y,t as H,a as W,b as V,o as B,m as z,e as K,N as Q,O as G,P as U,l as q,h as X}from"./index-C0IC_AUQ.js";import{M as Z}from"./ModalContainer-ChSAv7kt.js";import{P as $}from"./PreviewMedia-BRbCWc-a.js";import{u as J}from"./VesselInfoHook-CSuF_4V0.js";import{i as ee,c as te,d as ne,e as re,g as oe,f as ae,h as ie,j as se,k as le,l as ce,p as de,t as ue,m as pe,n as he,o as fe,q as me,u as ge,v as ve,w as ye,x as xe,y as we,z as be,A as De,B as Ce,C as Se,D as ke,E as _e,G as Me,H as Ee,I as Fe,J as Te,K as je,L as Pe,M as Re,N as Ie,O as Ae,P as Ne,Q as Le,R as Oe,S as Ye,T as He,U as We,V as Ve,W as Be,X as ze,Y as Ke,Z as Qe,_ as Ge,$ as Ue,a0 as qe,a1 as Xe,a2 as Ze,a3 as $e,a4 as Je,a5 as et,a6 as tt,a7 as nt,a8 as rt,a9 as ot,aa as at,ab as it}from"./utils-guRmN1PB.js";import{M as st}from"./MultiSelect-D8-gMvUK.js";import{V as lt}from"./index.esm-CACUSHDd.js";import{u as ct}from"./GroupRegionHook-Cfdf5fGr.js";import{a as dt}from"./ArtifactFlag.controller-BbMgJwBE.js";import"./maps-R0vlfPHe.js";import"./charts-Bh3hGOgg.js";function ut({showDetailModal:m,setShowDetailModal:g,selectedCard:v,setSelectedCard:y,id:x}){const{screenSize:w}=N(),{user:b}=L(),{vesselInfo:D}=J(),C=e(),[S,k]=t.useState(null),[_,M]=t.useState(v?.currentGroupIndex||0),[E,F]=t.useState(0),[T,j]=t.useState(!0),[P,R]=t.useState(!1),[I,A]=t.useState(new Set),[B,z]=t.useState(!1),[K,Q]=t.useState(null),G=b?.hasPermissions([O.manageArtifacts]),U=v?.isGroup&&v?.groupArtifacts?.length>1,q=U?v.groupArtifacts.length:1,X=t.useMemo((()=>U&&v.groupArtifacts[_]||v),[v,_,U]),ee=Array.isArray(X?.duplications)&&X.duplications.length>=1,te=ee?X.duplications.length+1:0,ne=t.useMemo((()=>{if(!ee||0===E)return X;const e=E-1,t=X?.duplications?.[e];return t||X}),[X,ee,E]),re=t.useMemo((()=>D.find((e=>e.vessel_id===ne?.onboard_vessel_id))),[D,ne?.onboard_vessel_id]),oe=ne?.location?.coordinates&&Y(ne.location.coordinates,!!b?.use_MGRS),ae=t.useCallback((()=>{_>0&&(R(!1),z(!1),M(_-1),F(0))}),[_]),ie=t.useCallback((()=>{_<q-1&&(R(!1),z(!1),M(_+1),F(0))}),[_,q]),se=()=>{K&&(K.abort(),Q(null)),y(null),g(!1),M(0),R(!1),z(!1),A(new Set)},le=t.useCallback((()=>{E>0&&F(E-1)}),[E]),ce=t.useCallback((()=>{E<te-1&&F(E+1)}),[E,te]),de=t.useCallback((async(e,t)=>{if(e)for(let r=0;r<e.length;r++){if(t.signal.aborted)return;const o=e[r];if(!o||I.has(o._id))continue;if(o.video_path)continue;const a=o.image_url;if(a)try{await new Promise(((e,n)=>{const r=new Image,i=()=>{r.onload=null,r.onerror=null};if(r.onload=()=>{i(),A((e=>new Set(e).add(o._id))),e()},r.onerror=()=>{i(),n(new Error("Failed to preload image"))},t.signal.aborted)return i(),void n(new Error("Preloading cancelled"));r.src=a}))}catch(n){}}}),[I]);t.useEffect((()=>{if(!B||!m||!U)return;K&&K.abort();const e=new AbortController;Q(e);const t=v?.groupArtifacts?.filter(((e,t)=>t!==_&&!e.video_path))||[];return t.length>0&&de(t,e),()=>{e.abort()}}),[B,m,U,v?.groupArtifacts,_,de]);t.useEffect((()=>{if(!X)return;const e=ee?X:ne;R(!1),z(!1);const t=Boolean(e.video_path),n=e.image_url,r=e.video_url;if(t)return k(r),j(!1),void z(!0);if(S===n)return j(!1),void z(!0);const o=new Image,a=()=>{k(n),j(!1),R(!1)};return o.onload=a,o.onerror=()=>{j(!1),R(!0)},j(!0),o.src=n,(o.complete||o.naturalWidth>0)&&a(),()=>{o.onload=null,o.onerror=null}}),[X]),t.useEffect((()=>{void 0!==v?.currentGroupIndex&&(M(v.currentGroupIndex),F(0))}),[v]),t.useEffect((()=>{F(0)}),[X?._id]);const ue=[{label:"Location",value:oe},{label:"Category",value:ne?.super_category||"Unspecified category"},{label:"Sub Category",value:ne?.category},{label:"Weapons",value:ne?.weapons},{label:"Size",value:ne?.size},{label:"Color",value:ne?.color},{label:"Imo Number",value:ne?.imo_number},{label:"Flag",value:ne?.country_flag},{label:"Detected Country",value:ne?.home_country},{label:"Orientation",value:ne?.vessel_orientation},{label:"Bearing Angle",value:ne?.true_bearing?`${Number(ne.true_bearing).toFixed(2)}°`:void 0},{label:"Features",value:ne?.vessel_features},{label:"Text Detected",value:Array.isArray(ne?.text_extraction)&&ne.text_extraction.length>0?ne.text_extraction.map((e=>e.text)).slice(0,5).join(", "):null},{label:"Description",value:ne?.others}],pe=["Text Detected","Description","Features"];return n.jsx(r,{open:Boolean(m),onClose:se,children:n.jsx(Z,{title:"Event Details "+(ee?"(Multiple Vessels)":""),onClose:se,showDivider:!0,children:n.jsxs(o,{container:!0,gap:1,minWidth:{xs:300,sm:500},maxWidth:800,children:[n.jsxs(o,{size:12,position:"relative",minHeight:"300px",children:[T&&!S&&n.jsxs(n.Fragment,{children:[n.jsx(a,{sx:{display:"flex",justifyContent:"center",alignItems:"center",position:"absolute",top:0,left:0,width:"100%",height:"300px",zIndex:1,borderRadius:2},children:n.jsx(i,{})}),n.jsx(s,{variant:"rectangular",width:"100%",height:"300px",sx:{borderRadius:2}})]}),T&&S&&n.jsx(a,{sx:{display:"flex",justifyContent:"center",alignItems:"center",position:"absolute",top:"50%",left:"50%",transform:"translate(-50%, -50%)",zIndex:3,backgroundColor:"rgba(0, 0, 0, 0.7)",borderRadius:"50%",padding:"12px"},children:n.jsx(i,{size:24,sx:{color:"white"}})}),P&&!T&&n.jsx(a,{height:"300px",display:"flex",alignItems:"center",justifyContent:"center",backgroundColor:"rgba(0,0,0,0.2)",borderRadius:2,children:n.jsx(l,{color:"text.secondary",variant:"body1",children:"Failed to load media"})}),S&&!P&&ne&&n.jsx(a,{height:"300px",children:n.jsx($,{thumbnailLink:S,originalLink:S,cardId:x||X._id,isImage:!ne.video_path,style:{borderRadius:8,height:300,objectFit:"contain",backgroundColor:"#000"},skeletonStyle:{height:300,width:"100%",borderRadius:2},showFullscreenIconForMap:!ne.video_path,showArchiveButton:G,isGrouped:U,groupArtifacts:v?.groupArtifacts,isBounding:ee,det_nbbox:ne?.det_nbbox,isUnified:ee,unifiedArtifacts:ee?[X,...X?.duplications||[]]:[]})})]}),U&&n.jsx(o,{size:12,container:!0,justifyContent:"center",children:n.jsxs(a,{sx:{display:"flex",alignItems:"center",padding:"4px 8px",gap:1},onClick:e=>e.stopPropagation(),children:[n.jsx(c,{size:"small",onClick:ae,disabled:0===_,sx:{color:"white",padding:"4px",background:u(H.palette.custom.borderColor,.8)+" !important","&:disabled":{color:"rgba(255,255,255,0.3)"}},children:n.jsx(d,{fontSize:"small"})}),n.jsxs(l,{variant:"caption",sx:{color:"white",fontWeight:500,minWidth:"40px",textAlign:"center",padding:"5px 17px",borderRadius:"100px",background:u(H.palette.primary.main,.8)},children:[String(_+1).padStart(2,"0"),"/",String(q).padStart(2,"0")]}),n.jsx(c,{size:"small",onClick:ie,disabled:_===q-1,sx:{color:"white",padding:"4px",background:u(H.palette.custom.borderColor,.8)+" !important","&:disabled":{color:"rgba(255,255,255,0.3)"}},children:n.jsx(p,{fontSize:"small"})})]})}),n.jsxs(o,{container:!0,sx:{maxHeight:"400px",overflow:"auto"},children:[n.jsxs(o,{display:"flex",justifyContent:w.xs?"flex-start":"space-between",alignItems:w.xs?"flex-start":"center",paddingX:1,flexDirection:w.xs?"column":"row",size:12,children:[w.xs&&n.jsx(l,{fontSize:{xs:"16px",sm:"20px"},fontWeight:500,color:H.palette.custom.mainBlue,children:"Name"}),n.jsx(l,{fontSize:{xs:"16px",sm:"20px"},fontWeight:500,children:re?.name||re?.vessel_id||"Unknown Vessel"}),w.xs&&n.jsx(l,{fontSize:{xs:"16px",sm:"20px"},fontWeight:500,color:H.palette.custom.mainBlue,children:"Timestamp"}),n.jsx(l,{fontSize:{xs:"16px",sm:"20px"},fontWeight:500,children:W(ne?.timestamp).format(V.dateTimeFormat(b,{exclude_seconds:!0}))})]}),ue.map((({label:e,value:t},r)=>n.jsx(h.Fragment,{children:n.jsxs(o,{display:"flex",alignItems:{xs:"flex-start",sm:r%2==0||pe.includes(e)?"flex-start":"flex-end"},paddingX:1,flexDirection:"column",size:{xs:12,sm:pe.includes(e)?12:5.9},children:[n.jsxs(l,{fontSize:"16px",fontWeight:500,color:H.palette.custom.mainBlue,sx:{display:"flex",alignItems:"center",gap:"6px"},children:[e,"Bearing Angle"===e&&n.jsx(f,{title:"Angle measured clockwise between the True North and the Target as observed from own vessel",children:n.jsx("img",{src:"/icons/info_icon.svg"})})]}),n.jsx(l,{fontSize:"16px",fontWeight:500,onClick:()=>"Location"===e?void(ne?.location?.coordinates&&(C(`/dashboard/map?artifact=${ne._id}`),se())):null,sx:{cursor:"Location"===e?"pointer":"default",color:"Location"===e?"#007bff":"inherit",textDecoration:"Location"===e?"underline":"none",userSelect:"none","&:hover":"Location"===e?{color:"#0056b3",textDecoration:"underline"}:{}},title:"Location"===e?"Click to view on map":"",textAlign:r%2==0||pe.includes(e)?"left":"right",children:t??"--"})]})},r)))]}),ee&&n.jsx(o,{size:12,container:!0,justifyContent:"center",children:n.jsxs(a,{sx:{display:"flex",alignItems:"center",padding:"4px 8px",gap:1},onClick:e=>e.stopPropagation(),children:[n.jsx(c,{size:"small",onClick:le,disabled:0===E,sx:{color:"white",padding:"4px",background:u(H.palette.custom.borderColor,.8)+" !important","&:disabled":{color:"rgba(255,255,255,0.3)"}},children:n.jsx(d,{fontSize:"small"})}),n.jsxs(l,{variant:"caption",sx:{color:"white",fontWeight:500,minWidth:"40px",textAlign:"center",padding:"5px 17px",borderRadius:"100px",background:u(H.palette.primary.main,.8)},children:["Vessel Detections ",String(E+1).padStart(2,"0"),"/",String(te).padStart(2,"0")]}),n.jsx(c,{size:"small",onClick:ce,disabled:E===te-1,sx:{color:"white",padding:"4px",background:u(H.palette.custom.borderColor,.8)+" !important","&:disabled":{color:"rgba(255,255,255,0.3)"}},children:n.jsx(p,{fontSize:"small"})})]})})]})})})}function pt(){return"undefined"!=typeof window}function ht(e){return gt(e)?(e.nodeName||"").toLowerCase():"#document"}function ft(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function mt(e){var t;return null==(t=(gt(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function gt(e){return!!pt()&&(e instanceof Node||e instanceof ft(e).Node)}function vt(e){return!!pt()&&(e instanceof Element||e instanceof ft(e).Element)}function yt(e){return!!pt()&&(e instanceof HTMLElement||e instanceof ft(e).HTMLElement)}function xt(e){return!(!pt()||"undefined"==typeof ShadowRoot)&&(e instanceof ShadowRoot||e instanceof ft(e).ShadowRoot)}function wt(e){const{overflow:t,overflowX:n,overflowY:r,display:o}=_t(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(o)}function bt(e){return["table","td","th"].includes(ht(e))}function Dt(e){return[":popover-open",":modal"].some((t=>{try{return e.matches(t)}catch(n){return!1}}))}function Ct(e){const t=St(),n=vt(e)?_t(e):e;return["transform","translate","scale","rotate","perspective"].some((e=>!!n[e]&&"none"!==n[e]))||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||["transform","translate","scale","rotate","perspective","filter"].some((e=>(n.willChange||"").includes(e)))||["paint","layout","strict","content"].some((e=>(n.contain||"").includes(e)))}function St(){return!("undefined"==typeof CSS||!CSS.supports)&&CSS.supports("-webkit-backdrop-filter","none")}function kt(e){return["html","body","#document"].includes(ht(e))}function _t(e){return ft(e).getComputedStyle(e)}function Mt(e){return vt(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function Et(e){if("html"===ht(e))return e;const t=e.assignedSlot||e.parentNode||xt(e)&&e.host||mt(e);return xt(t)?t.host:t}function Ft(e){const t=Et(e);return kt(t)?e.ownerDocument?e.ownerDocument.body:e.body:yt(t)&&wt(t)?t:Ft(t)}function Tt(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);const o=Ft(e),a=o===(null==(r=e.ownerDocument)?void 0:r.body),i=ft(o);if(a){const e=jt(i);return t.concat(i,i.visualViewport||[],wt(o)?o:[],e&&n?Tt(e):[])}return t.concat(o,Tt(o,[],n))}function jt(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}const Pt=Math.min,Rt=Math.max,It=Math.round,At=Math.floor,Nt=e=>({x:e,y:e}),Lt={left:"right",right:"left",bottom:"top",top:"bottom"},Ot={start:"end",end:"start"};function Yt(e,t,n){return Rt(e,Pt(t,n))}function Ht(e,t){return"function"==typeof e?e(t):e}function Wt(e){return e.split("-")[0]}function Vt(e){return e.split("-")[1]}function Bt(e){return"y"===e?"height":"width"}function zt(e){return["top","bottom"].includes(Wt(e))?"y":"x"}function Kt(e){return"x"===zt(e)?"y":"x"}function Qt(e){return e.replace(/start|end/g,(e=>Ot[e]))}function Gt(e){return e.replace(/left|right|bottom|top/g,(e=>Lt[e]))}function Ut(e){return"number"!=typeof e?function(e){return{top:0,right:0,bottom:0,left:0,...e}}(e):{top:e,right:e,bottom:e,left:e}}function qt(e){const{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}var Xt="undefined"!=typeof document?t.useLayoutEffect:t.useEffect;const Zt={...m}.useInsertionEffect||(e=>e());function $t(e,t,n){let{reference:r,floating:o}=e;const a=zt(t),i=Kt(t),s=Bt(i),l=Wt(t),c="y"===a,d=r.x+r.width/2-o.width/2,u=r.y+r.height/2-o.height/2,p=r[s]/2-o[s]/2;let h;switch(l){case"top":h={x:d,y:r.y-o.height};break;case"bottom":h={x:d,y:r.y+r.height};break;case"right":h={x:r.x+r.width,y:u};break;case"left":h={x:r.x-o.width,y:u};break;default:h={x:r.x,y:r.y}}switch(Vt(t)){case"start":h[i]-=p*(n&&c?-1:1);break;case"end":h[i]+=p*(n&&c?-1:1)}return h}function Jt(e){const t=_t(e);let n=parseFloat(t.width)||0,r=parseFloat(t.height)||0;const o=yt(e),a=o?e.offsetWidth:n,i=o?e.offsetHeight:r,s=It(n)!==a||It(r)!==i;return s&&(n=a,r=i),{width:n,height:r,$:s}}function en(e){return vt(e)?e:e.contextElement}function tn(e){const t=en(e);if(!yt(t))return Nt(1);const n=t.getBoundingClientRect(),{width:r,height:o,$:a}=Jt(t);let i=(a?It(n.width):n.width)/r,s=(a?It(n.height):n.height)/o;return i&&Number.isFinite(i)||(i=1),s&&Number.isFinite(s)||(s=1),{x:i,y:s}}const nn=Nt(0);function rn(e){const t=ft(e);return St()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:nn}function on(e,t,n,r){void 0===t&&(t=!1),void 0===n&&(n=!1);const o=e.getBoundingClientRect(),a=en(e);let i=Nt(1);t&&(r?vt(r)&&(i=tn(r)):i=tn(e));const s=function(e,t,n){return void 0===t&&(t=!1),!(!n||t&&n!==ft(e))&&t}(a,n,r)?rn(a):Nt(0);let l=(o.left+s.x)/i.x,c=(o.top+s.y)/i.y,d=o.width/i.x,u=o.height/i.y;if(a){const e=ft(a),t=r&&vt(r)?ft(r):r;let n=e,o=jt(n);for(;o&&r&&t!==n;){const e=tn(o),t=o.getBoundingClientRect(),r=_t(o),a=t.left+(o.clientLeft+parseFloat(r.paddingLeft))*e.x,i=t.top+(o.clientTop+parseFloat(r.paddingTop))*e.y;l*=e.x,c*=e.y,d*=e.x,u*=e.y,l+=a,c+=i,n=ft(o),o=jt(n)}}return qt({width:d,height:u,x:l,y:c})}function an(e,t){const n=Mt(e).scrollLeft;return t?t.left+n:on(mt(e)).left+n}function sn(e,t,n){void 0===n&&(n=!1);const r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:an(e,r)),y:r.top+t.scrollTop}}function ln(e,t,n){let r;if("viewport"===t)r=function(e,t){const n=ft(e),r=mt(e),o=n.visualViewport;let a=r.clientWidth,i=r.clientHeight,s=0,l=0;if(o){a=o.width,i=o.height;const e=St();(!e||e&&"fixed"===t)&&(s=o.offsetLeft,l=o.offsetTop)}return{width:a,height:i,x:s,y:l}}(e,n);else if("document"===t)r=function(e){const t=mt(e),n=Mt(e),r=e.ownerDocument.body,o=Rt(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),a=Rt(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight);let i=-n.scrollLeft+an(e);const s=-n.scrollTop;return"rtl"===_t(r).direction&&(i+=Rt(t.clientWidth,r.clientWidth)-o),{width:o,height:a,x:i,y:s}}(mt(e));else if(vt(t))r=function(e,t){const n=on(e,!0,"fixed"===t),r=n.top+e.clientTop,o=n.left+e.clientLeft,a=yt(e)?tn(e):Nt(1);return{width:e.clientWidth*a.x,height:e.clientHeight*a.y,x:o*a.x,y:r*a.y}}(t,n);else{const n=rn(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return qt(r)}function cn(e,t){const n=Et(e);return!(n===t||!vt(n)||kt(n))&&("fixed"===_t(n).position||cn(n,t))}function dn(e,t,n){const r=yt(t),o=mt(t),a="fixed"===n,i=on(e,!0,a,t);let s={scrollLeft:0,scrollTop:0};const l=Nt(0);if(r||!r&&!a)if(("body"!==ht(t)||wt(o))&&(s=Mt(t)),r){const e=on(t,!0,a,t);l.x=e.x+t.clientLeft,l.y=e.y+t.clientTop}else o&&(l.x=an(o));const c=!o||r||a?Nt(0):sn(o,s);return{x:i.left+s.scrollLeft-l.x-c.x,y:i.top+s.scrollTop-l.y-c.y,width:i.width,height:i.height}}function un(e){return"static"===_t(e).position}function pn(e,t){if(!yt(e)||"fixed"===_t(e).position)return null;if(t)return t(e);let n=e.offsetParent;return mt(e)===n&&(n=n.ownerDocument.body),n}function hn(e,t){const n=ft(e);if(Dt(e))return n;if(!yt(e)){let t=Et(e);for(;t&&!kt(t);){if(vt(t)&&!un(t))return t;t=Et(t)}return n}let r=pn(e,t);for(;r&&bt(r)&&un(r);)r=pn(r,t);return r&&kt(r)&&un(r)&&!Ct(r)?n:r||function(e){let t=Et(e);for(;yt(t)&&!kt(t);){if(Ct(t))return t;if(Dt(t))return null;t=Et(t)}return null}(e)||n}const fn={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e;const a="fixed"===o,i=mt(r),s=!!t&&Dt(t.floating);if(r===i||s&&a)return n;let l={scrollLeft:0,scrollTop:0},c=Nt(1);const d=Nt(0),u=yt(r);if((u||!u&&!a)&&(("body"!==ht(r)||wt(i))&&(l=Mt(r)),yt(r))){const e=on(r);c=tn(r),d.x=e.x+r.clientLeft,d.y=e.y+r.clientTop}const p=!i||u||a?Nt(0):sn(i,l,!0);return{width:n.width*c.x,height:n.height*c.y,x:n.x*c.x-l.scrollLeft*c.x+d.x+p.x,y:n.y*c.y-l.scrollTop*c.y+d.y+p.y}},getDocumentElement:mt,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e;const a=[..."clippingAncestors"===n?Dt(t)?[]:function(e,t){const n=t.get(e);if(n)return n;let r=Tt(e,[],!1).filter((e=>vt(e)&&"body"!==ht(e))),o=null;const a="fixed"===_t(e).position;let i=a?Et(e):e;for(;vt(i)&&!kt(i);){const t=_t(i),n=Ct(i);n||"fixed"!==t.position||(o=null),(a?!n&&!o:!n&&"static"===t.position&&o&&["absolute","fixed"].includes(o.position)||wt(i)&&!n&&cn(e,i))?r=r.filter((e=>e!==i)):o=t,i=Et(i)}return t.set(e,r),r}(t,this._c):[].concat(n),r],i=a[0],s=a.reduce(((e,n)=>{const r=ln(t,n,o);return e.top=Rt(r.top,e.top),e.right=Pt(r.right,e.right),e.bottom=Pt(r.bottom,e.bottom),e.left=Rt(r.left,e.left),e}),ln(t,i,o));return{width:s.right-s.left,height:s.bottom-s.top,x:s.left,y:s.top}},getOffsetParent:hn,getElementRects:async function(e){const t=this.getOffsetParent||hn,n=this.getDimensions,r=await n(e.floating);return{reference:dn(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){const{width:t,height:n}=Jt(e);return{width:t,height:n}},getScale:tn,isElement:vt,isRTL:function(e){return"rtl"===_t(e).direction}};function mn(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}function gn(e,t,n,r){void 0===r&&(r={});const{ancestorScroll:o=!0,ancestorResize:a=!0,elementResize:i="function"==typeof ResizeObserver,layoutShift:s="function"==typeof IntersectionObserver,animationFrame:l=!1}=r,c=en(e),d=o||a?[...c?Tt(c):[],...Tt(t)]:[];d.forEach((e=>{o&&e.addEventListener("scroll",n,{passive:!0}),a&&e.addEventListener("resize",n)}));const u=c&&s?function(e,t){let n,r=null;const o=mt(e);function a(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return function i(s,l){void 0===s&&(s=!1),void 0===l&&(l=1),a();const c=e.getBoundingClientRect(),{left:d,top:u,width:p,height:h}=c;if(s||t(),!p||!h)return;const f={rootMargin:-At(u)+"px "+-At(o.clientWidth-(d+p))+"px "+-At(o.clientHeight-(u+h))+"px "+-At(d)+"px",threshold:Rt(0,Pt(1,l))||1};let m=!0;function g(t){const r=t[0].intersectionRatio;if(r!==l){if(!m)return i();r?i(!1,r):n=setTimeout((()=>{i(!1,1e-7)}),1e3)}1!==r||mn(c,e.getBoundingClientRect())||i(),m=!1}try{r=new IntersectionObserver(g,{...f,root:o.ownerDocument})}catch(v){r=new IntersectionObserver(g,f)}r.observe(e)}(!0),a}(c,n):null;let p,h=-1,f=null;i&&(f=new ResizeObserver((e=>{let[r]=e;r&&r.target===c&&f&&(f.unobserve(t),cancelAnimationFrame(h),h=requestAnimationFrame((()=>{var e;null==(e=f)||e.observe(t)}))),n()})),c&&!l&&f.observe(c),f.observe(t));let m=l?on(e):null;return l&&function t(){const r=on(e);m&&!mn(m,r)&&n();m=r,p=requestAnimationFrame(t)}(),n(),()=>{var e;d.forEach((e=>{o&&e.removeEventListener("scroll",n),a&&e.removeEventListener("resize",n)})),null==u||u(),null==(e=f)||e.disconnect(),f=null,l&&cancelAnimationFrame(p)}}const vn=function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;const{x:o,y:a,placement:i,middlewareData:s}=t,l=await async function(e,t){const{placement:n,platform:r,elements:o}=e,a=await(null==r.isRTL?void 0:r.isRTL(o.floating)),i=Wt(n),s=Vt(n),l="y"===zt(n),c=["left","top"].includes(i)?-1:1,d=a&&l?-1:1,u=Ht(t,e);let{mainAxis:p,crossAxis:h,alignmentAxis:f}="number"==typeof u?{mainAxis:u,crossAxis:0,alignmentAxis:null}:{mainAxis:u.mainAxis||0,crossAxis:u.crossAxis||0,alignmentAxis:u.alignmentAxis};return s&&"number"==typeof f&&(h="end"===s?-1*f:f),l?{x:h*d,y:p*c}:{x:p*c,y:h*d}}(t,e);return i===(null==(n=s.offset)?void 0:n.placement)&&null!=(r=s.arrow)&&r.alignmentOffset?{}:{x:o+l.x,y:a+l.y,data:{...l,placement:i}}}}},yn=function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r;const{placement:o,middlewareData:a,rects:i,initialPlacement:s,platform:l,elements:c}=t,{mainAxis:d=!0,crossAxis:u=!0,fallbackPlacements:p,fallbackStrategy:h="bestFit",fallbackAxisSideDirection:f="none",flipAlignment:m=!0,...g}=Ht(e,t);if(null!=(n=a.arrow)&&n.alignmentOffset)return{};const v=Wt(o),y=zt(s),x=Wt(s)===s,w=await(null==l.isRTL?void 0:l.isRTL(c.floating)),b=p||(x||!m?[Gt(s)]:function(e){const t=Gt(e);return[Qt(e),t,Qt(t)]}(s)),D="none"!==f;!p&&D&&b.push(...function(e,t,n,r){const o=Vt(e);let a=function(e,t,n){const r=["left","right"],o=["right","left"],a=["top","bottom"],i=["bottom","top"];switch(e){case"top":case"bottom":return n?t?o:r:t?r:o;case"left":case"right":return t?a:i;default:return[]}}(Wt(e),"start"===n,r);return o&&(a=a.map((e=>e+"-"+o)),t&&(a=a.concat(a.map(Qt)))),a}(s,m,f,w));const C=[s,...b],S=await async function(e,t){var n;void 0===t&&(t={});const{x:r,y:o,platform:a,rects:i,elements:s,strategy:l}=e,{boundary:c="clippingAncestors",rootBoundary:d="viewport",elementContext:u="floating",altBoundary:p=!1,padding:h=0}=Ht(t,e),f=Ut(h),m=s[p?"floating"===u?"reference":"floating":u],g=qt(await a.getClippingRect({element:null==(n=await(null==a.isElement?void 0:a.isElement(m)))||n?m:m.contextElement||await(null==a.getDocumentElement?void 0:a.getDocumentElement(s.floating)),boundary:c,rootBoundary:d,strategy:l})),v="floating"===u?{x:r,y:o,width:i.floating.width,height:i.floating.height}:i.reference,y=await(null==a.getOffsetParent?void 0:a.getOffsetParent(s.floating)),x=await(null==a.isElement?void 0:a.isElement(y))&&await(null==a.getScale?void 0:a.getScale(y))||{x:1,y:1},w=qt(a.convertOffsetParentRelativeRectToViewportRelativeRect?await a.convertOffsetParentRelativeRectToViewportRelativeRect({elements:s,rect:v,offsetParent:y,strategy:l}):v);return{top:(g.top-w.top+f.top)/x.y,bottom:(w.bottom-g.bottom+f.bottom)/x.y,left:(g.left-w.left+f.left)/x.x,right:(w.right-g.right+f.right)/x.x}}(t,g),k=[];let _=(null==(r=a.flip)?void 0:r.overflows)||[];if(d&&k.push(S[v]),u){const e=function(e,t,n){void 0===n&&(n=!1);const r=Vt(e),o=Kt(e),a=Bt(o);let i="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[a]>t.floating[a]&&(i=Gt(i)),[i,Gt(i)]}(o,i,w);k.push(S[e[0]],S[e[1]])}if(_=[..._,{placement:o,overflows:k}],!k.every((e=>e<=0))){var M,E;const e=((null==(M=a.flip)?void 0:M.index)||0)+1,t=C[e];if(t)return{data:{index:e,overflows:_},reset:{placement:t}};let n=null==(E=_.filter((e=>e.overflows[0]<=0)).sort(((e,t)=>e.overflows[1]-t.overflows[1]))[0])?void 0:E.placement;if(!n)switch(h){case"bestFit":{var F;const e=null==(F=_.filter((e=>{if(D){const t=zt(e.placement);return t===y||"y"===t}return!0})).map((e=>[e.placement,e.overflows.filter((e=>e>0)).reduce(((e,t)=>e+t),0)])).sort(((e,t)=>e[1]-t[1]))[0])?void 0:F[0];e&&(n=e);break}case"initialPlacement":n=s}if(o!==n)return{reset:{placement:n}}}return{}}}},xn=e=>({name:"arrow",options:e,async fn(t){const{x:n,y:r,placement:o,rects:a,platform:i,elements:s,middlewareData:l}=t,{element:c,padding:d=0}=Ht(e,t)||{};if(null==c)return{};const u=Ut(d),p={x:n,y:r},h=Kt(o),f=Bt(h),m=await i.getDimensions(c),g="y"===h,v=g?"top":"left",y=g?"bottom":"right",x=g?"clientHeight":"clientWidth",w=a.reference[f]+a.reference[h]-p[h]-a.floating[f],b=p[h]-a.reference[h],D=await(null==i.getOffsetParent?void 0:i.getOffsetParent(c));let C=D?D[x]:0;C&&await(null==i.isElement?void 0:i.isElement(D))||(C=s.floating[x]||a.floating[f]);const S=w/2-b/2,k=C/2-m[f]/2-1,_=Pt(u[v],k),M=Pt(u[y],k),E=_,F=C-m[f]-M,T=C/2-m[f]/2+S,j=Yt(E,T,F),P=!l.arrow&&null!=Vt(o)&&T!==j&&a.reference[f]/2-(T<E?_:M)-m[f]/2<0,R=P?T<E?T-E:T-F:0;return{[h]:p[h]+R,data:{[h]:j,centerOffset:T-j-R,...P&&{alignmentOffset:R}},reset:P}}}),wn=(e,t,n)=>{const r=new Map,o={platform:fn,...n},a={...o.platform,_c:r};return(async(e,t,n)=>{const{placement:r="bottom",strategy:o="absolute",middleware:a=[],platform:i}=n,s=a.filter(Boolean),l=await(null==i.isRTL?void 0:i.isRTL(t));let c=await i.getElementRects({reference:e,floating:t,strategy:o}),{x:d,y:u}=$t(c,r,l),p=r,h={},f=0;for(let m=0;m<s.length;m++){const{name:n,fn:a}=s[m],{x:g,y:v,data:y,reset:x}=await a({x:d,y:u,initialPlacement:r,placement:p,strategy:o,middlewareData:h,rects:c,platform:i,elements:{reference:e,floating:t}});d=null!=g?g:d,u=null!=v?v:u,h={...h,[n]:{...h[n],...y}},x&&f<=50&&(f++,"object"==typeof x&&(x.placement&&(p=x.placement),x.rects&&(c=!0===x.rects?await i.getElementRects({reference:e,floating:t,strategy:o}):x.rects),({x:d,y:u}=$t(c,p,l))),m=-1)}return{x:d,y:u,placement:p,strategy:o,middlewareData:h}})(e,t,{...o,platform:a})};var bn="undefined"!=typeof document?t.useLayoutEffect:t.useEffect;function Dn(e,t){if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;let n,r,o;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if(n=e.length,n!==t.length)return!1;for(r=n;0!=r--;)if(!Dn(e[r],t[r]))return!1;return!0}if(o=Object.keys(e),n=o.length,n!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!{}.hasOwnProperty.call(t,o[r]))return!1;for(r=n;0!=r--;){const n=o[r];if(("_owner"!==n||!e.$$typeof)&&!Dn(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function Cn(e){if("undefined"==typeof window)return 1;return(e.ownerDocument.defaultView||window).devicePixelRatio||1}function Sn(e,t){const n=Cn(e);return Math.round(t*n)/n}function kn(e){const n=t.useRef(e);return bn((()=>{n.current=e})),n}const _n=e=>({name:"arrow",options:e,fn(t){const{element:n,padding:r}="function"==typeof e?e(t):e;return n&&(o=n,{}.hasOwnProperty.call(o,"current"))?null!=n.current?xn({element:n.current,padding:r}).fn(t):{}:n?xn({element:n,padding:r}).fn(t):{};var o}}),Mn=(e,t)=>({...vn(e),options:[e,t]}),En=(e,t)=>({..._n(e),options:[e,t]}),Fn={...m};let Tn=!1,jn=0;const Pn=()=>"floating-ui-"+Math.random().toString(36).slice(2,6)+jn++;const Rn=Fn.useId||function(){const[e,n]=t.useState((()=>Tn?Pn():void 0));return Xt((()=>{null==e&&n(Pn())}),[]),t.useEffect((()=>{Tn=!0}),[]),e},In=t.forwardRef((function(e,r){const{context:{placement:o,elements:{floating:a},middlewareData:{arrow:i,shift:s}},width:l=14,height:c=7,tipRadius:d=0,strokeWidth:u=0,staticOffset:p,stroke:h,d:f,style:{transform:m,...g}={},...v}=e,y=Rn(),[x,w]=t.useState(!1);if(Xt((()=>{if(!a)return;"rtl"===_t(a).direction&&w(!0)}),[a]),!a)return null;const[b,D]=o.split("-"),C="top"===b||"bottom"===b;let S=p;(C&&null!=s&&s.x||!C&&null!=s&&s.y)&&(S=null);const k=2*u,_=k/2,M=l/2*(d/-8+1),E=c/2*d/4,F=!!f,T=S&&"end"===D?"bottom":"top";let j=S&&"end"===D?"right":"left";S&&x&&(j="end"===D?"left":"right");const P=null!=(null==i?void 0:i.x)?S||i.x:"",R=null!=(null==i?void 0:i.y)?S||i.y:"",I=f||"M0,0 H"+l+" L"+(l-M)+","+(c-E)+" Q"+l/2+","+c+" "+M+","+(c-E)+" Z",A={top:F?"rotate(180deg)":"",left:F?"rotate(90deg)":"rotate(-90deg)",bottom:F?"":"rotate(180deg)",right:F?"rotate(-90deg)":"rotate(90deg)"}[b];return n.jsxs("svg",{...v,"aria-hidden":!0,ref:r,width:F?l:l+k,height:l,viewBox:"0 0 "+l+" "+(c>l?c:l),style:{position:"absolute",pointerEvents:"none",[j]:P,[T]:R,[b]:C||F?"100%":"calc(100% - "+k/2+"px)",transform:[A,m].filter((e=>!!e)).join(" "),...g},children:[k>0&&n.jsx("path",{clipPath:"url(#"+y+")",fill:"none",stroke:h,strokeWidth:k+(f?0:1),d:I}),n.jsx("path",{stroke:k&&!f?v.fill:"none",d:I}),n.jsx("clipPath",{id:y,children:n.jsx("rect",{x:-_,y:_*(F?-1:1),width:l+k,height:l})})]})}));const An=t.createContext(null),Nn=t.createContext(null);function Ln(e){const{open:n=!1,onOpenChange:r,elements:o}=e,a=Rn(),i=t.useRef({}),[s]=t.useState((()=>function(){const e=new Map;return{emit(t,n){var r;null==(r=e.get(t))||r.forEach((e=>e(n)))},on(t,n){e.has(t)||e.set(t,new Set),e.get(t).add(n)},off(t,n){var r;null==(r=e.get(t))||r.delete(n)}}}())),l=null!=((null==(c=t.useContext(An))?void 0:c.id)||null);var c;const[d,u]=t.useState(o.reference),p=function(e){const n=t.useRef((()=>{}));return Zt((()=>{n.current=e})),t.useCallback((function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return null==n.current?void 0:n.current(...t)}),[])}(((e,t,n)=>{i.current.openEvent=e?t:void 0,s.emit("openchange",{open:e,event:t,reason:n,nested:l}),null==r||r(e,t,n)})),h=t.useMemo((()=>({setPositionReference:u})),[]),f=t.useMemo((()=>({reference:d||o.reference||null,floating:o.floating||null,domReference:o.reference})),[d,o.reference,o.floating]);return t.useMemo((()=>({dataRef:i,open:n,onOpenChange:p,elements:f,events:s,floatingId:a,refs:h})),[n,p,f,s,a,h])}function On(e){void 0===e&&(e={});const{nodeId:n}=e,r=Ln({...e,elements:{reference:null,floating:null,...e.elements}}),o=e.rootContext||r,a=o.elements,[i,s]=t.useState(null),[l,c]=t.useState(null),d=(null==a?void 0:a.domReference)||i,u=t.useRef(null),p=t.useContext(Nn);Xt((()=>{d&&(u.current=d)}),[d]);const h=function(e){void 0===e&&(e={});const{placement:n="bottom",strategy:r="absolute",middleware:o=[],platform:a,elements:{reference:i,floating:s}={},transform:l=!0,whileElementsMounted:c,open:d}=e,[u,p]=t.useState({x:0,y:0,strategy:r,placement:n,middlewareData:{},isPositioned:!1}),[h,f]=t.useState(o);Dn(h,o)||f(o);const[m,v]=t.useState(null),[y,x]=t.useState(null),w=t.useCallback((e=>{e!==S.current&&(S.current=e,v(e))}),[]),b=t.useCallback((e=>{e!==k.current&&(k.current=e,x(e))}),[]),D=i||m,C=s||y,S=t.useRef(null),k=t.useRef(null),_=t.useRef(u),M=null!=c,E=kn(c),F=kn(a),T=kn(d),j=t.useCallback((()=>{if(!S.current||!k.current)return;const e={placement:n,strategy:r,middleware:h};F.current&&(e.platform=F.current),wn(S.current,k.current,e).then((e=>{const t={...e,isPositioned:!1!==T.current};P.current&&!Dn(_.current,t)&&(_.current=t,g.flushSync((()=>{p(t)})))}))}),[h,n,r,F,T]);bn((()=>{!1===d&&_.current.isPositioned&&(_.current.isPositioned=!1,p((e=>({...e,isPositioned:!1}))))}),[d]);const P=t.useRef(!1);bn((()=>(P.current=!0,()=>{P.current=!1})),[]),bn((()=>{if(D&&(S.current=D),C&&(k.current=C),D&&C){if(E.current)return E.current(D,C,j);j()}}),[D,C,j,E,M]);const R=t.useMemo((()=>({reference:S,floating:k,setReference:w,setFloating:b})),[w,b]),I=t.useMemo((()=>({reference:D,floating:C})),[D,C]),A=t.useMemo((()=>{const e={position:r,left:0,top:0};if(!I.floating)return e;const t=Sn(I.floating,u.x),n=Sn(I.floating,u.y);return l?{...e,transform:"translate("+t+"px, "+n+"px)",...Cn(I.floating)>=1.5&&{willChange:"transform"}}:{position:r,left:t,top:n}}),[r,l,I.floating,u.x,u.y]);return t.useMemo((()=>({...u,update:j,refs:R,elements:I,floatingStyles:A})),[u,j,R,I,A])}({...e,elements:{...a,...l&&{reference:l}}}),f=t.useCallback((e=>{const t=vt(e)?{getBoundingClientRect:()=>e.getBoundingClientRect(),getClientRects:()=>e.getClientRects(),contextElement:e}:e;c(t),h.refs.setReference(t)}),[h.refs]),m=t.useCallback((e=>{(vt(e)||null===e)&&(u.current=e,s(e)),(vt(h.refs.reference.current)||null===h.refs.reference.current||null!==e&&!vt(e))&&h.refs.setReference(e)}),[h.refs]),v=t.useMemo((()=>({...h.refs,setReference:m,setPositionReference:f,domReference:u})),[h.refs,m,f]),y=t.useMemo((()=>({...h.elements,domReference:d})),[h.elements,d]),x=t.useMemo((()=>({...h,...o,refs:v,elements:y,nodeId:n})),[h,v,y,n,o]);return Xt((()=>{o.dataRef.current.floatingContext=x;const e=null==p?void 0:p.nodesRef.current.find((e=>e.id===n));e&&(e.context=x)})),t.useMemo((()=>({...h,context:x,refs:v,elements:y})),[h,v,y,x])}
/*!
  react-datepicker v7.6.0
  https://github.com/Hacker0x01/react-datepicker
  Released under the MIT License.
*/var Yn=function(e,t){return(Yn=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)};function Hn(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}Yn(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}var Wn=function(){return Wn=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},Wn.apply(this,arguments)};function Vn(e,t,n){if(n||2===arguments.length)for(var r,o=0,a=t.length;o<a;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))}"function"==typeof SuppressedError&&SuppressedError;var Bn,zn,Kn=function(e){var t=e.showTimeSelectOnly,n=void 0!==t&&t,r=e.showTime,o=void 0!==r&&r,a=e.className,i=e.children,s=n?"Choose Time":"Choose Date".concat(o?" and Time":"");return h.createElement("div",{className:a,role:"dialog","aria-label":s,"aria-modal":"true"},i)},Qn=function(e){var n=e.children,r=e.onClickOutside,o=e.className,a=e.containerRef,i=e.style,s=function(e,n){var r=t.useRef(null),o=t.useRef(e);o.current=e;var a=t.useCallback((function(e){var t;r.current&&!r.current.contains(e.target)&&(n&&e.target instanceof HTMLElement&&e.target.classList.contains(n)||null===(t=o.current)||void 0===t||t.call(o,e))}),[n]);return t.useEffect((function(){return document.addEventListener("mousedown",a),function(){document.removeEventListener("mousedown",a)}}),[a]),r}(r,e.ignoreClass);return h.createElement("div",{className:o,style:i,ref:function(e){s.current=e,a&&(a.current=e)}},n)};function Gn(){return"undefined"!=typeof window?window:globalThis}(zn=Bn||(Bn={})).ArrowUp="ArrowUp",zn.ArrowDown="ArrowDown",zn.ArrowLeft="ArrowLeft",zn.ArrowRight="ArrowRight",zn.PageUp="PageUp",zn.PageDown="PageDown",zn.Home="Home",zn.End="End",zn.Enter="Enter",zn.Space=" ",zn.Tab="Tab",zn.Escape="Escape",zn.Backspace="Backspace",zn.X="x";var Un=/P+p+|P+|p+|''|'(''|[^'])+('|$)|./g;function qn(e){if(null==e)return new Date;var t="string"==typeof e?de(e):ue(e);return Zn(t)?t:new Date}function Xn(e,t,n,r,o){var a,i=null,s=mr(n)||mr(fr()),l=!0;if(Array.isArray(t))return t.forEach((function(t){var a=ye(e,t,new Date,{locale:s,useAdditionalWeekYearTokens:!0,useAdditionalDayOfYearTokens:!0});r&&(l=Zn(a,o)&&e===$n(a,t,n)),Zn(a,o)&&l&&(i=a)})),i;if(i=ye(e,t,new Date,{locale:s,useAdditionalWeekYearTokens:!0,useAdditionalDayOfYearTokens:!0}),r)l=Zn(i)&&e===$n(i,t,n);else if(!Zn(i)){var c=(null!==(a=t.match(Un))&&void 0!==a?a:[]).map((function(e){var t=e[0];if("p"===t||"P"===t){var n=xe[t];return s?n(e,s.formatLong):t}return e})).join("");e.length>0&&(i=ye(e,c.slice(0,e.length),new Date,{useAdditionalWeekYearTokens:!0,useAdditionalDayOfYearTokens:!0})),Zn(i)||(i=new Date(e))}return Zn(i)&&l?i:null}function Zn(e,t){return pe(e)&&!ee(e,null!=t?t:new Date("1/1/1800"))}function $n(e,t,n){if("en"===n)return Le(e,t,{useAdditionalWeekYearTokens:!0,useAdditionalDayOfYearTokens:!0});var r=n?mr(n):void 0;return!r&&fr()&&mr(fr())&&(r=mr(fr())),Le(e,t,{locale:r,useAdditionalWeekYearTokens:!0,useAdditionalDayOfYearTokens:!0})}function Jn(e,t){var n=t.dateFormat,r=t.locale,o=Array.isArray(n)&&n.length>0?n[0]:n;return e&&$n(e,o,r)||""}function er(e,t){var n=t.hour,r=void 0===n?0:n,o=t.minute,a=void 0===o?0:o,i=t.second;return _e(Me(Ee(e,void 0===i?0:i),a),r)}function tr(e){return ge(e)}function nr(e,t,n){var r=mr(t||fr());return Be(e,{locale:r,weekStartsOn:n})}function rr(e){return ke(e)}function or(e){return Ce(e)}function ar(e){return tt(e)}function ir(){return ge(qn())}function sr(e){return ve(e)}function lr(e,t){return e&&t?Oe(e,t):!e&&!t}function cr(e,t){return e&&t?Ye(e,t):!e&&!t}function dr(e,t){return e&&t?et(e,t):!e&&!t}function ur(e,t){return e&&t?be(e,t):!e&&!t}function pr(e,t){return e&&t?ce(e,t):!e&&!t}function hr(e,t,n){var r,o=ge(t),a=ve(n);try{r=we(e,{start:o,end:a})}catch(i){r=!1}return r}function fr(){return Gn().__localeId__}function mr(e){if("string"==typeof e){var t=Gn();return t.__localeData__?t.__localeData__[e]:void 0}return e}function gr(e,t){return $n(Ie(qn(),e),"LLLL",t)}function vr(e,t){return $n(Ie(qn(),e),"LLL",t)}function yr(e,t){var n=void 0===t?{}:t,r=n.minDate,o=n.maxDate,a=n.excludeDates,i=n.excludeDateIntervals,s=n.includeDates,l=n.includeDateIntervals,c=n.filterDate;return Mr(e,{minDate:r,maxDate:o})||a&&a.some((function(t){return t instanceof Date?ur(e,t):ur(e,t.date)}))||i&&i.some((function(t){var n=t.start,r=t.end;return we(e,{start:n,end:r})}))||s&&!s.some((function(t){return ur(e,t)}))||l&&!l.some((function(t){var n=t.start,r=t.end;return we(e,{start:n,end:r})}))||c&&!c(qn(e))||!1}function xr(e,t){var n=void 0===t?{}:t,r=n.excludeDates,o=n.excludeDateIntervals;return o&&o.length>0?o.some((function(t){var n=t.start,r=t.end;return we(e,{start:n,end:r})})):r&&r.some((function(t){var n;return t instanceof Date?ur(e,t):ur(e,null!==(n=t.date)&&void 0!==n?n:new Date)}))||!1}function wr(e,t){var n=void 0===t?{}:t,r=n.minDate,o=n.maxDate,a=n.excludeDates,i=n.includeDates,s=n.filterDate;return Mr(e,{minDate:r?ke(r):void 0,maxDate:o?Se(o):void 0})||(null==a?void 0:a.some((function(t){return cr(e,t instanceof Date?t:t.date)})))||i&&!i.some((function(t){return cr(e,t)}))||s&&!s(qn(e))||!1}function br(e,t,n,r){var o=se(e),a=le(e),i=se(t),s=le(t),l=se(r);return o===i&&o===l?a<=n&&n<=s:o<i&&(l===o&&a<=n||l===i&&s>=n||l<i&&l>o)}function Dr(e,t){var n=void 0===t?{}:t,r=n.minDate,o=n.maxDate,a=n.excludeDates,i=n.includeDates;return Mr(e,{minDate:r,maxDate:o})||a&&a.some((function(t){return cr(t instanceof Date?t:t.date,e)}))||i&&!i.some((function(t){return cr(t,e)}))||!1}function Cr(e,t){var n=void 0===t?{}:t,r=n.minDate,o=n.maxDate,a=n.excludeDates,i=n.includeDates,s=n.filterDate;return Mr(e,{minDate:r,maxDate:o})||(null==a?void 0:a.some((function(t){return dr(e,t instanceof Date?t:t.date)})))||i&&!i.some((function(t){return dr(e,t)}))||s&&!s(qn(e))||!1}function Sr(e,t,n){if(!t||!n)return!1;if(!pe(t)||!pe(n))return!1;var r=se(t),o=se(n);return r<=e&&o>=e}function kr(e,t){var n=void 0===t?{}:t,r=n.minDate,o=n.maxDate,a=n.excludeDates,i=n.includeDates,s=n.filterDate,l=new Date(e,0,1);return Mr(l,{minDate:r?Ce(r):void 0,maxDate:o?De(o):void 0})||(null==a?void 0:a.some((function(e){return lr(l,e instanceof Date?e:e.date)})))||i&&!i.some((function(e){return lr(l,e)}))||s&&!s(qn(l))||!1}function _r(e,t,n,r){var o=se(e),a=Ze(e),i=se(t),s=Ze(t),l=se(r);return o===i&&o===l?a<=n&&n<=s:o<i&&(l===o&&a<=n||l===i&&s>=n||l<i&&l>o)}function Mr(e,t){var n,r=void 0===t?{}:t,o=r.minDate,a=r.maxDate;return null!==(n=o&&he(e,o)<0||a&&he(e,a)>0)&&void 0!==n&&n}function Er(e,t){return t.some((function(t){return ie(t)===ie(e)&&ae(t)===ae(e)&&oe(t)===oe(e)}))}function Fr(e,t){var n=void 0===t?{}:t,r=n.excludeTimes,o=n.includeTimes,a=n.filterTime;return r&&Er(e,r)||o&&!Er(e,o)||a&&!a(e)||!1}function Tr(e,t){var n=t.minTime,r=t.maxTime;if(!n||!r)throw new Error("Both minTime and maxTime props required");var o=qn();o=_e(o,ie(e)),o=Me(o,ae(e)),o=Ee(o,oe(e));var a=qn();a=_e(a,ie(n)),a=Me(a,ae(n)),a=Ee(a,oe(n));var i,s=qn();s=_e(s,ie(r)),s=Me(s,ae(r)),s=Ee(s,oe(r));try{i=!we(o,{start:a,end:s})}catch(l){i=!1}return i}function jr(e,t){var n=void 0===t?{}:t,r=n.minDate,o=n.includeDates,a=Te(e,1);return r&&ze(r,a)>0||o&&o.every((function(e){return ze(e,a)>0}))||!1}function Pr(e,t){var n=void 0===t?{}:t,r=n.maxDate,o=n.includeDates,a=Fe(e,1);return r&&ze(a,r)>0||o&&o.every((function(e){return ze(a,e)>0}))||!1}function Rr(e,t){var n=void 0===t?{}:t,r=n.minDate,o=n.includeDates,a=Ae(e,1);return r&&Ge(r,a)>0||o&&o.every((function(e){return Ge(e,a)>0}))||!1}function Ir(e,t){var n=void 0===t?{}:t,r=n.maxDate,o=n.includeDates,a=Ne(e,1);return r&&Ge(a,r)>0||o&&o.every((function(e){return Ge(a,e)>0}))||!1}function Ar(e){var t=e.minDate,n=e.includeDates;if(n&&t){var r=n.filter((function(e){return he(e,t)>=0}));return fe(r)}return n?fe(n):t}function Nr(e){var t=e.maxDate,n=e.includeDates;if(n&&t){var r=n.filter((function(e){return he(e,t)<=0}));return me(r)}return n?me(n):t}function Lr(e,t){var n;void 0===e&&(e=[]),void 0===t&&(t="react-datepicker__day--highlighted");for(var r=new Map,o=0,a=e.length;o<a;o++){var i=e[o];if(ne(i)){var s=$n(i,"MM.dd.yyyy");(h=r.get(s)||[]).includes(t)||(h.push(t),r.set(s,h))}else if("object"==typeof i){var l=null!==(n=Object.keys(i)[0])&&void 0!==n?n:"",c=i[l];if("string"==typeof l&&Array.isArray(c))for(var d=0,u=c.length;d<u;d++){var p=c[d];if(p){var h;s=$n(p,"MM.dd.yyyy");(h=r.get(s)||[]).includes(l)||(h.push(l),r.set(s,h))}}}}return r}function Or(e,t){void 0===e&&(e=[]),void 0===t&&(t="react-datepicker__day--holidays");var n=new Map;return e.forEach((function(e){var r=e.date,o=e.holidayName;if(ne(r)){var a=$n(r,"MM.dd.yyyy"),i=n.get(a)||{className:"",holidayNames:[]};if(!("className"in i)||i.className!==t||(s=i.holidayNames,l=[o],s.length!==l.length||!s.every((function(e,t){return e===l[t]})))){var s,l;i.className=t;var c=i.holidayNames;i.holidayNames=c?Vn(Vn([],c,!0),[o],!1):[o],n.set(a,i)}}})),n}function Yr(e,t,n,r,o){for(var a=o.length,i=[],s=0;s<a;s++){var l=e,c=o[s];c&&(l=nt(l,ie(c)),l=$e(l,ae(c)),l=rt(l,oe(c)));var d=$e(e,(n+1)*r);te(l,t)&&ee(l,d)&&null!=c&&i.push(c)}return i}function Hr(e){return e<10?"0".concat(e):"".concat(e)}function Wr(e,t){void 0===t&&(t=12);var n=Math.ceil(se(e)/t)*t;return{startPeriod:n-(t-1),endPeriod:n}}function Vr(e){var t=e.getSeconds(),n=e.getMilliseconds();return ue(e.getTime()-1e3*t-n)}function Br(e){if(!ne(e))throw new Error("Invalid date");var t=new Date(e);return t.setHours(0,0,0,0),t}function zr(e,t){if(!ne(e)||!ne(t))throw new Error("Invalid date received");var n=Br(e),r=Br(t);return ee(n,r)}function Kr(e){return e.key===Bn.Space}var Qr,Gr=function(e){function n(n){var r=e.call(this,n)||this;return r.inputRef=h.createRef(),r.onTimeChange=function(e){var t,n;r.setState({time:e});var o=r.props.date,a=o instanceof Date&&!isNaN(+o)?o:new Date;if(null==e?void 0:e.includes(":")){var i=e.split(":"),s=i[0],l=i[1];a.setHours(Number(s)),a.setMinutes(Number(l))}null===(n=(t=r.props).onChange)||void 0===n||n.call(t,a)},r.renderTimeInput=function(){var e=r.state.time,n=r.props,o=n.date,a=n.timeString,i=n.customTimeInput;return i?t.cloneElement(i,{date:o,value:e,onChange:r.onTimeChange}):h.createElement("input",{type:"time",className:"react-datepicker-time__input",placeholder:"Time",name:"time-input",ref:r.inputRef,onClick:function(){var e;null===(e=r.inputRef.current)||void 0===e||e.focus()},required:!0,value:e,onChange:function(e){r.onTimeChange(e.target.value||a)}})},r.state={time:r.props.timeString},r}return Hn(n,e),n.getDerivedStateFromProps=function(e,t){return e.timeString!==t.time?{time:e.timeString}:null},n.prototype.render=function(){return h.createElement("div",{className:"react-datepicker__input-time-container"},h.createElement("div",{className:"react-datepicker-time__caption"},this.props.timeInputLabel),h.createElement("div",{className:"react-datepicker-time__input-container"},h.createElement("div",{className:"react-datepicker-time__input"},this.renderTimeInput())))},n}(t.Component),Ur=function(e){function n(){var n=null!==e&&e.apply(this,arguments)||this;return n.dayEl=t.createRef(),n.handleClick=function(e){!n.isDisabled()&&n.props.onClick&&n.props.onClick(e)},n.handleMouseEnter=function(e){!n.isDisabled()&&n.props.onMouseEnter&&n.props.onMouseEnter(e)},n.handleOnKeyDown=function(e){var t,r;e.key===Bn.Space&&(e.preventDefault(),e.key=Bn.Enter),null===(r=(t=n.props).handleOnKeyDown)||void 0===r||r.call(t,e)},n.isSameDay=function(e){return ur(n.props.day,e)},n.isKeyboardSelected=function(){var e;if(n.props.disabledKeyboardNavigation)return!1;var t=n.props.selectsMultiple?null===(e=n.props.selectedDates)||void 0===e?void 0:e.some((function(e){return n.isSameDayOrWeek(e)})):n.isSameDayOrWeek(n.props.selected),r=n.props.preSelection&&n.isDisabled(n.props.preSelection);return!t&&n.isSameDayOrWeek(n.props.preSelection)&&!r},n.isDisabled=function(e){return void 0===e&&(e=n.props.day),yr(e,{minDate:n.props.minDate,maxDate:n.props.maxDate,excludeDates:n.props.excludeDates,excludeDateIntervals:n.props.excludeDateIntervals,includeDateIntervals:n.props.includeDateIntervals,includeDates:n.props.includeDates,filterDate:n.props.filterDate})},n.isExcluded=function(){return xr(n.props.day,{excludeDates:n.props.excludeDates,excludeDateIntervals:n.props.excludeDateIntervals})},n.isStartOfWeek=function(){return ur(n.props.day,nr(n.props.day,n.props.locale,n.props.calendarStartDay))},n.isSameWeek=function(e){return n.props.showWeekPicker&&ur(e,nr(n.props.day,n.props.locale,n.props.calendarStartDay))},n.isSameDayOrWeek=function(e){return n.isSameDay(e)||n.isSameWeek(e)},n.getHighLightedClass=function(){var e=n.props,t=e.day,r=e.highlightDates;if(!r)return!1;var o=$n(t,"MM.dd.yyyy");return r.get(o)},n.getHolidaysClass=function(){var e,t=n.props,r=t.day,o=t.holidays;if(!o)return[void 0];var a=$n(r,"MM.dd.yyyy");return o.has(a)?[null===(e=o.get(a))||void 0===e?void 0:e.className]:[void 0]},n.isInRange=function(){var e=n.props,t=e.day,r=e.startDate,o=e.endDate;return!(!r||!o)&&hr(t,r,o)},n.isInSelectingRange=function(){var e,t=n.props,r=t.day,o=t.selectsStart,a=t.selectsEnd,i=t.selectsRange,s=t.selectsDisabledDaysInRange,l=t.startDate,c=t.endDate,d=null!==(e=n.props.selectingDate)&&void 0!==e?e:n.props.preSelection;return!(!(o||a||i)||!d||!s&&n.isDisabled())&&(o&&c&&(ee(d,c)||pr(d,c))?hr(r,d,c):(a&&l&&(te(d,l)||pr(d,l))||!(!i||!l||c||!te(d,l)&&!pr(d,l)))&&hr(r,l,d))},n.isSelectingRangeStart=function(){var e;if(!n.isInSelectingRange())return!1;var t=n.props,r=t.day,o=t.startDate,a=t.selectsStart,i=null!==(e=n.props.selectingDate)&&void 0!==e?e:n.props.preSelection;return ur(r,a?i:o)},n.isSelectingRangeEnd=function(){var e;if(!n.isInSelectingRange())return!1;var t=n.props,r=t.day,o=t.endDate,a=t.selectsEnd,i=t.selectsRange,s=null!==(e=n.props.selectingDate)&&void 0!==e?e:n.props.preSelection;return ur(r,a||i?s:o)},n.isRangeStart=function(){var e=n.props,t=e.day,r=e.startDate,o=e.endDate;return!(!r||!o)&&ur(r,t)},n.isRangeEnd=function(){var e=n.props,t=e.day,r=e.startDate,o=e.endDate;return!(!r||!o)&&ur(o,t)},n.isWeekend=function(){var e=at(n.props.day);return 0===e||6===e},n.isAfterMonth=function(){return void 0!==n.props.month&&(n.props.month+1)%12===le(n.props.day)},n.isBeforeMonth=function(){return void 0!==n.props.month&&(le(n.props.day)+1)%12===n.props.month},n.isCurrentDay=function(){return n.isSameDay(qn())},n.isSelected=function(){var e;return n.props.selectsMultiple?null===(e=n.props.selectedDates)||void 0===e?void 0:e.some((function(e){return n.isSameDayOrWeek(e)})):n.isSameDayOrWeek(n.props.selected)},n.getClassNames=function(e){var t,r=n.props.dayClassName?n.props.dayClassName(e):void 0;return v("react-datepicker__day",r,"react-datepicker__day--"+$n(n.props.day,"ddd",t),{"react-datepicker__day--disabled":n.isDisabled(),"react-datepicker__day--excluded":n.isExcluded(),"react-datepicker__day--selected":n.isSelected(),"react-datepicker__day--keyboard-selected":n.isKeyboardSelected(),"react-datepicker__day--range-start":n.isRangeStart(),"react-datepicker__day--range-end":n.isRangeEnd(),"react-datepicker__day--in-range":n.isInRange(),"react-datepicker__day--in-selecting-range":n.isInSelectingRange(),"react-datepicker__day--selecting-range-start":n.isSelectingRangeStart(),"react-datepicker__day--selecting-range-end":n.isSelectingRangeEnd(),"react-datepicker__day--today":n.isCurrentDay(),"react-datepicker__day--weekend":n.isWeekend(),"react-datepicker__day--outside-month":n.isAfterMonth()||n.isBeforeMonth()},n.getHighLightedClass(),n.getHolidaysClass())},n.getAriaLabel=function(){var e=n.props,t=e.day,r=e.ariaLabelPrefixWhenEnabled,o=void 0===r?"Choose":r,a=e.ariaLabelPrefixWhenDisabled,i=void 0===a?"Not available":a,s=n.isDisabled()||n.isExcluded()?i:o;return"".concat(s," ").concat($n(t,"PPPP",n.props.locale))},n.getTitle=function(){var e=n.props,t=e.day,r=e.holidays,o=void 0===r?new Map:r,a=e.excludeDates,i=$n(t,"MM.dd.yyyy"),s=[];return o.has(i)&&s.push.apply(s,o.get(i).holidayNames),n.isExcluded()&&s.push(null==a?void 0:a.filter((function(e){return e instanceof Date?ur(e,t):ur(null==e?void 0:e.date,t)})).map((function(e){if(!(e instanceof Date))return null==e?void 0:e.message}))),s.join(", ")},n.getTabIndex=function(){var e=n.props.selected,t=n.props.preSelection;return(!n.props.showWeekPicker||!n.props.showWeekNumber&&n.isStartOfWeek())&&(n.isKeyboardSelected()||n.isSameDay(e)&&ur(t,e))?0:-1},n.handleFocusDay=function(){var e;n.shouldFocusDay()&&(null===(e=n.dayEl.current)||void 0===e||e.focus({preventScroll:!0}))},n.renderDayContents=function(){return n.props.monthShowsDuplicateDaysEnd&&n.isAfterMonth()||n.props.monthShowsDuplicateDaysStart&&n.isBeforeMonth()?null:n.props.renderDayContents?n.props.renderDayContents(it(n.props.day),n.props.day):it(n.props.day)},n.render=function(){return h.createElement("div",{ref:n.dayEl,className:n.getClassNames(n.props.day),onKeyDown:n.handleOnKeyDown,onClick:n.handleClick,onMouseEnter:n.props.usePointerEvent?void 0:n.handleMouseEnter,onPointerEnter:n.props.usePointerEvent?n.handleMouseEnter:void 0,tabIndex:n.getTabIndex(),"aria-label":n.getAriaLabel(),role:"option",title:n.getTitle(),"aria-disabled":n.isDisabled(),"aria-current":n.isCurrentDay()?"date":void 0,"aria-selected":n.isSelected()||n.isInRange()},n.renderDayContents(),""!==n.getTitle()&&h.createElement("span",{className:"overlay"},n.getTitle()))},n}return Hn(n,e),n.prototype.componentDidMount=function(){this.handleFocusDay()},n.prototype.componentDidUpdate=function(){this.handleFocusDay()},n.prototype.shouldFocusDay=function(){var e=!1;return 0===this.getTabIndex()&&this.isSameDay(this.props.preSelection)&&(document.activeElement&&document.activeElement!==document.body||(e=!0),this.props.inline&&!this.props.shouldFocusDayInline&&(e=!1),this.isDayActiveElement()&&(e=!0),this.isDuplicateDay()&&(e=!1)),e},n.prototype.isDayActiveElement=function(){var e,t,n;return(null===(t=null===(e=this.props.containerRef)||void 0===e?void 0:e.current)||void 0===t?void 0:t.contains(document.activeElement))&&(null===(n=document.activeElement)||void 0===n?void 0:n.classList.contains("react-datepicker__day"))},n.prototype.isDuplicateDay=function(){return this.props.monthShowsDuplicateDaysEnd&&this.isAfterMonth()||this.props.monthShowsDuplicateDaysStart&&this.isBeforeMonth()},n}(t.Component),qr=function(e){function n(){var n=null!==e&&e.apply(this,arguments)||this;return n.weekNumberEl=t.createRef(),n.handleClick=function(e){n.props.onClick&&n.props.onClick(e)},n.handleOnKeyDown=function(e){var t,r;e.key===Bn.Space&&(e.preventDefault(),e.key=Bn.Enter),null===(r=(t=n.props).handleOnKeyDown)||void 0===r||r.call(t,e)},n.isKeyboardSelected=function(){return!n.props.disabledKeyboardNavigation&&!ur(n.props.date,n.props.selected)&&ur(n.props.date,n.props.preSelection)},n.getTabIndex=function(){return n.props.showWeekPicker&&n.props.showWeekNumber&&(n.isKeyboardSelected()||ur(n.props.date,n.props.selected)&&ur(n.props.preSelection,n.props.selected))?0:-1},n.handleFocusWeekNumber=function(e){var t=!1;0===n.getTabIndex()&&!(null==e?void 0:e.isInputFocused)&&ur(n.props.date,n.props.preSelection)&&(document.activeElement&&document.activeElement!==document.body||(t=!0),n.props.inline&&!n.props.shouldFocusDayInline&&(t=!1),n.props.containerRef&&n.props.containerRef.current&&n.props.containerRef.current.contains(document.activeElement)&&document.activeElement&&document.activeElement.classList.contains("react-datepicker__week-number")&&(t=!0)),t&&n.weekNumberEl.current&&n.weekNumberEl.current.focus({preventScroll:!0})},n}return Hn(n,e),Object.defineProperty(n,"defaultProps",{get:function(){return{ariaLabelPrefix:"week "}},enumerable:!1,configurable:!0}),n.prototype.componentDidMount=function(){this.handleFocusWeekNumber()},n.prototype.componentDidUpdate=function(e){this.handleFocusWeekNumber(e)},n.prototype.render=function(){var e=this.props,t=e.weekNumber,r=e.isWeekDisabled,o=e.ariaLabelPrefix,a=void 0===o?n.defaultProps.ariaLabelPrefix:o,i=e.onClick,s={"react-datepicker__week-number":!0,"react-datepicker__week-number--clickable":!!i&&!r,"react-datepicker__week-number--selected":!!i&&ur(this.props.date,this.props.selected)};return h.createElement("div",{ref:this.weekNumberEl,className:v(s),"aria-label":"".concat(a," ").concat(this.props.weekNumber),onClick:this.handleClick,onKeyDown:this.handleOnKeyDown,tabIndex:this.getTabIndex()},t)},n}(t.Component),Xr=function(e){function t(){var n=null!==e&&e.apply(this,arguments)||this;return n.isDisabled=function(e){return yr(e,{minDate:n.props.minDate,maxDate:n.props.maxDate,excludeDates:n.props.excludeDates,excludeDateIntervals:n.props.excludeDateIntervals,includeDateIntervals:n.props.includeDateIntervals,includeDates:n.props.includeDates,filterDate:n.props.filterDate})},n.handleDayClick=function(e,t){n.props.onDayClick&&n.props.onDayClick(e,t)},n.handleDayMouseEnter=function(e){n.props.onDayMouseEnter&&n.props.onDayMouseEnter(e)},n.handleWeekClick=function(e,r,o){for(var a,i,s,l=new Date(e),c=0;c<7;c++){var d=new Date(e);if(d.setDate(d.getDate()+c),!n.isDisabled(d)){l=d;break}}"function"==typeof n.props.onWeekSelect&&n.props.onWeekSelect(l,r,o),n.props.showWeekPicker&&n.handleDayClick(l,o),(null!==(a=n.props.shouldCloseOnSelect)&&void 0!==a?a:t.defaultProps.shouldCloseOnSelect)&&(null===(s=(i=n.props).setOpen)||void 0===s||s.call(i,!1))},n.formatWeekNumber=function(e){return n.props.formatWeekNumber?n.props.formatWeekNumber(e):function(e){return ot(e)}(e)},n.isWeekDisabled=function(){for(var e=n.startOfWeek(),t=Re(e,6),r=new Date(e);r<=t;){if(!n.isDisabled(r))return!1;r=Re(r,1)}return!0},n.renderDays=function(){var e=n.startOfWeek(),r=[],o=n.formatWeekNumber(e);if(n.props.showWeekNumber){var a=n.props.onWeekSelect||n.props.showWeekPicker?n.handleWeekClick.bind(n,e,o):void 0;r.push(h.createElement(qr,Wn({key:"W"},t.defaultProps,n.props,{weekNumber:o,isWeekDisabled:n.isWeekDisabled(),date:e,onClick:a})))}return r.concat([0,1,2,3,4,5,6].map((function(r){var o=Re(e,r);return h.createElement(Ur,Wn({},t.defaultProps,n.props,{ariaLabelPrefixWhenEnabled:n.props.chooseDayAriaLabelPrefix,ariaLabelPrefixWhenDisabled:n.props.disabledDayAriaLabelPrefix,key:o.valueOf(),day:o,onClick:n.handleDayClick.bind(n,o),onMouseEnter:n.handleDayMouseEnter.bind(n,o)}))})))},n.startOfWeek=function(){return nr(n.props.day,n.props.locale,n.props.calendarStartDay)},n.isKeyboardSelected=function(){return!n.props.disabledKeyboardNavigation&&!ur(n.startOfWeek(),n.props.selected)&&ur(n.startOfWeek(),n.props.preSelection)},n}return Hn(t,e),Object.defineProperty(t,"defaultProps",{get:function(){return{shouldCloseOnSelect:!0}},enumerable:!1,configurable:!0}),t.prototype.render=function(){var e={"react-datepicker__week":!0,"react-datepicker__week--selected":ur(this.startOfWeek(),this.props.selected),"react-datepicker__week--keyboard-selected":this.isKeyboardSelected()};return h.createElement("div",{className:v(e)},this.renderDays())},t}(t.Component),Zr="two_columns",$r="three_columns",Jr="four_columns",eo=((Qr={})[Zr]={grid:[[0,1],[2,3],[4,5],[6,7],[8,9],[10,11]],verticalNavigationOffset:2},Qr[$r]={grid:[[0,1,2],[3,4,5],[6,7,8],[9,10,11]],verticalNavigationOffset:3},Qr[Jr]={grid:[[0,1,2,3],[4,5,6,7],[8,9,10,11]],verticalNavigationOffset:4},Qr);function to(e,t){return e?Jr:t?Zr:$r}var no=function(e){function n(){var n=null!==e&&e.apply(this,arguments)||this;return n.MONTH_REFS=Vn([],Array(12),!0).map((function(){return t.createRef()})),n.QUARTER_REFS=Vn([],Array(4),!0).map((function(){return t.createRef()})),n.isDisabled=function(e){return yr(e,{minDate:n.props.minDate,maxDate:n.props.maxDate,excludeDates:n.props.excludeDates,excludeDateIntervals:n.props.excludeDateIntervals,includeDateIntervals:n.props.includeDateIntervals,includeDates:n.props.includeDates,filterDate:n.props.filterDate})},n.isExcluded=function(e){return xr(e,{excludeDates:n.props.excludeDates,excludeDateIntervals:n.props.excludeDateIntervals})},n.handleDayClick=function(e,t){var r,o;null===(o=(r=n.props).onDayClick)||void 0===o||o.call(r,e,t,n.props.orderInDisplay)},n.handleDayMouseEnter=function(e){var t,r;null===(r=(t=n.props).onDayMouseEnter)||void 0===r||r.call(t,e)},n.handleMouseLeave=function(){var e,t;null===(t=(e=n.props).onMouseLeave)||void 0===t||t.call(e)},n.isRangeStartMonth=function(e){var t=n.props,r=t.day,o=t.startDate,a=t.endDate;return!(!o||!a)&&cr(Ie(r,e),o)},n.isRangeStartQuarter=function(e){var t=n.props,r=t.day,o=t.startDate,a=t.endDate;return!(!o||!a)&&dr(Xe(r,e),o)},n.isRangeEndMonth=function(e){var t=n.props,r=t.day,o=t.startDate,a=t.endDate;return!(!o||!a)&&cr(Ie(r,e),a)},n.isRangeEndQuarter=function(e){var t=n.props,r=t.day,o=t.startDate,a=t.endDate;return!(!o||!a)&&dr(Xe(r,e),a)},n.isInSelectingRangeMonth=function(e){var t,r=n.props,o=r.day,a=r.selectsStart,i=r.selectsEnd,s=r.selectsRange,l=r.startDate,c=r.endDate,d=null!==(t=n.props.selectingDate)&&void 0!==t?t:n.props.preSelection;return!(!(a||i||s)||!d)&&(a&&c?br(d,c,e,o):(i&&l||!(!s||!l||c))&&br(l,d,e,o))},n.isSelectingMonthRangeStart=function(e){var t;if(!n.isInSelectingRangeMonth(e))return!1;var r=n.props,o=r.day,a=r.startDate,i=r.selectsStart,s=Ie(o,e),l=null!==(t=n.props.selectingDate)&&void 0!==t?t:n.props.preSelection;return cr(s,i?l:a)},n.isSelectingMonthRangeEnd=function(e){var t;if(!n.isInSelectingRangeMonth(e))return!1;var r=n.props,o=r.day,a=r.endDate,i=r.selectsEnd,s=r.selectsRange,l=Ie(o,e),c=null!==(t=n.props.selectingDate)&&void 0!==t?t:n.props.preSelection;return cr(l,i||s?c:a)},n.isInSelectingRangeQuarter=function(e){var t,r=n.props,o=r.day,a=r.selectsStart,i=r.selectsEnd,s=r.selectsRange,l=r.startDate,c=r.endDate,d=null!==(t=n.props.selectingDate)&&void 0!==t?t:n.props.preSelection;return!(!(a||i||s)||!d)&&(a&&c?_r(d,c,e,o):(i&&l||!(!s||!l||c))&&_r(l,d,e,o))},n.isWeekInMonth=function(e){var t=n.props.day,r=Re(e,6);return cr(e,t)||cr(r,t)},n.isCurrentMonth=function(e,t){return se(e)===se(qn())&&t===le(qn())},n.isCurrentQuarter=function(e,t){return se(e)===se(qn())&&t===Ze(qn())},n.isSelectedMonth=function(e,t,n){return le(n)===t&&se(e)===se(n)},n.isSelectMonthInList=function(e,t,r){return r.some((function(r){return n.isSelectedMonth(e,t,r)}))},n.isSelectedQuarter=function(e,t,n){return Ze(e)===t&&se(e)===se(n)},n.renderWeeks=function(){for(var e,t,r=[],o=n.props.fixedHeight,a=0,i=!1,s=nr(rr(n.props.day),n.props.locale,n.props.calendarStartDay),l=n.props.selected?(e=n.props.selected,n.props.showWeekPicker?nr(e,n.props.locale,n.props.calendarStartDay):n.props.selected):void 0,c=n.props.preSelection?(t=n.props.preSelection,n.props.showWeekPicker?nr(t,n.props.locale,n.props.calendarStartDay):n.props.preSelection):void 0;r.push(h.createElement(Xr,Wn({},n.props,{ariaLabelPrefix:n.props.weekAriaLabelPrefix,key:a,day:s,month:le(n.props.day),onDayClick:n.handleDayClick,onDayMouseEnter:n.handleDayMouseEnter,selected:l,preSelection:c,showWeekNumber:n.props.showWeekNumbers}))),!i;){a++,s=He(s,1);var d=o&&a>=6,u=!o&&!n.isWeekInMonth(s);if(d||u){if(!n.props.peekNextMonth)break;i=!0}}return r},n.onMonthClick=function(e,t){var r=n.isMonthDisabledForLabelDate(t),o=r.isDisabled,a=r.labelDate;o||n.handleDayClick(rr(a),e)},n.onMonthMouseEnter=function(e){var t=n.isMonthDisabledForLabelDate(e),r=t.isDisabled,o=t.labelDate;r||n.handleDayMouseEnter(rr(o))},n.handleMonthNavigation=function(e,t){var r,o,a,i;null===(o=(r=n.props).setPreSelection)||void 0===o||o.call(r,t),null===(i=null===(a=n.MONTH_REFS[e])||void 0===a?void 0:a.current)||void 0===i||i.focus()},n.handleKeyboardNavigation=function(e,t,r){var o,a=n.props,i=a.selected,s=a.preSelection,l=a.setPreSelection,c=a.minDate,d=a.maxDate,u=a.showFourColumnMonthYearPicker,p=a.showTwoColumnMonthYearPicker;if(s){var h=to(u,p),f=n.getVerticalOffset(h),m=null===(o=eo[h])||void 0===o?void 0:o.grid,g=function(e,t,n){var r,o,a=t,i=n;switch(e){case Bn.ArrowRight:a=Fe(t,1),i=11===n?0:n+1;break;case Bn.ArrowLeft:a=Te(t,1),i=0===n?11:n-1;break;case Bn.ArrowUp:a=Te(t,f),i=(null===(r=null==m?void 0:m[0])||void 0===r?void 0:r.includes(n))?n+12-f:n-f;break;case Bn.ArrowDown:a=Fe(t,f),i=(null===(o=null==m?void 0:m[m.length-1])||void 0===o?void 0:o.includes(n))?n-12+f:n+f}return{newCalculatedDate:a,newCalculatedMonth:i}};if(t!==Bn.Enter){var v=function(e,t,r){for(var o=e,a=!1,i=0,s=g(o,t,r),l=s.newCalculatedDate,u=s.newCalculatedMonth;!a;){if(i>=40){l=t,u=r;break}var p;if(c&&l<c)o=Bn.ArrowRight,l=(p=g(o,l,u)).newCalculatedDate,u=p.newCalculatedMonth;if(d&&l>d)o=Bn.ArrowLeft,l=(p=g(o,l,u)).newCalculatedDate,u=p.newCalculatedMonth;if(Dr(l,n.props))l=(p=g(o,l,u)).newCalculatedDate,u=p.newCalculatedMonth;else a=!0;i++}return{newCalculatedDate:l,newCalculatedMonth:u}}(t,s,r),y=v.newCalculatedDate,x=v.newCalculatedMonth;switch(t){case Bn.ArrowRight:case Bn.ArrowLeft:case Bn.ArrowUp:case Bn.ArrowDown:n.handleMonthNavigation(x,y)}}else n.isMonthDisabled(r)||(n.onMonthClick(e,r),null==l||l(i))}},n.getVerticalOffset=function(e){var t,n;return null!==(n=null===(t=eo[e])||void 0===t?void 0:t.verticalNavigationOffset)&&void 0!==n?n:0},n.onMonthKeyDown=function(e,t){var r=n.props,o=r.disabledKeyboardNavigation,a=r.handleOnMonthKeyDown,i=e.key;i!==Bn.Tab&&e.preventDefault(),o||n.handleKeyboardNavigation(e,i,t),a&&a(e)},n.onQuarterClick=function(e,t){var r=Xe(n.props.day,t);Cr(r,n.props)||n.handleDayClick(ar(r),e)},n.onQuarterMouseEnter=function(e){var t=Xe(n.props.day,e);Cr(t,n.props)||n.handleDayMouseEnter(ar(t))},n.handleQuarterNavigation=function(e,t){var r,o,a,i;n.isDisabled(t)||n.isExcluded(t)||(null===(o=(r=n.props).setPreSelection)||void 0===o||o.call(r,t),null===(i=null===(a=n.QUARTER_REFS[e-1])||void 0===a?void 0:a.current)||void 0===i||i.focus())},n.onQuarterKeyDown=function(e,t){var r,o,a=e.key;if(!n.props.disabledKeyboardNavigation)switch(a){case Bn.Enter:n.onQuarterClick(e,t),null===(o=(r=n.props).setPreSelection)||void 0===o||o.call(r,n.props.selected);break;case Bn.ArrowRight:if(!n.props.preSelection)break;n.handleQuarterNavigation(4===t?1:t+1,Ue(n.props.preSelection,1));break;case Bn.ArrowLeft:if(!n.props.preSelection)break;n.handleQuarterNavigation(1===t?4:t-1,Ke(n.props.preSelection))}},n.isMonthDisabledForLabelDate=function(e){var t,r=n.props,o=r.day,a=r.minDate,i=r.maxDate,s=r.excludeDates,l=r.includeDates,c=Ie(o,e);return{isDisabled:null!==(t=(a||i||s||l)&&wr(c,n.props))&&void 0!==t&&t,labelDate:c}},n.isMonthDisabled=function(e){return n.isMonthDisabledForLabelDate(e).isDisabled},n.getMonthClassNames=function(e){var t=n.props,r=t.day,o=t.startDate,a=t.endDate,i=t.preSelection,s=t.monthClassName,l=s?s(Ie(r,e)):void 0,c=n.getSelection();return v("react-datepicker__month-text","react-datepicker__month-".concat(e),l,{"react-datepicker__month-text--disabled":n.isMonthDisabled(e),"react-datepicker__month-text--selected":c?n.isSelectMonthInList(r,e,c):void 0,"react-datepicker__month-text--keyboard-selected":!n.props.disabledKeyboardNavigation&&i&&n.isSelectedMonth(r,e,i)&&!n.isMonthDisabled(e),"react-datepicker__month-text--in-selecting-range":n.isInSelectingRangeMonth(e),"react-datepicker__month-text--in-range":o&&a?br(o,a,e,r):void 0,"react-datepicker__month-text--range-start":n.isRangeStartMonth(e),"react-datepicker__month-text--range-end":n.isRangeEndMonth(e),"react-datepicker__month-text--selecting-range-start":n.isSelectingMonthRangeStart(e),"react-datepicker__month-text--selecting-range-end":n.isSelectingMonthRangeEnd(e),"react-datepicker__month-text--today":n.isCurrentMonth(r,e)})},n.getTabIndex=function(e){if(null==n.props.preSelection)return"-1";var t=le(n.props.preSelection),r=n.isMonthDisabledForLabelDate(t).isDisabled;return e!==t||r||n.props.disabledKeyboardNavigation?"-1":"0"},n.getQuarterTabIndex=function(e){if(null==n.props.preSelection)return"-1";var t=Ze(n.props.preSelection),r=Cr(n.props.day,n.props);return e!==t||r||n.props.disabledKeyboardNavigation?"-1":"0"},n.getAriaLabel=function(e){var t=n.props,r=t.chooseDayAriaLabelPrefix,o=void 0===r?"Choose":r,a=t.disabledDayAriaLabelPrefix,i=void 0===a?"Not available":a,s=t.day,l=t.locale,c=Ie(s,e),d=n.isDisabled(c)||n.isExcluded(c)?i:o;return"".concat(d," ").concat($n(c,"MMMM yyyy",l))},n.getQuarterClassNames=function(e){var t=n.props,r=t.day,o=t.startDate,a=t.endDate,i=t.selected,s=t.minDate,l=t.maxDate,c=t.excludeDates,d=t.includeDates,u=t.filterDate,p=t.preSelection,h=t.disabledKeyboardNavigation,f=(s||l||c||d||u)&&Cr(Xe(r,e),n.props);return v("react-datepicker__quarter-text","react-datepicker__quarter-".concat(e),{"react-datepicker__quarter-text--disabled":f,"react-datepicker__quarter-text--selected":i?n.isSelectedQuarter(r,e,i):void 0,"react-datepicker__quarter-text--keyboard-selected":!h&&p&&n.isSelectedQuarter(r,e,p)&&!f,"react-datepicker__quarter-text--in-selecting-range":n.isInSelectingRangeQuarter(e),"react-datepicker__quarter-text--in-range":o&&a?_r(o,a,e,r):void 0,"react-datepicker__quarter-text--range-start":n.isRangeStartQuarter(e),"react-datepicker__quarter-text--range-end":n.isRangeEndQuarter(e)})},n.getMonthContent=function(e){var t=n.props,r=t.showFullMonthYearPicker,o=t.renderMonthContent,a=t.locale,i=t.day,s=vr(e,a),l=gr(e,a);return o?o(e,s,l,i):r?l:s},n.getQuarterContent=function(e){var t,r=n.props,o=r.renderQuarterContent,a=function(e,t){return $n(Xe(qn(),e),"QQQ",t)}(e,r.locale);return null!==(t=null==o?void 0:o(e,a))&&void 0!==t?t:a},n.renderMonths=function(){var e,t=n.props,r=t.showTwoColumnMonthYearPicker,o=t.showFourColumnMonthYearPicker,a=t.day,i=t.selected,s=null===(e=eo[to(o,r)])||void 0===e?void 0:e.grid;return null==s?void 0:s.map((function(e,t){return h.createElement("div",{className:"react-datepicker__month-wrapper",key:t},e.map((function(e,t){return h.createElement("div",{ref:n.MONTH_REFS[e],key:t,onClick:function(t){n.onMonthClick(t,e)},onKeyDown:function(t){Kr(t)&&(t.preventDefault(),t.key=Bn.Enter),n.onMonthKeyDown(t,e)},onMouseEnter:n.props.usePointerEvent?void 0:function(){return n.onMonthMouseEnter(e)},onPointerEnter:n.props.usePointerEvent?function(){return n.onMonthMouseEnter(e)}:void 0,tabIndex:Number(n.getTabIndex(e)),className:n.getMonthClassNames(e),"aria-disabled":n.isMonthDisabled(e),role:"option","aria-label":n.getAriaLabel(e),"aria-current":n.isCurrentMonth(a,e)?"date":void 0,"aria-selected":i?n.isSelectedMonth(a,e,i):void 0},n.getMonthContent(e))})))}))},n.renderQuarters=function(){var e=n.props,t=e.day,r=e.selected;return h.createElement("div",{className:"react-datepicker__quarter-wrapper"},[1,2,3,4].map((function(e,o){return h.createElement("div",{key:o,ref:n.QUARTER_REFS[o],role:"option",onClick:function(t){n.onQuarterClick(t,e)},onKeyDown:function(t){n.onQuarterKeyDown(t,e)},onMouseEnter:n.props.usePointerEvent?void 0:function(){return n.onQuarterMouseEnter(e)},onPointerEnter:n.props.usePointerEvent?function(){return n.onQuarterMouseEnter(e)}:void 0,className:n.getQuarterClassNames(e),"aria-selected":r?n.isSelectedQuarter(t,e,r):void 0,tabIndex:Number(n.getQuarterTabIndex(e)),"aria-current":n.isCurrentQuarter(t,e)?"date":void 0},n.getQuarterContent(e))})))},n.getClassNames=function(){var e=n.props,t=e.selectingDate,r=e.selectsStart,o=e.selectsEnd,a=e.showMonthYearPicker,i=e.showQuarterYearPicker,s=e.showWeekPicker;return v("react-datepicker__month",{"react-datepicker__month--selecting-range":t&&(r||o)},{"react-datepicker__monthPicker":a},{"react-datepicker__quarterPicker":i},{"react-datepicker__weekPicker":s})},n}return Hn(n,e),n.prototype.getSelection=function(){var e=this.props,t=e.selected,n=e.selectedDates;return e.selectsMultiple?n:t?[t]:void 0},n.prototype.render=function(){var e=this.props,t=e.showMonthYearPicker,n=e.showQuarterYearPicker,r=e.day,o=e.ariaLabelPrefix,a=void 0===o?"Month ":o,i=a?a.trim()+" ":"";return h.createElement("div",{className:this.getClassNames(),onMouseLeave:this.props.usePointerEvent?void 0:this.handleMouseLeave,onPointerLeave:this.props.usePointerEvent?this.handleMouseLeave:void 0,"aria-label":"".concat(i).concat($n(r,"MMMM, yyyy",this.props.locale)),role:"listbox"},t?this.renderMonths():n?this.renderQuarters():this.renderWeeks())},n}(t.Component),ro=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.isSelectedMonth=function(e){return t.props.month===e},t.renderOptions=function(){return t.props.monthNames.map((function(e,n){return h.createElement("div",{className:t.isSelectedMonth(n)?"react-datepicker__month-option react-datepicker__month-option--selected_month":"react-datepicker__month-option",key:e,onClick:t.onChange.bind(t,n),"aria-selected":t.isSelectedMonth(n)?"true":void 0},t.isSelectedMonth(n)?h.createElement("span",{className:"react-datepicker__month-option--selected"},"✓"):"",e)}))},t.onChange=function(e){return t.props.onChange(e)},t.handleClickOutside=function(){return t.props.onCancel()},t}return Hn(t,e),t.prototype.render=function(){return h.createElement(Qn,{className:"react-datepicker__month-dropdown",onClickOutside:this.handleClickOutside},this.renderOptions())},t}(t.Component),oo=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.state={dropdownVisible:!1},t.renderSelectOptions=function(e){return e.map((function(e,t){return h.createElement("option",{key:e,value:t},e)}))},t.renderSelectMode=function(e){return h.createElement("select",{value:t.props.month,className:"react-datepicker__month-select",onChange:function(e){return t.onChange(parseInt(e.target.value))}},t.renderSelectOptions(e))},t.renderReadView=function(e,n){return h.createElement("div",{key:"read",style:{visibility:e?"visible":"hidden"},className:"react-datepicker__month-read-view",onClick:t.toggleDropdown},h.createElement("span",{className:"react-datepicker__month-read-view--down-arrow"}),h.createElement("span",{className:"react-datepicker__month-read-view--selected-month"},n[t.props.month]))},t.renderDropdown=function(e){return h.createElement(ro,Wn({key:"dropdown"},t.props,{monthNames:e,onChange:t.onChange,onCancel:t.toggleDropdown}))},t.renderScrollMode=function(e){var n=t.state.dropdownVisible,r=[t.renderReadView(!n,e)];return n&&r.unshift(t.renderDropdown(e)),r},t.onChange=function(e){t.toggleDropdown(),e!==t.props.month&&t.props.onChange(e)},t.toggleDropdown=function(){return t.setState({dropdownVisible:!t.state.dropdownVisible})},t}return Hn(t,e),t.prototype.render=function(){var e,t=this,n=[0,1,2,3,4,5,6,7,8,9,10,11].map(this.props.useShortMonthInDropdown?function(e){return vr(e,t.props.locale)}:function(e){return gr(e,t.props.locale)});switch(this.props.dropdownMode){case"scroll":e=this.renderScrollMode(n);break;case"select":e=this.renderSelectMode(n)}return h.createElement("div",{className:"react-datepicker__month-dropdown-container react-datepicker__month-dropdown-container--".concat(this.props.dropdownMode)},e)},t}(t.Component);function ao(e,t){for(var n=[],r=rr(e),o=rr(t);!te(r,o);)n.push(qn(r)),r=Fe(r,1);return n}var io=function(e){function t(t){var n=e.call(this,t)||this;return n.renderOptions=function(){return n.state.monthYearsList.map((function(e){var t=qe(e),r=lr(n.props.date,e)&&cr(n.props.date,e);return h.createElement("div",{className:r?"react-datepicker__month-year-option--selected_month-year":"react-datepicker__month-year-option",key:t,onClick:n.onChange.bind(n,t),"aria-selected":r?"true":void 0},r?h.createElement("span",{className:"react-datepicker__month-year-option--selected"},"✓"):"",$n(e,n.props.dateFormat,n.props.locale))}))},n.onChange=function(e){return n.props.onChange(e)},n.handleClickOutside=function(){n.props.onCancel()},n.state={monthYearsList:ao(n.props.minDate,n.props.maxDate)},n}return Hn(t,e),t.prototype.render=function(){var e=v({"react-datepicker__month-year-dropdown":!0,"react-datepicker__month-year-dropdown--scrollable":this.props.scrollableMonthYearDropdown});return h.createElement(Qn,{className:e,onClickOutside:this.handleClickOutside},this.renderOptions())},t}(t.Component),so=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.state={dropdownVisible:!1},t.renderSelectOptions=function(){for(var e=rr(t.props.minDate),n=rr(t.props.maxDate),r=[];!te(e,n);){var o=qe(e);r.push(h.createElement("option",{key:o,value:o},$n(e,t.props.dateFormat,t.props.locale))),e=Fe(e,1)}return r},t.onSelectChange=function(e){t.onChange(parseInt(e.target.value))},t.renderSelectMode=function(){return h.createElement("select",{value:qe(rr(t.props.date)),className:"react-datepicker__month-year-select",onChange:t.onSelectChange},t.renderSelectOptions())},t.renderReadView=function(e){var n=$n(t.props.date,t.props.dateFormat,t.props.locale);return h.createElement("div",{key:"read",style:{visibility:e?"visible":"hidden"},className:"react-datepicker__month-year-read-view",onClick:t.toggleDropdown},h.createElement("span",{className:"react-datepicker__month-year-read-view--down-arrow"}),h.createElement("span",{className:"react-datepicker__month-year-read-view--selected-month-year"},n))},t.renderDropdown=function(){return h.createElement(io,Wn({key:"dropdown"},t.props,{onChange:t.onChange,onCancel:t.toggleDropdown}))},t.renderScrollMode=function(){var e=t.state.dropdownVisible,n=[t.renderReadView(!e)];return e&&n.unshift(t.renderDropdown()),n},t.onChange=function(e){t.toggleDropdown();var n=qn(e);lr(t.props.date,n)&&cr(t.props.date,n)||t.props.onChange(n)},t.toggleDropdown=function(){return t.setState({dropdownVisible:!t.state.dropdownVisible})},t}return Hn(t,e),t.prototype.render=function(){var e;switch(this.props.dropdownMode){case"scroll":e=this.renderScrollMode();break;case"select":e=this.renderSelectMode()}return h.createElement("div",{className:"react-datepicker__month-year-dropdown-container react-datepicker__month-year-dropdown-container--".concat(this.props.dropdownMode)},e)},t}(t.Component),lo=function(e){function t(){var n=null!==e&&e.apply(this,arguments)||this;return n.state={height:null},n.scrollToTheSelectedTime=function(){requestAnimationFrame((function(){var e,r,o;n.list&&(n.list.scrollTop=null!==(o=n.centerLi&&t.calcCenterPosition(n.props.monthRef?n.props.monthRef.clientHeight-(null!==(r=null===(e=n.header)||void 0===e?void 0:e.clientHeight)&&void 0!==r?r:0):n.list.clientHeight,n.centerLi))&&void 0!==o?o:0)}))},n.handleClick=function(e){var t,r;(n.props.minTime||n.props.maxTime)&&Tr(e,n.props)||(n.props.excludeTimes||n.props.includeTimes||n.props.filterTime)&&Fr(e,n.props)||null===(r=(t=n.props).onChange)||void 0===r||r.call(t,e)},n.isSelectedTime=function(e){return n.props.selected&&(t=n.props.selected,r=e,Vr(t).getTime()===Vr(r).getTime());var t,r},n.isDisabledTime=function(e){return(n.props.minTime||n.props.maxTime)&&Tr(e,n.props)||(n.props.excludeTimes||n.props.includeTimes||n.props.filterTime)&&Fr(e,n.props)},n.liClasses=function(e){var r,o=["react-datepicker__time-list-item",n.props.timeClassName?n.props.timeClassName(e):void 0];return n.isSelectedTime(e)&&o.push("react-datepicker__time-list-item--selected"),n.isDisabledTime(e)&&o.push("react-datepicker__time-list-item--disabled"),n.props.injectTimes&&(3600*ie(e)+60*ae(e)+oe(e))%(60*(null!==(r=n.props.intervals)&&void 0!==r?r:t.defaultProps.intervals))!=0&&o.push("react-datepicker__time-list-item--injected"),o.join(" ")},n.handleOnKeyDown=function(e,t){var r,o;e.key===Bn.Space&&(e.preventDefault(),e.key=Bn.Enter),(e.key===Bn.ArrowUp||e.key===Bn.ArrowLeft)&&e.target instanceof HTMLElement&&e.target.previousSibling&&(e.preventDefault(),e.target.previousSibling instanceof HTMLElement&&e.target.previousSibling.focus()),(e.key===Bn.ArrowDown||e.key===Bn.ArrowRight)&&e.target instanceof HTMLElement&&e.target.nextSibling&&(e.preventDefault(),e.target.nextSibling instanceof HTMLElement&&e.target.nextSibling.focus()),e.key===Bn.Enter&&n.handleClick(t),null===(o=(r=n.props).handleOnKeyDown)||void 0===o||o.call(r,e)},n.renderTimes=function(){for(var e,r,o,a,i=[],s="string"==typeof n.props.format?n.props.format:"p",l=null!==(e=n.props.intervals)&&void 0!==e?e:t.defaultProps.intervals,c=n.props.selected||n.props.openToDate||qn(),d=tr(c),u=n.props.injectTimes&&n.props.injectTimes.sort((function(e,t){return e.getTime()-t.getTime()})),p=60*(r=c,o=new Date(r.getFullYear(),r.getMonth(),r.getDate()),a=new Date(r.getFullYear(),r.getMonth(),r.getDate(),24),Math.round((+a-+o)/36e5))/l,f=0;f<p;f++){var m=$e(d,f*l);if(i.push(m),u){var g=Yr(d,m,f,l,u);i=i.concat(g)}}var v=i.reduce((function(e,t){return t.getTime()<=c.getTime()?t:e}),i[0]);return i.map((function(e){return h.createElement("li",{key:e.valueOf(),onClick:n.handleClick.bind(n,e),className:n.liClasses(e),ref:function(t){e===v&&(n.centerLi=t)},onKeyDown:function(t){n.handleOnKeyDown(t,e)},tabIndex:e===v?0:-1,role:"option","aria-selected":n.isSelectedTime(e)?"true":void 0,"aria-disabled":n.isDisabledTime(e)?"true":void 0},$n(e,s,n.props.locale))}))},n.renderTimeCaption=function(){return!1===n.props.showTimeCaption?h.createElement(h.Fragment,null):h.createElement("div",{className:"react-datepicker__header react-datepicker__header--time ".concat(n.props.showTimeSelectOnly?"react-datepicker__header--time--only":""),ref:function(e){n.header=e}},h.createElement("div",{className:"react-datepicker-time__header"},n.props.timeCaption))},n}return Hn(t,e),Object.defineProperty(t,"defaultProps",{get:function(){return{intervals:30,todayButton:null,timeCaption:"Time",showTimeCaption:!0}},enumerable:!1,configurable:!0}),t.prototype.componentDidMount=function(){this.scrollToTheSelectedTime(),this.props.monthRef&&this.header&&this.setState({height:this.props.monthRef.clientHeight-this.header.clientHeight})},t.prototype.render=function(){var e,n=this,r=this.state.height;return h.createElement("div",{className:"react-datepicker__time-container ".concat((null!==(e=this.props.todayButton)&&void 0!==e?e:t.defaultProps.todayButton)?"react-datepicker__time-container--with-today-button":"")},this.renderTimeCaption(),h.createElement("div",{className:"react-datepicker__time"},h.createElement("div",{className:"react-datepicker__time-box"},h.createElement("ul",{className:"react-datepicker__time-list",ref:function(e){n.list=e},style:r?{height:r}:{},role:"listbox","aria-label":this.props.timeCaption},this.renderTimes()))))},t.calcCenterPosition=function(e,t){return t.offsetTop-(e/2-t.clientHeight/2)},t}(t.Component),co=function(e){function n(n){var r=e.call(this,n)||this;return r.YEAR_REFS=Vn([],Array(r.props.yearItemNumber),!0).map((function(){return t.createRef()})),r.isDisabled=function(e){return yr(e,{minDate:r.props.minDate,maxDate:r.props.maxDate,excludeDates:r.props.excludeDates,includeDates:r.props.includeDates,filterDate:r.props.filterDate})},r.isExcluded=function(e){return xr(e,{excludeDates:r.props.excludeDates})},r.selectingDate=function(){var e;return null!==(e=r.props.selectingDate)&&void 0!==e?e:r.props.preSelection},r.updateFocusOnPaginate=function(e){window.requestAnimationFrame((function(){var t,n;null===(n=null===(t=r.YEAR_REFS[e])||void 0===t?void 0:t.current)||void 0===n||n.focus()}))},r.handleYearClick=function(e,t){r.props.onDayClick&&r.props.onDayClick(e,t)},r.handleYearNavigation=function(e,t){var n,o,a,i,s=r.props,l=s.date,c=s.yearItemNumber;if(void 0!==l&&void 0!==c){var d=Wr(l,c).startPeriod;r.isDisabled(t)||r.isExcluded(t)||(null===(o=(n=r.props).setPreSelection)||void 0===o||o.call(n,t),e-d<0?r.updateFocusOnPaginate(c-(d-e)):e-d>=c?r.updateFocusOnPaginate(Math.abs(c-(e-d))):null===(i=null===(a=r.YEAR_REFS[e-d])||void 0===a?void 0:a.current)||void 0===i||i.focus())}},r.isSameDay=function(e,t){return ur(e,t)},r.isCurrentYear=function(e){return e===se(qn())},r.isRangeStart=function(e){return r.props.startDate&&r.props.endDate&&lr(je(qn(),e),r.props.startDate)},r.isRangeEnd=function(e){return r.props.startDate&&r.props.endDate&&lr(je(qn(),e),r.props.endDate)},r.isInRange=function(e){return Sr(e,r.props.startDate,r.props.endDate)},r.isInSelectingRange=function(e){var t=r.props,n=t.selectsStart,o=t.selectsEnd,a=t.selectsRange,i=t.startDate,s=t.endDate;return!(!(n||o||a)||!r.selectingDate())&&(n&&s?Sr(e,r.selectingDate(),s):(o&&i||!(!a||!i||s))&&Sr(e,i,r.selectingDate()))},r.isSelectingRangeStart=function(e){var t;if(!r.isInSelectingRange(e))return!1;var n=r.props,o=n.startDate,a=n.selectsStart,i=je(qn(),e);return lr(i,a?null!==(t=r.selectingDate())&&void 0!==t?t:null:null!=o?o:null)},r.isSelectingRangeEnd=function(e){var t;if(!r.isInSelectingRange(e))return!1;var n=r.props,o=n.endDate,a=n.selectsEnd,i=n.selectsRange,s=je(qn(),e);return lr(s,a||i?null!==(t=r.selectingDate())&&void 0!==t?t:null:null!=o?o:null)},r.isKeyboardSelected=function(e){if(void 0!==r.props.date&&null!=r.props.selected&&null!=r.props.preSelection){var t=r.props,n=t.minDate,o=t.maxDate,a=t.excludeDates,i=t.includeDates,s=t.filterDate,l=or(je(r.props.date,e)),c=(n||o||a||i||s)&&kr(e,r.props);return!r.props.disabledKeyboardNavigation&&!r.props.inline&&!ur(l,or(r.props.selected))&&ur(l,or(r.props.preSelection))&&!c}},r.onYearClick=function(e,t){var n=r.props.date;void 0!==n&&r.handleYearClick(or(je(n,t)),e)},r.onYearKeyDown=function(e,t){var n,o,a=e.key,i=r.props,s=i.date,l=i.yearItemNumber,c=i.handleOnKeyDown;if(a!==Bn.Tab&&e.preventDefault(),!r.props.disabledKeyboardNavigation)switch(a){case Bn.Enter:if(null==r.props.selected)break;r.onYearClick(e,t),null===(o=(n=r.props).setPreSelection)||void 0===o||o.call(n,r.props.selected);break;case Bn.ArrowRight:if(null==r.props.preSelection)break;r.handleYearNavigation(t+1,Ne(r.props.preSelection,1));break;case Bn.ArrowLeft:if(null==r.props.preSelection)break;r.handleYearNavigation(t-1,Ae(r.props.preSelection,1));break;case Bn.ArrowUp:if(void 0===s||void 0===l||null==r.props.preSelection)break;var d=Wr(s,l).startPeriod;if((h=t-(p=3))<d){var u=l%p;t>=d&&t<d+u?p=u:p+=u,h=t-p}r.handleYearNavigation(h,Ae(r.props.preSelection,p));break;case Bn.ArrowDown:if(void 0===s||void 0===l||null==r.props.preSelection)break;var p,h,f=Wr(s,l).endPeriod;if((h=t+(p=3))>f){u=l%p;t<=f&&t>f-u?p=u:p+=u,h=t+p}r.handleYearNavigation(h,Ne(r.props.preSelection,p))}c&&c(e)},r.getYearClassNames=function(e){var t=r.props,n=t.date,o=t.minDate,a=t.maxDate,i=t.selected,s=t.excludeDates,l=t.includeDates,c=t.filterDate,d=t.yearClassName;return v("react-datepicker__year-text","react-datepicker__year-".concat(e),n?null==d?void 0:d(je(n,e)):void 0,{"react-datepicker__year-text--selected":i?e===se(i):void 0,"react-datepicker__year-text--disabled":(o||a||s||l||c)&&kr(e,r.props),"react-datepicker__year-text--keyboard-selected":r.isKeyboardSelected(e),"react-datepicker__year-text--range-start":r.isRangeStart(e),"react-datepicker__year-text--range-end":r.isRangeEnd(e),"react-datepicker__year-text--in-range":r.isInRange(e),"react-datepicker__year-text--in-selecting-range":r.isInSelectingRange(e),"react-datepicker__year-text--selecting-range-start":r.isSelectingRangeStart(e),"react-datepicker__year-text--selecting-range-end":r.isSelectingRangeEnd(e),"react-datepicker__year-text--today":r.isCurrentYear(e)})},r.getYearTabIndex=function(e){if(r.props.disabledKeyboardNavigation||null==r.props.preSelection)return"-1";var t=se(r.props.preSelection),n=kr(e,r.props);return e!==t||n?"-1":"0"},r.getYearContent=function(e){return r.props.renderYearContent?r.props.renderYearContent(e):e},r}return Hn(n,e),n.prototype.render=function(){var e=this,t=[],n=this.props,r=n.date,o=n.yearItemNumber,a=n.onYearMouseEnter,i=n.onYearMouseLeave;if(void 0===r)return null;for(var s=Wr(r,o),l=s.startPeriod,c=s.endPeriod,d=function(n){t.push(h.createElement("div",{ref:u.YEAR_REFS[n-l],onClick:function(t){e.onYearClick(t,n)},onKeyDown:function(t){Kr(t)&&(t.preventDefault(),t.key=Bn.Enter),e.onYearKeyDown(t,n)},tabIndex:Number(u.getYearTabIndex(n)),className:u.getYearClassNames(n),onMouseEnter:u.props.usePointerEvent?void 0:function(e){return a(e,n)},onPointerEnter:u.props.usePointerEvent?function(e){return a(e,n)}:void 0,onMouseLeave:u.props.usePointerEvent?void 0:function(e){return i(e,n)},onPointerLeave:u.props.usePointerEvent?function(e){return i(e,n)}:void 0,key:n,"aria-current":u.isCurrentYear(n)?"date":void 0},u.getYearContent(n)))},u=this,p=l;p<=c;p++)d(p);return h.createElement("div",{className:"react-datepicker__year"},h.createElement("div",{className:"react-datepicker__year-wrapper",onMouseLeave:this.props.usePointerEvent?void 0:this.props.clearSelectingDate,onPointerLeave:this.props.usePointerEvent?this.props.clearSelectingDate:void 0},t))},n}(t.Component);function uo(e,t,n,r){for(var o=[],a=0;a<2*t+1;a++){var i=e+t-a,s=!0;n&&(s=se(n)<=i),r&&s&&(s=se(r)>=i),s&&o.push(i)}return o}var po=function(e){function n(n){var r=e.call(this,n)||this;r.renderOptions=function(){var e=r.props.year,t=r.state.yearsList.map((function(t){return h.createElement("div",{className:e===t?"react-datepicker__year-option react-datepicker__year-option--selected_year":"react-datepicker__year-option",key:t,onClick:r.onChange.bind(r,t),"aria-selected":e===t?"true":void 0},e===t?h.createElement("span",{className:"react-datepicker__year-option--selected"},"✓"):"",t)})),n=r.props.minDate?se(r.props.minDate):null,o=r.props.maxDate?se(r.props.maxDate):null;return o&&r.state.yearsList.find((function(e){return e===o}))||t.unshift(h.createElement("div",{className:"react-datepicker__year-option",key:"upcoming",onClick:r.incrementYears},h.createElement("a",{className:"react-datepicker__navigation react-datepicker__navigation--years react-datepicker__navigation--years-upcoming"}))),n&&r.state.yearsList.find((function(e){return e===n}))||t.push(h.createElement("div",{className:"react-datepicker__year-option",key:"previous",onClick:r.decrementYears},h.createElement("a",{className:"react-datepicker__navigation react-datepicker__navigation--years react-datepicker__navigation--years-previous"}))),t},r.onChange=function(e){r.props.onChange(e)},r.handleClickOutside=function(){r.props.onCancel()},r.shiftYears=function(e){var t=r.state.yearsList.map((function(t){return t+e}));r.setState({yearsList:t})},r.incrementYears=function(){return r.shiftYears(1)},r.decrementYears=function(){return r.shiftYears(-1)};var o=n.yearDropdownItemNumber,a=n.scrollableYearDropdown,i=o||(a?10:5);return r.state={yearsList:uo(r.props.year,i,r.props.minDate,r.props.maxDate)},r.dropdownRef=t.createRef(),r}return Hn(n,e),n.prototype.componentDidMount=function(){var e=this.dropdownRef.current;if(e){var t=e.children?Array.from(e.children):null,n=t?t.find((function(e){return e.ariaSelected})):null;e.scrollTop=n&&n instanceof HTMLElement?n.offsetTop+(n.clientHeight-e.clientHeight)/2:(e.scrollHeight-e.clientHeight)/2}},n.prototype.render=function(){var e=v({"react-datepicker__year-dropdown":!0,"react-datepicker__year-dropdown--scrollable":this.props.scrollableYearDropdown});return h.createElement(Qn,{className:e,containerRef:this.dropdownRef,onClickOutside:this.handleClickOutside},this.renderOptions())},n}(t.Component),ho=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.state={dropdownVisible:!1},t.renderSelectOptions=function(){for(var e=t.props.minDate?se(t.props.minDate):1900,n=t.props.maxDate?se(t.props.maxDate):2100,r=[],o=e;o<=n;o++)r.push(h.createElement("option",{key:o,value:o},o));return r},t.onSelectChange=function(e){t.onChange(parseInt(e.target.value))},t.renderSelectMode=function(){return h.createElement("select",{value:t.props.year,className:"react-datepicker__year-select",onChange:t.onSelectChange},t.renderSelectOptions())},t.renderReadView=function(e){return h.createElement("div",{key:"read",style:{visibility:e?"visible":"hidden"},className:"react-datepicker__year-read-view",onClick:function(e){return t.toggleDropdown(e)}},h.createElement("span",{className:"react-datepicker__year-read-view--down-arrow"}),h.createElement("span",{className:"react-datepicker__year-read-view--selected-year"},t.props.year))},t.renderDropdown=function(){return h.createElement(po,Wn({key:"dropdown"},t.props,{onChange:t.onChange,onCancel:t.toggleDropdown}))},t.renderScrollMode=function(){var e=t.state.dropdownVisible,n=[t.renderReadView(!e)];return e&&n.unshift(t.renderDropdown()),n},t.onChange=function(e){t.toggleDropdown(),e!==t.props.year&&t.props.onChange(e)},t.toggleDropdown=function(e){t.setState({dropdownVisible:!t.state.dropdownVisible},(function(){t.props.adjustDateOnChange&&t.handleYearChange(t.props.date,e)}))},t.handleYearChange=function(e,n){var r;null===(r=t.onSelect)||void 0===r||r.call(t,e,n),t.setOpen()},t.onSelect=function(e,n){var r,o;null===(o=(r=t.props).onSelect)||void 0===o||o.call(r,e,n)},t.setOpen=function(){var e,n;null===(n=(e=t.props).setOpen)||void 0===n||n.call(e,!0)},t}return Hn(t,e),t.prototype.render=function(){var e;switch(this.props.dropdownMode){case"scroll":e=this.renderScrollMode();break;case"select":e=this.renderSelectMode()}return h.createElement("div",{className:"react-datepicker__year-dropdown-container react-datepicker__year-dropdown-container--".concat(this.props.dropdownMode)},e)},t}(t.Component),fo=["react-datepicker__year-select","react-datepicker__month-select","react-datepicker__month-year-select"],mo=function(e){function n(r){var o=e.call(this,r)||this;return o.monthContainer=void 0,o.handleClickOutside=function(e){o.props.onClickOutside(e)},o.setClickOutsideRef=function(){return o.containerRef.current},o.handleDropdownFocus=function(e){var t,n,r,a;r=e.target,a=(r.className||"").split(/\s+/),fo.some((function(e){return a.indexOf(e)>=0}))&&(null===(n=(t=o.props).onDropdownFocus)||void 0===n||n.call(t,e))},o.getDateInView=function(){var e=o.props,t=e.preSelection,n=e.selected,r=e.openToDate,a=Ar(o.props),i=Nr(o.props),s=qn(),l=r||n||t;return l||(a&&ee(s,a)?a:i&&te(s,i)?i:s)},o.increaseMonth=function(){o.setState((function(e){var t=e.date;return{date:Fe(t,1)}}),(function(){return o.handleMonthChange(o.state.date)}))},o.decreaseMonth=function(){o.setState((function(e){var t=e.date;return{date:Te(t,1)}}),(function(){return o.handleMonthChange(o.state.date)}))},o.handleDayClick=function(e,t,n){o.props.onSelect(e,t,n),o.props.setPreSelection&&o.props.setPreSelection(e)},o.handleDayMouseEnter=function(e){o.setState({selectingDate:e}),o.props.onDayMouseEnter&&o.props.onDayMouseEnter(e)},o.handleMonthMouseLeave=function(){o.setState({selectingDate:void 0}),o.props.onMonthMouseLeave&&o.props.onMonthMouseLeave()},o.handleYearMouseEnter=function(e,t){o.setState({selectingDate:je(qn(),t)}),o.props.onYearMouseEnter&&o.props.onYearMouseEnter(e,t)},o.handleYearMouseLeave=function(e,t){o.props.onYearMouseLeave&&o.props.onYearMouseLeave(e,t)},o.handleYearChange=function(e){var t,n,r,a;null===(n=(t=o.props).onYearChange)||void 0===n||n.call(t,e),o.setState({isRenderAriaLiveMessage:!0}),o.props.adjustDateOnChange&&(o.props.onSelect(e),null===(a=(r=o.props).setOpen)||void 0===a||a.call(r,!0)),o.props.setPreSelection&&o.props.setPreSelection(e)},o.getEnabledPreSelectionDateForMonth=function(e){if(!yr(e,o.props))return e;for(var t=rr(e),n=function(e){return Se(e)}(e),r=Pe(n,t),a=null,i=0;i<=r;i++){var s=Re(t,i);if(!yr(s,o.props)){a=s;break}}return a},o.handleMonthChange=function(e){var t,n,r,a=null!==(t=o.getEnabledPreSelectionDateForMonth(e))&&void 0!==t?t:e;o.handleCustomMonthChange(a),o.props.adjustDateOnChange&&(o.props.onSelect(a),null===(r=(n=o.props).setOpen)||void 0===r||r.call(n,!0)),o.props.setPreSelection&&o.props.setPreSelection(a)},o.handleCustomMonthChange=function(e){var t,n;null===(n=(t=o.props).onMonthChange)||void 0===n||n.call(t,e),o.setState({isRenderAriaLiveMessage:!0})},o.handleMonthYearChange=function(e){o.handleYearChange(e),o.handleMonthChange(e)},o.changeYear=function(e){o.setState((function(t){var n=t.date;return{date:je(n,Number(e))}}),(function(){return o.handleYearChange(o.state.date)}))},o.changeMonth=function(e){o.setState((function(t){var n=t.date;return{date:Ie(n,Number(e))}}),(function(){return o.handleMonthChange(o.state.date)}))},o.changeMonthYear=function(e){o.setState((function(t){var n=t.date;return{date:je(Ie(n,le(e)),se(e))}}),(function(){return o.handleMonthYearChange(o.state.date)}))},o.header=function(e){void 0===e&&(e=o.state.date);var t=nr(e,o.props.locale,o.props.calendarStartDay),n=[];return o.props.showWeekNumbers&&n.push(h.createElement("div",{key:"W",className:"react-datepicker__day-name"},o.props.weekLabel||"#")),n.concat([0,1,2,3,4,5,6].map((function(e){var n=Re(t,e),r=o.formatWeekday(n,o.props.locale),a=o.props.weekDayClassName?o.props.weekDayClassName(n):void 0;return h.createElement("div",{key:e,"aria-label":$n(n,"EEEE",o.props.locale),className:v("react-datepicker__day-name",a)},r)})))},o.formatWeekday=function(e,t){return o.props.formatWeekDay?function(e,t,n){return t($n(e,"EEEE",n))}(e,o.props.formatWeekDay,t):o.props.useWeekdaysShort?function(e,t){return $n(e,"EEE",t)}(e,t):function(e,t){return $n(e,"EEEEEE",t)}(e,t)},o.decreaseYear=function(){o.setState((function(e){var t,r=e.date;return{date:Ae(r,o.props.showYearPicker?null!==(t=o.props.yearItemNumber)&&void 0!==t?t:n.defaultProps.yearItemNumber:1)}}),(function(){return o.handleYearChange(o.state.date)}))},o.clearSelectingDate=function(){o.setState({selectingDate:void 0})},o.renderPreviousButton=function(){var e,t,r;if(!o.props.renderCustomHeader){var a,i=null!==(e=o.props.monthsShown)&&void 0!==e?e:n.defaultProps.monthsShown,s=o.props.showPreviousMonths?i-1:0,l=null!==(t=o.props.monthSelectedIn)&&void 0!==t?t:s,c=Te(o.state.date,l);switch(!0){case o.props.showMonthYearPicker:a=Rr(o.state.date,o.props);break;case o.props.showYearPicker:a=function(e,t){var n=void 0===t?{}:t,r=n.minDate,o=n.yearItemNumber,a=void 0===o?12:o,i=Wr(or(Ae(e,a)),a).endPeriod,s=r&&se(r);return s&&s>i||!1}(o.state.date,o.props);break;case o.props.showQuarterYearPicker:a=function(e,t){var n=void 0===t?{}:t,r=n.minDate,o=n.includeDates,a=Ce(e),i=Ke(a);return r&&Qe(r,i)>0||o&&o.every((function(e){return Qe(e,i)>0}))||!1}(o.state.date,o.props);break;default:a=jr(c,o.props)}if(((null!==(r=o.props.forceShowMonthNavigation)&&void 0!==r?r:n.defaultProps.forceShowMonthNavigation)||o.props.showDisabledMonthNavigation||!a)&&!o.props.showTimeSelectOnly){var d=["react-datepicker__navigation","react-datepicker__navigation--previous"],u=o.decreaseMonth;(o.props.showMonthYearPicker||o.props.showQuarterYearPicker||o.props.showYearPicker)&&(u=o.decreaseYear),a&&o.props.showDisabledMonthNavigation&&(d.push("react-datepicker__navigation--previous--disabled"),u=void 0);var p=o.props.showMonthYearPicker||o.props.showQuarterYearPicker||o.props.showYearPicker,f=o.props,m=f.previousMonthButtonLabel,g=void 0===m?n.defaultProps.previousMonthButtonLabel:m,v=f.previousYearButtonLabel,y=void 0===v?n.defaultProps.previousYearButtonLabel:v,x=o.props,w=x.previousMonthAriaLabel,b=void 0===w?"string"==typeof g?g:"Previous Month":w,D=x.previousYearAriaLabel,C=void 0===D?"string"==typeof y?y:"Previous Year":D;return h.createElement("button",{type:"button",className:d.join(" "),onClick:u,onKeyDown:o.props.handleOnKeyDown,"aria-label":p?C:b},h.createElement("span",{className:["react-datepicker__navigation-icon","react-datepicker__navigation-icon--previous"].join(" ")},p?y:g))}}},o.increaseYear=function(){o.setState((function(e){var t,r=e.date;return{date:Ne(r,o.props.showYearPicker?null!==(t=o.props.yearItemNumber)&&void 0!==t?t:n.defaultProps.yearItemNumber:1)}}),(function(){return o.handleYearChange(o.state.date)}))},o.renderNextButton=function(){var e;if(!o.props.renderCustomHeader){var t;switch(!0){case o.props.showMonthYearPicker:t=Ir(o.state.date,o.props);break;case o.props.showYearPicker:t=function(e,t){var n=void 0===t?{}:t,r=n.maxDate,o=n.yearItemNumber,a=void 0===o?12:o,i=Wr(Ne(e,a),a).startPeriod,s=r&&se(r);return s&&s<i||!1}(o.state.date,o.props);break;case o.props.showQuarterYearPicker:t=function(e,t){var n=void 0===t?{}:t,r=n.maxDate,o=n.includeDates,a=De(e),i=Ue(a,1);return r&&Qe(i,r)>0||o&&o.every((function(e){return Qe(i,e)>0}))||!1}(o.state.date,o.props);break;default:t=Pr(o.state.date,o.props)}if(((null!==(e=o.props.forceShowMonthNavigation)&&void 0!==e?e:n.defaultProps.forceShowMonthNavigation)||o.props.showDisabledMonthNavigation||!t)&&!o.props.showTimeSelectOnly){var r=["react-datepicker__navigation","react-datepicker__navigation--next"];o.props.showTimeSelect&&r.push("react-datepicker__navigation--next--with-time"),o.props.todayButton&&r.push("react-datepicker__navigation--next--with-today-button");var a=o.increaseMonth;(o.props.showMonthYearPicker||o.props.showQuarterYearPicker||o.props.showYearPicker)&&(a=o.increaseYear),t&&o.props.showDisabledMonthNavigation&&(r.push("react-datepicker__navigation--next--disabled"),a=void 0);var i=o.props.showMonthYearPicker||o.props.showQuarterYearPicker||o.props.showYearPicker,s=o.props,l=s.nextMonthButtonLabel,c=void 0===l?n.defaultProps.nextMonthButtonLabel:l,d=s.nextYearButtonLabel,u=void 0===d?n.defaultProps.nextYearButtonLabel:d,p=o.props,f=p.nextMonthAriaLabel,m=void 0===f?"string"==typeof c?c:"Next Month":f,g=p.nextYearAriaLabel,v=void 0===g?"string"==typeof u?u:"Next Year":g;return h.createElement("button",{type:"button",className:r.join(" "),onClick:a,onKeyDown:o.props.handleOnKeyDown,"aria-label":i?v:m},h.createElement("span",{className:["react-datepicker__navigation-icon","react-datepicker__navigation-icon--next"].join(" ")},i?u:c))}}},o.renderCurrentMonth=function(e){void 0===e&&(e=o.state.date);var t=["react-datepicker__current-month"];return o.props.showYearDropdown&&t.push("react-datepicker__current-month--hasYearDropdown"),o.props.showMonthDropdown&&t.push("react-datepicker__current-month--hasMonthDropdown"),o.props.showMonthYearDropdown&&t.push("react-datepicker__current-month--hasMonthYearDropdown"),h.createElement("h2",{className:t.join(" ")},$n(e,o.props.dateFormat,o.props.locale))},o.renderYearDropdown=function(e){if(void 0===e&&(e=!1),o.props.showYearDropdown&&!e)return h.createElement(ho,Wn({},n.defaultProps,o.props,{date:o.state.date,onChange:o.changeYear,year:se(o.state.date)}))},o.renderMonthDropdown=function(e){if(void 0===e&&(e=!1),o.props.showMonthDropdown&&!e)return h.createElement(oo,Wn({},n.defaultProps,o.props,{month:le(o.state.date),onChange:o.changeMonth}))},o.renderMonthYearDropdown=function(e){if(void 0===e&&(e=!1),o.props.showMonthYearDropdown&&!e)return h.createElement(so,Wn({},n.defaultProps,o.props,{date:o.state.date,onChange:o.changeMonthYear}))},o.handleTodayButtonClick=function(e){o.props.onSelect(ir(),e),o.props.setPreSelection&&o.props.setPreSelection(ir())},o.renderTodayButton=function(){if(o.props.todayButton&&!o.props.showTimeSelectOnly)return h.createElement("div",{className:"react-datepicker__today-button",onClick:o.handleTodayButtonClick},o.props.todayButton)},o.renderDefaultHeader=function(e){var t=e.monthDate,n=e.i;return h.createElement("div",{className:"react-datepicker__header ".concat(o.props.showTimeSelect?"react-datepicker__header--has-time-select":"")},o.renderCurrentMonth(t),h.createElement("div",{className:"react-datepicker__header__dropdown react-datepicker__header__dropdown--".concat(o.props.dropdownMode),onFocus:o.handleDropdownFocus},o.renderMonthDropdown(0!==n),o.renderMonthYearDropdown(0!==n),o.renderYearDropdown(0!==n)),h.createElement("div",{className:"react-datepicker__day-names"},o.header(t)))},o.renderCustomHeader=function(e){var t,n,r=e.monthDate,a=e.i;if(o.props.showTimeSelect&&!o.state.monthContainer||o.props.showTimeSelectOnly)return null;var i=jr(o.state.date,o.props),s=Pr(o.state.date,o.props),l=Rr(o.state.date,o.props),c=Ir(o.state.date,o.props),d=!o.props.showMonthYearPicker&&!o.props.showQuarterYearPicker&&!o.props.showYearPicker;return h.createElement("div",{className:"react-datepicker__header react-datepicker__header--custom",onFocus:o.props.onDropdownFocus},null===(n=(t=o.props).renderCustomHeader)||void 0===n?void 0:n.call(t,Wn(Wn({},o.state),{customHeaderCount:a,monthDate:r,changeMonth:o.changeMonth,changeYear:o.changeYear,decreaseMonth:o.decreaseMonth,increaseMonth:o.increaseMonth,decreaseYear:o.decreaseYear,increaseYear:o.increaseYear,prevMonthButtonDisabled:i,nextMonthButtonDisabled:s,prevYearButtonDisabled:l,nextYearButtonDisabled:c})),d&&h.createElement("div",{className:"react-datepicker__day-names"},o.header(r)))},o.renderYearHeader=function(e){var t=e.monthDate,r=o.props,a=r.showYearPicker,i=r.yearItemNumber,s=Wr(t,void 0===i?n.defaultProps.yearItemNumber:i),l=s.startPeriod,c=s.endPeriod;return h.createElement("div",{className:"react-datepicker__header react-datepicker-year-header"},a?"".concat(l," - ").concat(c):se(t))},o.renderHeader=function(e){var t=e.monthDate,n=e.i,r={monthDate:t,i:void 0===n?0:n};switch(!0){case void 0!==o.props.renderCustomHeader:return o.renderCustomHeader(r);case o.props.showMonthYearPicker||o.props.showQuarterYearPicker||o.props.showYearPicker:return o.renderYearHeader(r);default:return o.renderDefaultHeader(r)}},o.renderMonths=function(){var e,t;if(!o.props.showTimeSelectOnly&&!o.props.showYearPicker){for(var r=[],a=null!==(e=o.props.monthsShown)&&void 0!==e?e:n.defaultProps.monthsShown,i=o.props.showPreviousMonths?a-1:0,s=o.props.showMonthYearPicker||o.props.showQuarterYearPicker?Ne(o.state.date,i):Te(o.state.date,i),l=null!==(t=o.props.monthSelectedIn)&&void 0!==t?t:i,c=0;c<a;++c){var d=c-l+i,u=o.props.showMonthYearPicker||o.props.showQuarterYearPicker?Ne(s,d):Fe(s,d),p="month-".concat(c),f=c<a-1,m=c>0;r.push(h.createElement("div",{key:p,ref:function(e){o.monthContainer=null!=e?e:void 0},className:"react-datepicker__month-container"},o.renderHeader({monthDate:u,i:c}),h.createElement(no,Wn({},n.defaultProps,o.props,{ariaLabelPrefix:o.props.monthAriaLabelPrefix,day:u,onDayClick:o.handleDayClick,handleOnKeyDown:o.props.handleOnDayKeyDown,handleOnMonthKeyDown:o.props.handleOnKeyDown,onDayMouseEnter:o.handleDayMouseEnter,onMouseLeave:o.handleMonthMouseLeave,orderInDisplay:c,selectingDate:o.state.selectingDate,monthShowsDuplicateDaysEnd:f,monthShowsDuplicateDaysStart:m}))))}return r}},o.renderYears=function(){if(!o.props.showTimeSelectOnly)return o.props.showYearPicker?h.createElement("div",{className:"react-datepicker__year--container"},o.renderHeader({monthDate:o.state.date}),h.createElement(co,Wn({},n.defaultProps,o.props,{selectingDate:o.state.selectingDate,date:o.state.date,onDayClick:o.handleDayClick,clearSelectingDate:o.clearSelectingDate,onYearMouseEnter:o.handleYearMouseEnter,onYearMouseLeave:o.handleYearMouseLeave}))):void 0},o.renderTimeSection=function(){if(o.props.showTimeSelect&&(o.state.monthContainer||o.props.showTimeSelectOnly))return h.createElement(lo,Wn({},n.defaultProps,o.props,{onChange:o.props.onTimeChange,format:o.props.timeFormat,intervals:o.props.timeIntervals,monthRef:o.state.monthContainer}))},o.renderInputTimeSection=function(){var e=o.props.selected?new Date(o.props.selected):void 0,t=e&&Zn(e)&&Boolean(o.props.selected)?"".concat(Hr(e.getHours()),":").concat(Hr(e.getMinutes())):"";if(o.props.showTimeInput)return h.createElement(Gr,Wn({},n.defaultProps,o.props,{date:e,timeString:t,onChange:o.props.onTimeChange}))},o.renderAriaLiveRegion=function(){var e,t,r=Wr(o.state.date,null!==(e=o.props.yearItemNumber)&&void 0!==e?e:n.defaultProps.yearItemNumber),a=r.startPeriod,i=r.endPeriod;return t=o.props.showYearPicker?"".concat(a," - ").concat(i):o.props.showMonthYearPicker||o.props.showQuarterYearPicker?se(o.state.date):"".concat(gr(le(o.state.date),o.props.locale)," ").concat(se(o.state.date)),h.createElement("span",{role:"alert","aria-live":"polite",className:"react-datepicker__aria-live"},o.state.isRenderAriaLiveMessage&&t)},o.renderChildren=function(){if(o.props.children)return h.createElement("div",{className:"react-datepicker__children-container"},o.props.children)},o.containerRef=t.createRef(),o.state={date:o.getDateInView(),selectingDate:void 0,monthContainer:void 0,isRenderAriaLiveMessage:!1},o}return Hn(n,e),Object.defineProperty(n,"defaultProps",{get:function(){return{monthsShown:1,forceShowMonthNavigation:!1,timeCaption:"Time",previousYearButtonLabel:"Previous Year",nextYearButtonLabel:"Next Year",previousMonthButtonLabel:"Previous Month",nextMonthButtonLabel:"Next Month",yearItemNumber:12}},enumerable:!1,configurable:!0}),n.prototype.componentDidMount=function(){var e=this;this.props.showTimeSelect&&(this.assignMonthContainer=void e.setState({monthContainer:e.monthContainer}))},n.prototype.componentDidUpdate=function(e){var t=this;if(!this.props.preSelection||ur(this.props.preSelection,e.preSelection)&&this.props.monthSelectedIn===e.monthSelectedIn)this.props.openToDate&&!ur(this.props.openToDate,e.openToDate)&&this.setState({date:this.props.openToDate});else{var n=!cr(this.state.date,this.props.preSelection);this.setState({date:this.props.preSelection},(function(){return n&&t.handleCustomMonthChange(t.state.date)}))}},n.prototype.render=function(){var e=this.props.container||Kn;return h.createElement(Qn,{onClickOutside:this.handleClickOutside,style:{display:"contents"},containerRef:this.containerRef,ignoreClass:this.props.outsideClickIgnoreClass},h.createElement(e,{className:v("react-datepicker",this.props.className,{"react-datepicker--time-only":this.props.showTimeSelectOnly}),showTime:this.props.showTimeSelect||this.props.showTimeInput,showTimeSelectOnly:this.props.showTimeSelectOnly},this.renderAriaLiveRegion(),this.renderPreviousButton(),this.renderNextButton(),this.renderMonths(),this.renderYears(),this.renderTodayButton(),this.renderTimeSection(),this.renderInputTimeSection(),this.renderChildren()))},n}(t.Component),go=function(e){var t=e.icon,n=e.className,r=void 0===n?"":n,o=e.onClick,a="react-datepicker__calendar-icon";return"string"==typeof t?h.createElement("i",{className:"".concat(a," ").concat(t," ").concat(r),"aria-hidden":"true",onClick:o}):h.isValidElement(t)?h.cloneElement(t,{className:"".concat(t.props.className||""," ").concat(a," ").concat(r),onClick:function(e){"function"==typeof t.props.onClick&&t.props.onClick(e),"function"==typeof o&&o(e)}}):h.createElement("svg",{className:"".concat(a," ").concat(r),xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 448 512",onClick:o},h.createElement("path",{d:"M96 32V64H48C21.5 64 0 85.5 0 112v48H448V112c0-26.5-21.5-48-48-48H352V32c0-17.7-14.3-32-32-32s-32 14.3-32 32V64H160V32c0-17.7-14.3-32-32-32S96 14.3 96 32zM448 192H0V464c0 26.5 21.5 48 48 48H400c26.5 0 48-21.5 48-48V192z"}))},vo=function(e){function t(t){var n=e.call(this,t)||this;return n.portalRoot=null,n.el=document.createElement("div"),n}return Hn(t,e),t.prototype.componentDidMount=function(){this.portalRoot=(this.props.portalHost||document).getElementById(this.props.portalId),this.portalRoot||(this.portalRoot=document.createElement("div"),this.portalRoot.setAttribute("id",this.props.portalId),(this.props.portalHost||document.body).appendChild(this.portalRoot)),this.portalRoot.appendChild(this.el)},t.prototype.componentWillUnmount=function(){this.portalRoot&&this.portalRoot.removeChild(this.el)},t.prototype.render=function(){return y.createPortal(this.props.children,this.el)},t}(t.Component),yo=function(e){return(e instanceof HTMLAnchorElement||!e.disabled)&&-1!==e.tabIndex},xo=function(e){function n(n){var r=e.call(this,n)||this;return r.getTabChildren=function(){var e;return Array.prototype.slice.call(null===(e=r.tabLoopRef.current)||void 0===e?void 0:e.querySelectorAll("[tabindex], a, button, input, select, textarea"),1,-1).filter(yo)},r.handleFocusStart=function(){var e=r.getTabChildren();e&&e.length>1&&e[e.length-1].focus()},r.handleFocusEnd=function(){var e=r.getTabChildren();e&&e.length>1&&e[0].focus()},r.tabLoopRef=t.createRef(),r}return Hn(n,e),n.prototype.render=function(){var e;return(null!==(e=this.props.enableTabLoop)&&void 0!==e?e:n.defaultProps.enableTabLoop)?h.createElement("div",{className:"react-datepicker__tab-loop",ref:this.tabLoopRef},h.createElement("div",{className:"react-datepicker__tab-loop__start",tabIndex:0,onFocus:this.handleFocusStart}),this.props.children,h.createElement("div",{className:"react-datepicker__tab-loop__end",tabIndex:0,onFocus:this.handleFocusEnd})):this.props.children},n.defaultProps={enableTabLoop:!0},n}(t.Component);var wo,bo=function(e){function n(){return null!==e&&e.apply(this,arguments)||this}return Hn(n,e),Object.defineProperty(n,"defaultProps",{get:function(){return{hidePopper:!0}},enumerable:!1,configurable:!0}),n.prototype.render=function(){var e=this.props,r=e.className,o=e.wrapperClassName,a=e.hidePopper,i=void 0===a?n.defaultProps.hidePopper:a,s=e.popperComponent,l=e.targetComponent,c=e.enableTabLoop,d=e.popperOnKeyDown,u=e.portalId,p=e.portalHost,f=e.popperProps,m=e.showArrow,g=void 0;if(!i){var y=v("react-datepicker-popper",r);g=h.createElement(xo,{enableTabLoop:c},h.createElement("div",{ref:f.refs.setFloating,style:f.floatingStyles,className:y,"data-placement":f.placement,onKeyDown:d},s,m&&h.createElement(In,{ref:f.arrowRef,context:f.context,fill:"currentColor",strokeWidth:1,height:8,width:16,style:{transform:"translateY(-1px)"},className:"react-datepicker__triangle"})))}this.props.popperContainer&&(g=t.createElement(this.props.popperContainer,{},g)),u&&!i&&(g=h.createElement(vo,{portalId:u,portalHost:p},g));var x=v("react-datepicker-wrapper",o);return h.createElement(h.Fragment,null,h.createElement("div",{ref:f.refs.setReference,className:x},l),g)},n}(t.Component),Do=(wo=bo,function(e){var n,r,o,a="boolean"!=typeof e.hidePopper||e.hidePopper,i=t.useRef(null),s=On(Wn({open:!a,whileElementsMounted:gn,placement:e.popperPlacement,middleware:Vn([(r={padding:15},{...yn(r),options:[r,o]}),Mn(10),En({element:i})],null!==(n=e.popperModifiers)&&void 0!==n?n:[],!0)},e.popperProps)),l=Wn(Wn({},e),{hidePopper:a,popperProps:Wn(Wn({},s),{arrowRef:i})});return h.createElement(wo,Wn({},l))}),Co="react-datepicker-ignore-onclickoutside";var So="Date input not valid.",ko=function(e){function n(r){var o=e.call(this,r)||this;return o.calendar=null,o.input=null,o.getPreSelection=function(){return o.props.openToDate?o.props.openToDate:o.props.selectsEnd&&o.props.startDate?o.props.startDate:o.props.selectsStart&&o.props.endDate?o.props.endDate:qn()},o.modifyHolidays=function(){var e;return null===(e=o.props.holidays)||void 0===e?void 0:e.reduce((function(e,t){var n=new Date(t.date);return Zn(n)?Vn(Vn([],e,!0),[Wn(Wn({},t),{date:n})],!1):e}),[])},o.calcInitialState=function(){var e,t=o.getPreSelection(),n=Ar(o.props),r=Nr(o.props),a=n&&ee(t,tr(n))?n:r&&te(t,sr(r))?r:t;return{open:o.props.startOpen||!1,preventFocus:!1,inputValue:null,preSelection:null!==(e=o.props.selectsRange?o.props.startDate:o.props.selected)&&void 0!==e?e:a,highlightDates:Lr(o.props.highlightDates),focused:!1,shouldFocusDayInline:!1,isRenderAriaLiveMessage:!1,wasHidden:!1}},o.resetHiddenStatus=function(){o.setState(Wn(Wn({},o.state),{wasHidden:!1}))},o.setHiddenStatus=function(){o.setState(Wn(Wn({},o.state),{wasHidden:!0}))},o.setHiddenStateOnVisibilityHidden=function(){"hidden"===document.visibilityState&&o.setHiddenStatus()},o.clearPreventFocusTimeout=function(){o.preventFocusTimeout&&clearTimeout(o.preventFocusTimeout)},o.safeFocus=function(){setTimeout((function(){var e,t;null===(t=null===(e=o.input)||void 0===e?void 0:e.focus)||void 0===t||t.call(e,{preventScroll:!0})}),0)},o.safeBlur=function(){setTimeout((function(){var e,t;null===(t=null===(e=o.input)||void 0===e?void 0:e.blur)||void 0===t||t.call(e)}),0)},o.setFocus=function(){o.safeFocus()},o.setBlur=function(){o.safeBlur(),o.cancelFocusInput()},o.setOpen=function(e,t){void 0===t&&(t=!1),o.setState({open:e,preSelection:e&&o.state.open?o.state.preSelection:o.calcInitialState().preSelection,lastPreSelectChange:Mo},(function(){e||o.setState((function(e){return{focused:!!t&&e.focused}}),(function(){!t&&o.setBlur(),o.setState({inputValue:null})}))}))},o.inputOk=function(){return ne(o.state.preSelection)},o.isCalendarOpen=function(){return void 0===o.props.open?o.state.open&&!o.props.disabled&&!o.props.readOnly:o.props.open},o.handleFocus=function(e){var t,n,r=o.state.wasHidden,a=!r||o.state.open;r&&o.resetHiddenStatus(),!o.state.preventFocus&&a&&(null===(n=(t=o.props).onFocus)||void 0===n||n.call(t,e),o.props.preventOpenOnFocus||o.props.readOnly||o.setOpen(!0)),o.setState({focused:!0})},o.sendFocusBackToInput=function(){o.preventFocusTimeout&&o.clearPreventFocusTimeout(),o.setState({preventFocus:!0},(function(){o.preventFocusTimeout=setTimeout((function(){o.setFocus(),o.setState({preventFocus:!1})}))}))},o.cancelFocusInput=function(){clearTimeout(o.inputFocusTimeout),o.inputFocusTimeout=void 0},o.deferFocusInput=function(){o.cancelFocusInput(),o.inputFocusTimeout=setTimeout((function(){return o.setFocus()}),1)},o.handleDropdownFocus=function(){o.cancelFocusInput()},o.handleBlur=function(e){var t,n;(!o.state.open||o.props.withPortal||o.props.showTimeInput)&&(null===(n=(t=o.props).onBlur)||void 0===n||n.call(t,e)),o.setState({focused:!1})},o.handleCalendarClickOutside=function(e){var t,n;o.props.inline||o.setOpen(!1),null===(n=(t=o.props).onClickOutside)||void 0===n||n.call(t,e),o.props.withPortal&&e.preventDefault()},o.handleChange=function(){for(var e,t,r=[],a=0;a<arguments.length;a++)r[a]=arguments[a];var i=r[0];if(!o.props.onChangeRaw||(o.props.onChangeRaw.apply(o,r),i&&"function"==typeof i.isDefaultPrevented&&!i.isDefaultPrevented())){o.setState({inputValue:(null==i?void 0:i.target)instanceof HTMLInputElement?i.target.value:null,lastPreSelectChange:_o});var s=o.props,l=s.dateFormat,c=void 0===l?n.defaultProps.dateFormat:l,d=s.strictParsing,u=void 0===d?n.defaultProps.strictParsing:d,p=s.selectsRange,h=s.startDate,f=s.endDate,m=(null==i?void 0:i.target)instanceof HTMLInputElement?i.target.value:"";if(p){var g=m.split("-",2).map((function(e){return e.trim()})),v=g[0],y=g[1],x=Xn(null!=v?v:"",c,o.props.locale,u),w=Xn(null!=y?y:"",c,o.props.locale,u),b=(null==h?void 0:h.getTime())!==(null==x?void 0:x.getTime()),D=(null==f?void 0:f.getTime())!==(null==w?void 0:w.getTime());if(!b&&!D)return;if(x&&yr(x,o.props))return;if(w&&yr(w,o.props))return;null===(t=(e=o.props).onChange)||void 0===t||t.call(e,[x,w],i)}else{var C=Xn(m,c,o.props.locale,u,o.props.minDate);o.props.showTimeSelectOnly&&o.props.selected&&C&&!ur(C,o.props.selected)&&(C=re(o.props.selected,{hours:ie(C),minutes:ae(C),seconds:oe(C)})),!C&&m||o.setSelected(C,i,!0)}}},o.handleSelect=function(e,t,n){if(o.props.shouldCloseOnSelect&&!o.props.showTimeSelect&&o.sendFocusBackToInput(),o.props.onChangeRaw&&o.props.onChangeRaw(t),o.setSelected(e,t,!1,n),o.props.showDateSelect&&o.setState({isRenderAriaLiveMessage:!0}),!o.props.shouldCloseOnSelect||o.props.showTimeSelect)o.setPreSelection(e);else if(!o.props.inline){o.props.selectsRange||o.setOpen(!1);var r=o.props,a=r.startDate,i=r.endDate;!a||i||!o.props.swapRange&&zr(e,a)||o.setOpen(!1)}},o.setSelected=function(e,t,n,r){var a,i,s=e;if(o.props.showYearPicker){if(null!==s&&kr(se(s),o.props))return}else if(o.props.showMonthYearPicker){if(null!==s&&wr(s,o.props))return}else if(null!==s&&yr(s,o.props))return;var l=o.props,c=l.onChange,d=l.selectsRange,u=l.startDate,p=l.endDate,h=l.selectsMultiple,f=l.selectedDates,m=l.minTime,g=l.swapRange;if(!pr(o.props.selected,s)||o.props.allowSameDay||d||h)if(null!==s&&(!o.props.selected||n&&(o.props.showTimeSelect||o.props.showTimeSelectOnly||o.props.showTimeInput)||(s=er(s,{hour:ie(o.props.selected),minute:ae(o.props.selected),second:oe(o.props.selected)})),n||!o.props.showTimeSelect&&!o.props.showTimeSelectOnly||m&&(s=er(s,{hour:m.getHours(),minute:m.getMinutes(),second:m.getSeconds()})),o.props.inline||o.setState({preSelection:s}),o.props.focusSelectedMonth||o.setState({monthSelectedIn:r})),d){var v=u&&!p,y=u&&p;!u&&!p?null==c||c([s,null],t):v&&(null===s?null==c||c([null,null],t):zr(s,u)?g?null==c||c([s,u],t):null==c||c([s,null],t):null==c||c([u,s],t)),y&&(null==c||c([s,null],t))}else if(h){if(null!==s)if(null==f?void 0:f.length)if(f.some((function(e){return ur(e,s)}))){var x=f.filter((function(e){return!ur(e,s)}));null==c||c(x,t)}else null==c||c(Vn(Vn([],f,!0),[s],!1),t);else null==c||c([s],t)}else null==c||c(s,t);n||(null===(i=(a=o.props).onSelect)||void 0===i||i.call(a,s,t),o.setState({inputValue:null}))},o.setPreSelection=function(e){var t=ne(o.props.minDate),n=ne(o.props.maxDate),r=!0;if(e){var a=tr(e);if(t&&n)r=hr(e,o.props.minDate,o.props.maxDate);else if(t){var i=tr(o.props.minDate);r=te(e,i)||pr(a,i)}else if(n){var s=sr(o.props.maxDate);r=ee(e,s)||pr(a,s)}}r&&o.setState({preSelection:e})},o.toggleCalendar=function(){o.setOpen(!o.state.open)},o.handleTimeChange=function(e){var t,n;if(!o.props.selectsRange&&!o.props.selectsMultiple){var r=o.props.selected?o.props.selected:o.getPreSelection(),a=o.props.selected?e:er(r,{hour:ie(e),minute:ae(e)});o.setState({preSelection:a}),null===(n=(t=o.props).onChange)||void 0===n||n.call(t,a),o.props.shouldCloseOnSelect&&!o.props.showTimeInput&&(o.sendFocusBackToInput(),o.setOpen(!1)),o.props.showTimeInput&&o.setOpen(!0),(o.props.showTimeSelectOnly||o.props.showTimeSelect)&&o.setState({isRenderAriaLiveMessage:!0}),o.setState({inputValue:null})}},o.onInputClick=function(){var e,t;o.props.disabled||o.props.readOnly||o.setOpen(!0),null===(t=(e=o.props).onInputClick)||void 0===t||t.call(e)},o.onInputKeyDown=function(e){var t,n,r,a,i,s;null===(n=(t=o.props).onKeyDown)||void 0===n||n.call(t,e);var l=e.key;if(o.state.open||o.props.inline||o.props.preventOpenOnFocus){if(o.state.open){if(l===Bn.ArrowDown||l===Bn.ArrowUp){e.preventDefault();var c=o.props.showTimeSelectOnly?".react-datepicker__time-list-item[tabindex='0']":o.props.showWeekPicker&&o.props.showWeekNumbers?'.react-datepicker__week-number[tabindex="0"]':o.props.showFullMonthYearPicker||o.props.showMonthYearPicker?'.react-datepicker__month-text[tabindex="0"]':'.react-datepicker__day[tabindex="0"]',d=(null===(a=o.calendar)||void 0===a?void 0:a.containerRef.current)instanceof Element&&o.calendar.containerRef.current.querySelector(c);return void(d instanceof HTMLElement&&d.focus({preventScroll:!0}))}var u=qn(o.state.preSelection);l===Bn.Enter?(e.preventDefault(),e.target.blur(),o.inputOk()&&o.state.lastPreSelectChange===Mo?(o.handleSelect(u,e),!o.props.shouldCloseOnSelect&&o.setPreSelection(u)):o.setOpen(!1)):l===Bn.Escape?(e.preventDefault(),e.target.blur(),o.sendFocusBackToInput(),o.setOpen(!1)):l===Bn.Tab&&o.setOpen(!1),o.inputOk()||null===(s=(i=o.props).onInputError)||void 0===s||s.call(i,{code:1,msg:So})}}else l!==Bn.ArrowDown&&l!==Bn.ArrowUp&&l!==Bn.Enter||null===(r=o.onInputClick)||void 0===r||r.call(o)},o.onPortalKeyDown=function(e){e.key===Bn.Escape&&(e.preventDefault(),o.setState({preventFocus:!0},(function(){o.setOpen(!1),setTimeout((function(){o.setFocus(),o.setState({preventFocus:!1})}))})))},o.onDayKeyDown=function(e){var t,n,r,a,i,s,l=o.props,c=l.minDate,d=l.maxDate,u=l.disabledKeyboardNavigation,p=l.showWeekPicker,h=l.shouldCloseOnSelect,f=l.locale,m=l.calendarStartDay,g=l.adjustDateOnChange,v=l.inline;if(null===(n=(t=o.props).onKeyDown)||void 0===n||n.call(t,e),!u){var y=e.key,x=e.shiftKey,w=qn(o.state.preSelection),b=function(e,t){var n=t;switch(e){case Bn.ArrowRight:n=p?He(t,1):Re(t,1);break;case Bn.ArrowLeft:n=p?We(t):Ve(t);break;case Bn.ArrowUp:n=We(t);break;case Bn.ArrowDown:n=He(t,1);break;case Bn.PageUp:n=x?Ae(t,1):Te(t,1);break;case Bn.PageDown:n=x?Ne(t,1):Fe(t,1);break;case Bn.Home:n=nr(t,f,m);break;case Bn.End:n=function(e){return Je(e)}(t)}return n};if(y===Bn.Enter)return e.preventDefault(),o.handleSelect(w,e),void(!h&&o.setPreSelection(w));if(y===Bn.Escape)return e.preventDefault(),o.setOpen(!1),void(o.inputOk()||null===(a=(r=o.props).onInputError)||void 0===a||a.call(r,{code:1,msg:So}));var D=null;switch(y){case Bn.ArrowLeft:case Bn.ArrowRight:case Bn.ArrowUp:case Bn.ArrowDown:case Bn.PageUp:case Bn.PageDown:case Bn.Home:case Bn.End:D=function(e,t){for(var n=e,r=!1,a=0,i=b(e,t);!r;){if(a>=40){i=t;break}c&&i<c&&(n=Bn.ArrowRight,i=yr(c,o.props)?b(n,i):c),d&&i>d&&(n=Bn.ArrowLeft,i=yr(d,o.props)?b(n,i):d),yr(i,o.props)?(n!==Bn.PageUp&&n!==Bn.Home||(n=Bn.ArrowRight),n!==Bn.PageDown&&n!==Bn.End||(n=Bn.ArrowLeft),i=b(n,i)):r=!0,a++}return i}(y,w)}if(D){if(e.preventDefault(),o.setState({lastPreSelectChange:Mo}),g&&o.setSelected(D),o.setPreSelection(D),v){var C=le(w),S=le(D),k=se(w),_=se(D);C!==S||k!==_?o.setState({shouldFocusDayInline:!0}):o.setState({shouldFocusDayInline:!1})}}else null===(s=(i=o.props).onInputError)||void 0===s||s.call(i,{code:1,msg:So})}},o.onPopperKeyDown=function(e){e.key===Bn.Escape&&(e.preventDefault(),o.sendFocusBackToInput())},o.onClearClick=function(e){e&&e.preventDefault&&e.preventDefault(),o.sendFocusBackToInput();var t=o.props,n=t.selectsRange,r=t.onChange;n?null==r||r([null,null],e):null==r||r(null,e),o.setState({inputValue:null})},o.clear=function(){o.onClearClick()},o.onScroll=function(e){"boolean"==typeof o.props.closeOnScroll&&o.props.closeOnScroll?e.target!==document&&e.target!==document.documentElement&&e.target!==document.body||o.setOpen(!1):"function"==typeof o.props.closeOnScroll&&o.props.closeOnScroll(e)&&o.setOpen(!1)},o.renderCalendar=function(){var e,t;return o.props.inline||o.isCalendarOpen()?h.createElement(mo,Wn({showMonthYearDropdown:void 0,ref:function(e){o.calendar=e}},o.props,o.state,{setOpen:o.setOpen,dateFormat:null!==(e=o.props.dateFormatCalendar)&&void 0!==e?e:n.defaultProps.dateFormatCalendar,onSelect:o.handleSelect,onClickOutside:o.handleCalendarClickOutside,holidays:Or(o.modifyHolidays()),outsideClickIgnoreClass:Co,onDropdownFocus:o.handleDropdownFocus,onTimeChange:o.handleTimeChange,className:o.props.calendarClassName,container:o.props.calendarContainer,handleOnKeyDown:o.props.onKeyDown,handleOnDayKeyDown:o.onDayKeyDown,setPreSelection:o.setPreSelection,dropdownMode:null!==(t=o.props.dropdownMode)&&void 0!==t?t:n.defaultProps.dropdownMode}),o.props.children):null},o.renderAriaLiveRegion=function(){var e,t=o.props,r=t.dateFormat,a=void 0===r?n.defaultProps.dateFormat:r,i=t.locale,s=o.props.showTimeInput||o.props.showTimeSelect?"PPPPp":"PPPP";return e=o.props.selectsRange?"Selected start date: ".concat(Jn(o.props.startDate,{dateFormat:s,locale:i}),". ").concat(o.props.endDate?"End date: "+Jn(o.props.endDate,{dateFormat:s,locale:i}):""):o.props.showTimeSelectOnly?"Selected time: ".concat(Jn(o.props.selected,{dateFormat:a,locale:i})):o.props.showYearPicker?"Selected year: ".concat(Jn(o.props.selected,{dateFormat:"yyyy",locale:i})):o.props.showMonthYearPicker?"Selected month: ".concat(Jn(o.props.selected,{dateFormat:"MMMM yyyy",locale:i})):o.props.showQuarterYearPicker?"Selected quarter: ".concat(Jn(o.props.selected,{dateFormat:"yyyy, QQQ",locale:i})):"Selected date: ".concat(Jn(o.props.selected,{dateFormat:s,locale:i})),h.createElement("span",{role:"alert","aria-live":"polite",className:"react-datepicker__aria-live"},e)},o.renderDateInput=function(){var e,r,a,i=v(o.props.className,((e={})[Co]=o.state.open,e)),s=o.props.customInput||h.createElement("input",{type:"text"}),l=o.props.customInputRef||"ref",c=o.props,d=c.dateFormat,u=void 0===d?n.defaultProps.dateFormat:d,p=c.locale,f="string"==typeof o.props.value?o.props.value:"string"==typeof o.state.inputValue?o.state.inputValue:o.props.selectsRange?function(e,t,n){if(!e)return"";var r=Jn(e,n),o=t?Jn(t,n):"";return"".concat(r," - ").concat(o)}(o.props.startDate,o.props.endDate,{dateFormat:u,locale:p}):o.props.selectsMultiple?function(e,t){if(!(null==e?void 0:e.length))return"";var n=e[0]?Jn(e[0],t):"";if(1===e.length)return n;if(2===e.length&&e[1]){var r=Jn(e[1],t);return"".concat(n,", ").concat(r)}var o=e.length-1;return"".concat(n," (+").concat(o,")")}(null!==(a=o.props.selectedDates)&&void 0!==a?a:[],{dateFormat:u,locale:p}):Jn(o.props.selected,{dateFormat:u,locale:p});return t.cloneElement(s,((r={})[l]=function(e){o.input=e},r.value=f,r.onBlur=o.handleBlur,r.onChange=o.handleChange,r.onClick=o.onInputClick,r.onFocus=o.handleFocus,r.onKeyDown=o.onInputKeyDown,r.id=o.props.id,r.name=o.props.name,r.form=o.props.form,r.autoFocus=o.props.autoFocus,r.placeholder=o.props.placeholderText,r.disabled=o.props.disabled,r.autoComplete=o.props.autoComplete,r.className=v(s.props.className,i),r.title=o.props.title,r.readOnly=o.props.readOnly,r.required=o.props.required,r.tabIndex=o.props.tabIndex,r["aria-describedby"]=o.props.ariaDescribedBy,r["aria-invalid"]=o.props.ariaInvalid,r["aria-labelledby"]=o.props.ariaLabelledBy,r["aria-required"]=o.props.ariaRequired,r))},o.renderClearButton=function(){var e=o.props,t=e.isClearable,n=e.disabled,r=e.selected,a=e.startDate,i=e.endDate,s=e.clearButtonTitle,l=e.clearButtonClassName,c=void 0===l?"":l,d=e.ariaLabelClose,u=void 0===d?"Close":d,p=e.selectedDates;return t&&(null!=r||null!=a||null!=i||(null==p?void 0:p.length))?h.createElement("button",{type:"button",className:v("react-datepicker__close-icon",c,{"react-datepicker__close-icon--disabled":n}),disabled:n,"aria-label":u,onClick:o.onClearClick,title:s,tabIndex:-1}):null},o.state=o.calcInitialState(),o.preventFocusTimeout=void 0,o}return Hn(n,e),Object.defineProperty(n,"defaultProps",{get:function(){return{allowSameDay:!1,dateFormat:"MM/dd/yyyy",dateFormatCalendar:"LLLL yyyy",disabled:!1,disabledKeyboardNavigation:!1,dropdownMode:"scroll",preventOpenOnFocus:!1,monthsShown:1,readOnly:!1,withPortal:!1,selectsDisabledDaysInRange:!1,shouldCloseOnSelect:!0,showTimeSelect:!1,showTimeInput:!1,showPreviousMonths:!1,showMonthYearPicker:!1,showFullMonthYearPicker:!1,showTwoColumnMonthYearPicker:!1,showFourColumnMonthYearPicker:!1,showYearPicker:!1,showQuarterYearPicker:!1,showWeekPicker:!1,strictParsing:!1,swapRange:!1,timeIntervals:30,timeCaption:"Time",previousMonthAriaLabel:"Previous Month",previousMonthButtonLabel:"Previous Month",nextMonthAriaLabel:"Next Month",nextMonthButtonLabel:"Next Month",previousYearAriaLabel:"Previous Year",previousYearButtonLabel:"Previous Year",nextYearAriaLabel:"Next Year",nextYearButtonLabel:"Next Year",timeInputLabel:"Time",enableTabLoop:!0,yearItemNumber:12,focusSelectedMonth:!1,showPopperArrow:!0,excludeScrollbar:!0,customTimeInput:null,calendarStartDay:void 0,toggleCalendarOnIconClick:!1,usePointerEvent:!1}},enumerable:!1,configurable:!0}),n.prototype.componentDidMount=function(){window.addEventListener("scroll",this.onScroll,!0),document.addEventListener("visibilitychange",this.setHiddenStateOnVisibilityHidden)},n.prototype.componentDidUpdate=function(e,t){var n,r,o,a,i,s;e.inline&&(i=e.selected,s=this.props.selected,i&&s?le(i)!==le(s)||se(i)!==se(s):i!==s)&&this.setPreSelection(this.props.selected),void 0!==this.state.monthSelectedIn&&e.monthsShown!==this.props.monthsShown&&this.setState({monthSelectedIn:0}),e.highlightDates!==this.props.highlightDates&&this.setState({highlightDates:Lr(this.props.highlightDates)}),t.focused||pr(e.selected,this.props.selected)||this.setState({inputValue:null}),t.open!==this.state.open&&(!1===t.open&&!0===this.state.open&&(null===(r=(n=this.props).onCalendarOpen)||void 0===r||r.call(n)),!0===t.open&&!1===this.state.open&&(null===(a=(o=this.props).onCalendarClose)||void 0===a||a.call(o)))},n.prototype.componentWillUnmount=function(){this.clearPreventFocusTimeout(),window.removeEventListener("scroll",this.onScroll,!0),document.removeEventListener("visibilitychange",this.setHiddenStateOnVisibilityHidden)},n.prototype.renderInputContainer=function(){var e=this.props,t=e.showIcon,n=e.icon,r=e.calendarIconClassname,o=e.calendarIconClassName,a=e.toggleCalendarOnIconClick,i=this.state.open;return h.createElement("div",{className:"react-datepicker__input-container".concat(t?" react-datepicker__view-calendar-icon":"")},t&&h.createElement(go,Wn({icon:n,className:v(o,!o&&r,i&&"react-datepicker-ignore-onclickoutside")},a?{onClick:this.toggleCalendar}:null)),this.state.isRenderAriaLiveMessage&&this.renderAriaLiveRegion(),this.renderDateInput(),this.renderClearButton())},n.prototype.render=function(){var e=this.renderCalendar();if(this.props.inline)return e;if(this.props.withPortal){var t=this.state.open?h.createElement(xo,{enableTabLoop:this.props.enableTabLoop},h.createElement("div",{className:"react-datepicker__portal",tabIndex:-1,onKeyDown:this.onPortalKeyDown},e)):null;return this.state.open&&this.props.portalId&&(t=h.createElement(vo,Wn({portalId:this.props.portalId},this.props),t)),h.createElement("div",null,this.renderInputContainer(),t)}return h.createElement(Do,Wn({},this.props,{className:this.props.popperClassName,hidePopper:!this.isCalendarOpen(),targetComponent:this.renderInputContainer(),popperComponent:e,popperOnKeyDown:this.onPopperKeyDown,showArrow:this.props.showPopperArrow}))},n}(t.Component),_o="input",Mo="navigate";const Eo=[{name:"black",hex:"#000000"},{name:"white",hex:"#FFFFFF"},{name:"red",hex:"#FF0000"},{name:"green",hex:"#00FF00"},{name:"blue",hex:"#0000FF"},{name:"yellow",hex:"#FFFF00"},{name:"cyan",hex:"#00FFFF"},{name:"magenta",hex:"#FF00FF"},{name:"silver",hex:"#C0C0C0"},{name:"gray",hex:"#808080"},{name:"maroon",hex:"#800000"},{name:"olive",hex:"#808000"},{name:"purple",hex:"#800080"},{name:"teal",hex:"#008080"},{name:"navy",hex:"#000080"},{name:"orange",hex:"#FFA500"},{name:"brown",hex:"#A52A2A"},{name:"lime",hex:"#00FF00"},{name:"indigo",hex:"#4B0082"},{name:"violet",hex:"#EE82EE"},{name:"pink",hex:"#FFC0CB"},{name:"gold",hex:"#FFD700"},{name:"beige",hex:"#F5F5DC"},{name:"coral",hex:"#FF7F50"},{name:"crimson",hex:"#DC143C"},{name:"khaki",hex:"#F0E68C"},{name:"lavender",hex:"#E6E6FA"},{name:"salmon",hex:"#FA8072"},{name:"tan",hex:"#D2B48C"},{name:"turquoise",hex:"#40E0D0"},{name:"aquamarine",hex:"#7FFFD4"},{name:"azure",hex:"#F0FFFF"},{name:"chartreuse",hex:"#7FFF00"},{name:"chocolate",hex:"#D2691E"},{name:"plum",hex:"#DDA0DD"},{name:"orchid",hex:"#DA70D6"},{name:"rose",hex:"#FF007F"},{name:"mint",hex:"#98FF98"},{name:"peach",hex:"#FFDAB9"},{name:"apricot",hex:"#FBCEB1"},{name:"amber",hex:"#FFBF00"},{name:"mustard",hex:"#FFDB58"},{name:"sky",hex:"#87CEEB"},{name:"sea",hex:"#2E8B57"},{name:"grass",hex:"#7CFC00"},{name:"sand",hex:"#C2B280"},{name:"stone",hex:"#888888"},{name:"smoke",hex:"#737373"},{name:"ice",hex:"#AFEEEE"}];const Fo=({showFilterModal:e,setShowFilterModal:a,events:i,setFilteredEvents:s,filterItems:c,setFilters:d,vessels:p,filters:f,selectedTime:m,setSelectedTime:g,selectedType:v,setSelectedType:y,selectedArea:_,setSelectedArea:M,selectedVessel:E,setSelectedVessel:F,selectedCategory:T,setSelectedCategory:j,timeStart:P,setTimeStart:R,timeEnd:I,setTimeEnd:A,selectedSize:L,setSelectedSize:O,selectedColor:Y,setSelectedColor:V,selectedWeapon:z,setSelectedWeapon:K,selectedHostVessel:Q,setSelectedHostVessel:G,regionGroups:U})=>{const{devMode:q}=N();t.useEffect((()=>{if(e){if(g(f?.start_time||f?.end_time?"custom":""),y(f?.type||""),M(f?.country_flags||[]),f?.vessel_ids&&f.vessel_ids.length>0){const e=p.filter((e=>f.vessel_ids.includes(e.vessel_id))),t=p.filter((e=>!1!==e.is_active));if(t.every((e=>f.vessel_ids.includes(e.vessel_id)))&&t.length>0){F([{vessel_id:"all",name:"Select All",region_group_object:{name:"Select All"}},...e])}else F(e)}else F([]);j(f?.categories||[]),O(f?.sizes||[]),V(f?.colors||[]),K(f?.weapons||[]),G(!0===f?.host_vessel?"host":!1===f?.host_vessel?"non_host":"both"),R(f?.start_time?new Date(f.start_time):new Date),A(f?.end_time?new Date(f.end_time):new Date)}}),[e,f,p]);const[X,$]=t.useState({changedTime:!1,oldTime:"",changedType:!1,oldType:"",changedArea:!1,oldArea:[],changedVessel:!1,oldVessel:[],changedCategory:!1,oldCategory:[],changedHostVessel:!1,oldHostVessel:""}),J=(e,t)=>{"backdropClick"!==t&&(a(!1),X.changedTime&&g(X.oldTime),X.changedType&&y(X.oldType),X.changedArea&&M(X.oldArea),X.changedVessel&&F(X.oldVessel),X.changedCategory&&j(X.oldCategory),X.changedHostVessel&&G(X.oldHostVessel),$({changedTime:!1,oldTime:"",changedType:!1,oldType:"",changedArea:!1,oldArea:[],changedVessel:!1,oldVessel:[],changedCategory:!1,oldCategory:[],changedHostVessel:!1,oldHostVessel:""}))},ee=(e,t,n)=>{e(t),$((e=>{const t={...e};return"Time"===n&&(t.changedTime=!0,t.oldTime=m),"Type"===n&&(t.changedType=!0,t.oldType=v),"Category"===n&&(t.changedCategory=!0,t.oldCategory=T),"Vessel"===n&&(t.changedVessel=!0,t.oldVessel=E),"Flag State"===n&&(t.changedArea=!0,t.oldArea=_),"Host Vessel"===n&&(t.changedHostVessel=!0,t.oldHostVessel=Q),t}))},te=t.useMemo((()=>[{vessel_id:"all",name:"Select All",region_group_object:{name:"Select All"}},...p.filter((e=>e.region_group_id&&!1!==e.is_active)).map((e=>({...e,region_group_object:U.find((t=>t._id===e.region_group_id))}))).sort(((e,t)=>{const n=e.region_group_object?.name?.toLowerCase()||"",r=t.region_group_object?.name?.toLowerCase()||"";if(n<r)return-1;if(n>r)return 1;const o=e.name?.toLowerCase()||e.vessel_id,a=t.name?.toLowerCase()||t.vessel_id;return o.localeCompare(a)}))]),[p,U]),ne=(e,t,r,a,i=!0,s)=>n.jsx(b,{sx:{width:"100%",maxHeight:"150px",overflowY:"auto"},size:"small",children:n.jsx(D,{multiple:i,value:t,onChange:(e,t)=>ee(r,t,a),options:e,disableCloseOnSelect:!0,getOptionLabel:e=>s?s(e):e.replace(/_/g," ").replace(/\b\w/g,(e=>e.toUpperCase())),renderInput:e=>n.jsx(k,{...e,label:"Select "+a,variant:"outlined"}),sx:{"& .MuiFormLabel-root":{color:u("#FFFFFF",.6),fontWeight:400,maxHeight:"250px",overflowY:"auto"}},renderOption:(e,t,{selected:r})=>{const{key:a,...i}=e;return n.jsxs(h.Fragment,{children:[n.jsxs("li",{...i,children:[n.jsx(C,{checked:r}),n.jsx(S,{primary:s?s(t):t.replace(/_/g," ").replace(/\b\w/g,(e=>e.toUpperCase()))})]}),"custom"===t&&r&&n.jsxs(o,{container:!0,sx:{display:"flex",alignItems:"center",justifyContent:"space-around"},children:[n.jsxs(o,{container:!0,sx:{display:"flex",alignItems:"center",justifyContent:"space-around"},size:{xs:12,md:5},children:[n.jsx(o,{size:{xs:12,md:12},children:n.jsx(l,{children:"From"})}),n.jsx(o,{size:{xs:12,md:12},children:n.jsx(ko,{showIcon:!0,icon:n.jsx("svg",{width:"12",height:"12",viewBox:"0 0 12 12",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:n.jsx("path",{d:"M11.25 1.12236L8.24738 1.12237V0.375C8.24738 0.167812 8.07956 0 7.87238 0C7.66519 0 7.49738 0.167812 7.49738 0.375V1.12219H4.49738V0.375C4.49738 0.167812 4.32956 0 4.12238 0C3.91519 0 3.74738 0.167812 3.74738 0.375V1.12219H0.75C0.335812 1.12219 0 1.458 0 1.87219V11.2472C0 11.6614 0.335812 11.9972 0.75 11.9972H11.25C11.6642 11.9972 12 11.6614 12 11.2472V1.87219C12 1.45818 11.6642 1.12236 11.25 1.12236ZM11.25 11.2472H0.75V1.87219H3.74738V2.25C3.74738 2.45718 3.91519 2.625 4.12238 2.625C4.32956 2.625 4.49738 2.45718 4.49738 2.25V1.87237H7.49738V2.25019C7.49738 2.45737 7.66519 2.62519 7.87238 2.62519C8.07956 2.62519 8.24738 2.45737 8.24738 2.25019V1.87237H11.25V11.2472ZM8.625 5.99736H9.375C9.582 5.99736 9.75 5.82936 9.75 5.62236V4.87236C9.75 4.66536 9.582 4.49736 9.375 4.49736H8.625C8.418 4.49736 8.25 4.66536 8.25 4.87236V5.62236C8.25 5.82936 8.418 5.99736 8.625 5.99736ZM8.625 8.99718H9.375C9.582 8.99718 9.75 8.82936 9.75 8.62218V7.87218C9.75 7.66518 9.582 7.49718 9.375 7.49718H8.625C8.418 7.49718 8.25 7.66518 8.25 7.87218V8.62218C8.25 8.82955 8.418 8.99718 8.625 8.99718ZM6.375 7.49718H5.625C5.418 7.49718 5.25 7.66518 5.25 7.87218V8.62218C5.25 8.82936 5.418 8.99718 5.625 8.99718H6.375C6.582 8.99718 6.75 8.82936 6.75 8.62218V7.87218C6.75 7.66536 6.582 7.49718 6.375 7.49718ZM6.375 4.49736H5.625C5.418 4.49736 5.25 4.66536 5.25 4.87236V5.62236C5.25 5.82936 5.418 5.99736 5.625 5.99736H6.375C6.582 5.99736 6.75 5.82936 6.75 5.62236V4.87236C6.75 4.66518 6.582 4.49736 6.375 4.49736ZM3.375 4.49736H2.625C2.418 4.49736 2.25 4.66536 2.25 4.87236V5.62236C2.25 5.82936 2.418 5.99736 2.625 5.99736H3.375C3.582 5.99736 3.75 5.82936 3.75 5.62236V4.87236C3.75 4.66518 3.582 4.49736 3.375 4.49736ZM3.375 7.49718H2.625C2.418 7.49718 2.25 7.66518 2.25 7.87218V8.62218C2.25 8.82936 2.418 8.99718 2.625 8.99718H3.375C3.582 8.99718 3.75 8.82936 3.75 8.62218V7.87218C3.75 7.66536 3.582 7.49718 3.375 7.49718Z",fill:"white"})}),maxDate:W().valueOf(),selected:P,onChange:e=>{R(e)}})})]}),n.jsxs(o,{container:!0,sx:{display:"flex",alignItems:"center",justifyContent:"space-around"},size:{xs:12,md:5},children:[n.jsx(o,{size:{xs:12,md:12},children:n.jsx(l,{children:"To"})}),n.jsx(o,{size:{xs:12,md:12},children:n.jsx(ko,{showIcon:!0,icon:n.jsx("svg",{width:"12",height:"12",viewBox:"0 0 12 12",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:n.jsx("path",{d:"M11.25 1.12236L8.24738 1.12237V0.375C8.24738 0.167812 8.07956 0 7.87238 0C7.66519 0 7.49738 0.167812 7.49738 0.375V1.12219H4.49738V0.375C4.49738 0.167812 4.32956 0 4.12238 0C3.91519 0 3.74738 0.167812 3.74738 0.375V1.12219H0.75C0.335812 1.12219 0 1.458 0 1.87219V11.2472C0 11.6614 0.335812 11.9972 0.75 11.9972H11.25C11.6642 11.9972 12 11.6614 12 11.2472V1.87219C12 1.45818 11.6642 1.12236 11.25 1.12236ZM11.25 11.2472H0.75V1.87219H3.74738V2.25C3.74738 2.45718 3.91519 2.625 4.12238 2.625C4.32956 2.625 4.49738 2.45718 4.49738 2.25V1.87237H7.49738V2.25019C7.49738 2.45737 7.66519 2.62519 7.87238 2.62519C8.07956 2.62519 8.24738 2.45737 8.24738 2.25019V1.87237H11.25V11.2472ZM8.625 5.99736H9.375C9.582 5.99736 9.75 5.82936 9.75 5.62236V4.87236C9.75 4.66536 9.582 4.49736 9.375 4.49736H8.625C8.418 4.49736 8.25 4.66536 8.25 4.87236V5.62236C8.25 5.82936 8.418 5.99736 8.625 5.99736ZM8.625 8.99718H9.375C9.582 8.99718 9.75 8.82936 9.75 8.62218V7.87218C9.75 7.66518 9.582 7.49718 9.375 7.49718H8.625C8.418 7.49718 8.25 7.66518 8.25 7.87218V8.62218C8.25 8.82955 8.418 8.99718 8.625 8.99718ZM6.375 7.49718H5.625C5.418 7.49718 5.25 7.66518 5.25 7.87218V8.62218C5.25 8.82936 5.418 8.99718 5.625 8.99718H6.375C6.582 8.99718 6.75 8.82936 6.75 8.62218V7.87218C6.75 7.66536 6.582 7.49718 6.375 7.49718ZM6.375 4.49736H5.625C5.418 4.49736 5.25 4.66536 5.25 4.87236V5.62236C5.25 5.82936 5.418 5.99736 5.625 5.99736H6.375C6.582 5.99736 6.75 5.82936 6.75 5.62236V4.87236C6.75 4.66518 6.582 4.49736 6.375 4.49736ZM3.375 4.49736H2.625C2.418 4.49736 2.25 4.66536 2.25 4.87236V5.62236C2.25 5.82936 2.418 5.99736 2.625 5.99736H3.375C3.582 5.99736 3.75 5.82936 3.75 5.62236V4.87236C3.75 4.66518 3.582 4.49736 3.375 4.49736ZM3.375 7.49718H2.625C2.418 7.49718 2.25 7.66518 2.25 7.87218V8.62218C2.25 8.82936 2.418 8.99718 2.625 8.99718H3.375C3.582 8.99718 3.75 8.82936 3.75 8.62218V7.87218C3.75 7.66536 3.582 7.49718 3.375 7.49718Z",fill:"white"})}),maxDate:W().valueOf(),selected:I,onChange:e=>{A(e)}})})]})]})]},a)},isOptionEqualToValue:(e,t)=>e===t})});if(!p||!c||0===Object.keys(c).length)return null;const re=c.countryFlags.filter((e=>e.name)).map((e=>e.name)),oe=p.filter((e=>e)).filter((e=>!!q||e.is_active)),ae=(c.superCategories||[]).filter((e=>e)),ie=c.sizes||[],se=function(e="name"){return"hex"===e?Eo.map((e=>e.hex)):Eo.map((e=>e.name))}("name"),le=c.weapons||[];return n.jsx(r,{open:Boolean(e),onClose:J,children:n.jsxs(Z,{title:"Filter",onClose:J,showDivider:!0,children:[n.jsxs(o,{container:!0,direction:"row",gap:2,width:{xs:300,sm:500},sx:{maxHeight:"70vh",overflowY:"auto"},children:[ne(["last_1_hour","last_24_hours","last_7_days","last_1_month","custom"],m,g,"Time",!1),ne(["image","video"],v,y,"Type",!1),n.jsx(st,{loading:0===oe.length,options:te,value:E,multiple:!0,disableCloseOnSelect:!0,groupBy:e=>"all"===e.vessel_id?"Select All":e.region_group_object?.name||"Other",showOnlySingleAllSelectOption:!0,label:"Select Vessels",getOptionLabel:e=>e.name,isOptionEqualToValue:(e,t)=>e.vessel_id===t.vessel_id,renderTags:(e,t)=>e.map(((e,r)=>n.jsx(x,{label:e.name,size:"small",...t({index:r})},e.vessel_id))),onChange:(e,t)=>{const n=te.find((e=>"all"===e.vessel_id)),r=t.some((e=>"all"===e.vessel_id)),o=E.some((e=>"all"===e.vessel_id)),a=te.filter((e=>"all"!==e.vessel_id)),i=a.every((e=>t.some((t=>t.vessel_id===e.vessel_id))));if(r&&!o)ee(F,[n,...a],"Vessel");else if(!r&&o)ee(F,[],"Vessel");else if(r&&o){const e=t.filter((e=>"all"!==e.vessel_id));ee(F,e,"Vessel")}else if(!r&&i&&a.length>0)ee(F,[n,...t],"Vessel");else if(!r){const e=t.filter((e=>"all"!==e.vessel_id));ee(F,e,"Vessel")}},backgroundColor:"transparent",InputLabelStyle:{color:u("#FFFFFF",.6),fontWeight:400,fontSize:16,paddingTop:"8px"},sx:{width:"100%","& .MuiInputBase-root":{height:"58px"}}}),ne(re,_,M,"Flag State"),ne(ae,T,j,"Category"),ne(ie,L,O,"Size"),ne(se,Y,V,"Color"),ne(le,z,K,"Weapon"),ne(["non_host","host","both"],Q,G,"Host Vessel",!1,(e=>{switch(e){case"non_host":return"Non-host vessels only";case"host":return"Host vessels only";case"both":return"All Host & Non-host Vessels";default:return e}}))]}),n.jsxs(o,{container:!0,gap:2,justifyContent:"space-between",mt:2,children:[n.jsx(o,{children:n.jsx(w,{sx:{color:"#FFFFFF",textTransform:"none"},onClick:()=>{g(""),y(""),M([]),F([]),j([]),O([]),V([]),K([]),G("non_host"),s(i),$({changedTime:!1,oldTime:"",changedArea:!1,oldArea:[],changedVessel:!1,oldVessel:[],changedCategory:!1,oldCategory:[],changedHostVessel:!1,oldHostVessel:""}),d({host_vessel:!1})},children:"Clear filters"})}),n.jsx(o,{children:n.jsx(w,{sx:{color:"#FFFFFF",backgroundColor:H.palette.custom.mainBlue,"&:hover":{backgroundColor:H.palette.custom.mainBlue}},variant:"contained",onClick:()=>{const e={},t=W().valueOf();let n;switch(m){case"last_1_hour":n=t-36e5;break;case"last_24_hours":n=t-864e5;break;case"last_7_days":n=t-6048e5;break;case"last_1_month":n=t-2592e6;break;case"custom":n=W(P).valueOf()}if(v&&(e.type=v),_&&_.length>0&&(e.country_flags=_),E&&E.length>0){const t=E.some((e=>"all"===e.vessel_id));e.vessel_ids=t?p.filter((e=>!1!==e.is_active)).map((e=>e.vessel_id)):E.map((e=>e.vessel_id))}T&&T.length>0&&(e.categories=T),L&&L.length>0&&(e.sizes=L),Y&&Y.length>0&&(e.colors=Y),z&&z.length>0&&(e.weapons=z),"host"===Q?e.host_vessel=!0:"non_host"===Q&&(e.host_vessel=!1),n&&(e.start_time=W(n).valueOf(),e.end_time="custom"===m?W(I).valueOf():W(t).valueOf()),$({changedTime:!1,oldTime:"",changedType:!1,oldType:"",changedArea:!1,oldArea:[],changedVessel:!1,oldVessel:[],changedCategory:!1,oldCategory:[],changedHostVessel:!1,oldHostVessel:""}),d(e),a(!1),B("EventFilterApplied",{filters:e})},children:"Apply"})})]})]})})},To=t.memo((({listRef:e,containerHeight:t,getItemSize:r,handleScroll:o,Row:a,items:i,columnCount:s,rowData:l})=>n.jsx(lt,{ref:e,height:t,width:"100%",itemCount:Math.ceil(i.length/s),itemSize:r,overscanCount:3,onScroll:o,itemData:{items:i,columnCount:s,...l||{}},children:a})),((e,t)=>e.items.length===t.items.length&&e.containerHeight===t.containerHeight&&e.columnCount===t.columnCount));To.displayName="VirtualizedList";const jo=t.memo((({card:e,setShowDetailModal:r,setSelectedCard:a,buttonsToShow:i})=>{const[s,c]=t.useState(null),{user:d}=L(),[u,p]=t.useState(null),{vesselInfo:h}=J(),m=d?.hasPermissions([O.manageArtifacts]),g=t.useMemo((()=>Y(e.location?.coordinates,!!d?.use_MGRS)),[e.location?.coordinates,d?.use_MGRS]),v=h.find((t=>t.vessel_id===e.onboard_vessel_id)).name,y=Boolean(e.video_path),x=()=>{r(!0),a({...e,vesselName:v})};return t.useEffect((()=>{const t=e.thumbnail_url,n=e.image_url;e.video_url,y?(p(t||n||null),c(null)):(p(t||n||null),c(n||null))}),[e,y]),h?n.jsx(o,{container:!0,paddingTop:"0 !important",height:"100%",maxHeight:"350px",className:"events-step-2",onClick:x,sx:{cursor:"pointer"},children:n.jsxs(o,{container:!0,backgroundColor:"primary.main",borderRadius:2,padding:1,gap:1,children:[n.jsx(o,{size:12,height:"200px",children:n.jsx($,{thumbnailLink:u,originalLink:s,cardId:e._id,isImage:!y,style:{borderRadius:8},showVideoThumbnail:y,onThumbnailClick:x,showArchiveButton:m,isArchived:e?.portal?.is_archived||!1,vesselId:e?.onboard_vessel_id,buttonsToShow:i})}),n.jsxs(o,{container:!0,size:12,children:[n.jsxs(o,{display:"flex",justifyContent:"space-between",alignItems:"center",paddingX:1,size:12,children:[n.jsx(f,{title:v.length>12?v:"",children:n.jsx(l,{fontSize:"14px",fontWeight:500,children:v&&(v.length>12?v.slice(0,12)+"...":v)})}),n.jsx(l,{fontSize:"14px",fontWeight:500,children:W(e.timestamp).format(V.dateTimeFormat(d,{exclude_seconds:!0}))})]}),n.jsxs(o,{display:"flex",justifyContent:"space-between",alignItems:"center",paddingX:1,size:12,children:[n.jsx(l,{fontSize:"14px",fontWeight:500,color:H.palette.custom.mainBlue,children:"Location"}),n.jsx(l,{fontSize:"14px",fontWeight:500,color:H.palette.custom.mainBlue,children:"Category"})]}),n.jsxs(o,{display:"flex",justifyContent:"space-between",alignItems:"center",paddingX:1,size:12,children:[n.jsx(l,{fontSize:"14px",fontWeight:500,maxWidth:"50%",children:g}),n.jsx(l,{fontSize:"14px",fontWeight:500,maxWidth:"50%",textAlign:"right",children:e.super_category?e.super_category.length>12?e.super_category.slice(0,12)+"...":e.super_category:"Unspecified category"})]})]})]})}):n.jsx(l,{children:"No vessel info"})})),Po=t.memo((({card:e,setShowDetailModal:r,setSelectedCard:h,buttonsToShow:m})=>{const{user:g}=L(),{vesselInfo:v}=J(),[y,x]=t.useState(0),[w,b]=t.useState(!0),[D,C]=t.useState(!1),[S,k]=t.useState(null),[_,M]=t.useState(null),[E,F]=t.useState(new Set),T=t.useMemo((()=>e.isGroup?e.groupArtifacts[y]:e),[e,y]),j=e.isGroup?e.groupArtifacts.length:1,P=Boolean(T.video_path),R=g?.hasPermissions([O.manageArtifacts]),I=t.useMemo((()=>v.find((e=>e.vessel_id===T.onboard_vessel_id))),[v,T.onboard_vessel_id]),A=I?.name,N=t.useMemo((()=>Y(T.location?.coordinates,!!g?.use_MGRS)),[T.location?.coordinates,g?.use_MGRS]),B=t.useCallback((()=>{r(!0),h({...T,vesselName:A,isGroup:e.isGroup,groupArtifacts:e.groupArtifacts,currentGroupIndex:y})}),[r,h,T,A,e.isGroup,e.groupArtifacts,y]),z=t.useCallback((e=>{const t=y+e;t<0||t>=j||(C(!1),x(t))}),[y,j]),K=t.useCallback((e=>{e.stopPropagation(),z(-1)}),[z]),Q=t.useCallback((e=>{e.stopPropagation(),z(1)}),[z]),G=t.useCallback((e=>{if(!e||E.has(e._id))return;const t=e.thumbnail_url;if(t){const n=new Image;n.onload=()=>F((t=>new Set(t).add(e._id))),n.src=t}}),[E]);if(t.useEffect((()=>{e.isGroup&&(y>0&&G(e.groupArtifacts[y-1]),y<j-1&&G(e.groupArtifacts[y+1]))}),[y,e.isGroup,e.groupArtifacts,j,G]),t.useEffect((()=>{const e=T.thumbnail_url;if(!e)return b(!1),C(!1),k(null),void M(null);if(S===e&&_===e)return b(!1),void C(!1);C(!1);const t=new Image,n=()=>{T.thumbnail_url===e&&(M(e),k(e),b(!1))};return t.onload=n,t.onerror=()=>{T.thumbnail_url===e&&(b(!1),C(!0))},b(!0),t.src=e,t.complete||t.naturalWidth>0?n():(k(null),M(null)),()=>{t.onload=null,t.onerror=null}}),[T?._id,y]),!v?.length)return n.jsx(l,{children:"No vessel info"});const U=S&&!D,q=e.isGroup&&j>1;return n.jsx(o,{container:!0,paddingTop:"0 !important",height:"100%",maxHeight:"350px",className:"events-step-2",onClick:B,sx:{cursor:"pointer"},children:n.jsxs(o,{container:!0,backgroundColor:"primary.main",borderRadius:2,padding:1,gap:1,children:[n.jsxs(o,{size:12,height:"200px",position:"relative",children:[w&&!S&&n.jsxs(n.Fragment,{children:[n.jsx(a,{sx:{display:"flex",justifyContent:"center",alignItems:"center",position:"absolute",top:0,left:0,width:"100%",height:"100%",zIndex:1},children:n.jsx(i,{})}),n.jsx(s,{variant:"rectangular",width:"100%",height:"100%",sx:{borderRadius:2,position:"absolute",top:0,left:0,right:0,bottom:0}})]}),D&&!w&&n.jsx(a,{position:"absolute",top:0,left:0,right:0,bottom:0,display:"flex",alignItems:"center",justifyContent:"center",backgroundColor:"rgba(0,0,0,0.2)",borderRadius:2,zIndex:2,children:n.jsx(l,{color:"text.secondary",variant:"body2",children:"Failed to load image"})}),U&&n.jsx(a,{height:"100%",width:"100%",children:n.jsx($,{thumbnailLink:_,originalLink:S,cardId:T._id,isImage:!P,style:{borderRadius:8},showVideoThumbnail:P,onThumbnailClick:B,showArchiveButton:R,isArchived:T?.portal?.is_archived||!1,vesselId:T?.onboard_vessel_id,buttonsToShow:m,isGrouped:e.isGroup,groupArtifacts:e.groupArtifacts})}),q&&n.jsxs(a,{position:"absolute",bottom:8,left:"50%",sx:{transform:"translateX(-50%)",display:"flex",alignItems:"center",padding:"4px 8px",gap:1,zIndex:2},onClick:e=>e.stopPropagation(),children:[n.jsx(c,{size:"small",onClick:K,disabled:0===y,sx:{color:"white",padding:"4px",background:u(H.palette.custom.borderColor,.8)+" !important","&:disabled":{color:"rgba(255,255,255,0.3)"}},children:n.jsx(d,{fontSize:"small"})}),n.jsxs(l,{variant:"caption",sx:{color:"white",fontWeight:500,minWidth:"40px",textAlign:"center",padding:"5px 17px",borderRadius:"100px",background:u(H.palette.primary.main,.8)},children:[String(y+1).padStart(2,"0"),"/",String(j).padStart(2,"0")]}),n.jsx(c,{size:"small",onClick:Q,disabled:y===j-1,sx:{color:"white",padding:"4px",background:u(H.palette.custom.borderColor,.8)+" !important","&:disabled":{color:"rgba(255,255,255,0.3)"}},children:n.jsx(p,{fontSize:"small"})})]})]}),n.jsxs(o,{container:!0,size:12,children:[n.jsxs(o,{display:"flex",justifyContent:"space-between",alignItems:"center",paddingX:1,size:12,children:[n.jsx(f,{title:A?.length>12?A:"",children:n.jsx(l,{fontSize:"14px",fontWeight:500,children:A?.length>12?A.slice(0,12)+"...":A||"Unknown"})}),n.jsx(l,{fontSize:"14px",fontWeight:500,children:W(T.timestamp).format(V.dateTimeFormat(g,{exclude_seconds:!0}))})]}),n.jsxs(o,{display:"flex",justifyContent:"space-between",alignItems:"center",paddingX:1,size:12,children:[n.jsx(l,{fontSize:"14px",fontWeight:500,color:H.palette.custom.mainBlue,children:"Location"}),n.jsx(l,{fontSize:"14px",fontWeight:500,color:H.palette.custom.mainBlue,children:"Category"})]}),n.jsxs(o,{display:"flex",justifyContent:"space-between",alignItems:"center",paddingX:1,size:12,children:[n.jsx(l,{fontSize:"14px",fontWeight:500,maxWidth:"50%",children:N}),n.jsx(l,{fontSize:"14px",fontWeight:500,maxWidth:"50%",textAlign:"right",children:T.super_category?.length>12?T.super_category.slice(0,12)+"...":T.super_category||"Unspecified category"})]})]})]})})})),Ro=t.memo((({card:e,setShowDetailModal:r,setSelectedCard:a,buttonsToShow:i})=>{const{user:s}=L(),{vesselInfo:c}=J(),[d,u]=t.useState(null),[p,h]=t.useState(null),m=e,g=Boolean(m.video_path),v=s?.hasPermissions([O.manageArtifacts]),y=t.useMemo((()=>c.find((e=>e.vessel_id===m.onboard_vessel_id))),[c,m.onboard_vessel_id]),x=y?.name,w=t.useMemo((()=>Y(m.location?.coordinates,!!s?.use_MGRS)),[m.location?.coordinates,s?.use_MGRS]),b=t.useCallback((()=>{r(!0),a({...m,vesselName:x,isGroup:e.isGroup,groupArtifacts:e.groupArtifacts,currentGroupIndex:0})}),[r,a,m,x,e.isGroup,e.groupArtifacts]);return t.useEffect((()=>{const t=e.thumbnail_url,n=e.image_url,r=e.video_url;g?(h(t||n||null),u(r||null)):(h(t||n||null),u(n||null))}),[e,g]),c?n.jsx(o,{container:!0,paddingTop:"0 !important",height:"100%",maxHeight:"350px",className:"events-step-2",onClick:b,sx:{cursor:"pointer"},children:n.jsxs(o,{container:!0,backgroundColor:"primary.main",borderRadius:2,padding:1,gap:1,children:[n.jsx(o,{size:12,height:"200px",children:n.jsx($,{thumbnailLink:p,originalLink:d,cardId:m._id,isImage:!g,style:{borderRadius:8},showVideoThumbnail:g,onThumbnailClick:b,showArchiveButton:v,isArchived:m?.portal?.is_archived||!1,vesselId:m?.onboard_vessel_id,buttonsToShow:i,isGrouped:e.isGroup,groupArtifacts:e.groupArtifacts,isUnified:!0,unifiedArtifacts:e.duplications?[m,...e.duplications]:[m]})}),n.jsxs(o,{container:!0,size:12,children:[n.jsxs(o,{display:"flex",justifyContent:"space-between",alignItems:"center",paddingX:1,size:12,children:[n.jsx(f,{title:x?.length>12?x:"",children:n.jsx(l,{fontSize:"14px",fontWeight:500,children:x?.length>12?x.slice(0,12)+"...":x||"Unknown"})}),n.jsx(l,{fontSize:"14px",fontWeight:500,children:W(m.timestamp).format(V.dateTimeFormat(s,{exclude_seconds:!0}))})]}),n.jsxs(o,{display:"flex",justifyContent:"space-between",alignItems:"center",paddingX:1,size:12,children:[n.jsx(l,{fontSize:"14px",fontWeight:500,color:H.palette.custom.mainBlue,children:"Location"}),n.jsx(l,{fontSize:"14px",fontWeight:500,color:H.palette.custom.mainBlue,children:"Category"})]}),n.jsxs(o,{display:"flex",justifyContent:"space-between",alignItems:"center",paddingX:1,size:12,children:[n.jsx(l,{fontSize:"14px",fontWeight:500,maxWidth:"50%",children:w}),n.jsx(l,{fontSize:"14px",fontWeight:500,maxWidth:"50%",textAlign:"right",children:"Multiple"})]})]})]})}):n.jsx(l,{children:"No vessel info"})})),Io=({index:e,style:t,data:r})=>{const{items:a,columnCount:i,CustomCard:s,setShowDetailModal:l,setSelectedCard:c,onFlaggedByClick:d,buttonsToShow:u}=r,p=e*i;return n.jsx(o,{container:!0,style:t,spacing:2,paddingBottom:2,children:Array.from({length:i}).map(((e,t)=>{const r=p+t;if(r>=a.length)return null;const h=a[r],f=s||(h.duplications&&h.duplications.length>0?Ro:h.isGroup?Po:jo),m=12/i;return n.jsx(o,{size:{xs:12,sm:m,md:m,lg:m,xl:m},children:n.jsx(f,{card:h,setShowDetailModal:l,setSelectedCard:c,onFlaggedByClick:d,buttonsToShow:u})},h._id||h.artifact?._id)}))})};Io.displayName="VirtualizedCardListRow";const Ao=t.memo(Io),No=t.forwardRef((({events:e,setShowDetailModal:r,setSelectedCard:s,isLoading:c,onLoadMore:d,hasMore:u,containerRef:p,CustomCard:h,onFlaggedByClick:f,buttonsToShow:m},g)=>{const{screenSize:v}=N(),y=t.useRef(),[x,w]=t.useState(!1),[b,D]=t.useState(0),[C,S]=t.useState(0);t.useEffect((()=>{g&&(g.current={scrollToTop:()=>{y.current&&y.current.scrollTo(0)}})}),[g]),t.useEffect((()=>{const e=p?.current;if(!e)return;const t=()=>{const t=e.clientHeight,n=e.clientWidth;D(c?t-70:t),S(n)};t();const n=new ResizeObserver(t);return n.observe(e),()=>{n.unobserve(e)}}),[p,c]),t.useEffect((()=>{y.current&&y.current.resetAfterIndex(0)}),[e,v,C]),t.useEffect((()=>{if(!c&&d&&u&&y.current){const e=p?.current;if(e){if(0===e.getClientRects().length)return;const t=window.getComputedStyle(e);if(!("none"!==t.display&&"hidden"!==t.visibility))return}const{scrollHeight:t,clientHeight:n}=y.current._outerRef;t<=n&&u&&d()}}),[e,c,d,u,p]);const k=t.useCallback((()=>{const e=C||0;if(e<=0)return v.xs?1:v.sm?2:v.md?3:v.lg?4:5;const t=Math.max(1,Math.floor((e+16)/316));return Math.min(t,12)}),[C,v]),M=t.useCallback(((e,t,n)=>e+t>=.6*n),[]),E=t.useCallback((({scrollOffset:e,scrollUpdateWasRequested:t})=>{if(!c&&d&&!t&&y.current){const{scrollHeight:t,clientHeight:n}=y.current._outerRef;M(e,n,t)&&u&&!c&&!x&&(w(!0),d(),setTimeout((()=>w(!1)),1e3))}}),[u,c,d,x,M]);if(c&&0===e.length)return n.jsx(o,{display:"flex",justifyContent:"center",alignItems:"center",size:12,children:n.jsx(i,{})});if(0===e.length)return n.jsx(o,{display:"flex",justifyContent:"center",alignItems:"center",size:12,children:n.jsxs(o,{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",children:[n.jsx(_,{sx:{fontSize:"100px",color:H.palette.custom.borderColor}}),n.jsx(l,{variant:"h6",component:"div",gutterBottom:!0,color:H.palette.custom.borderColor,children:"No data available"})]})});const F=k();return n.jsxs(n.Fragment,{children:[n.jsx(To,{listRef:y,containerHeight:b,getItemSize:e=>{const t=k();Math.floor(e/t);return 350},handleScroll:E,Row:Ao,items:e,columnCount:F,rowData:{CustomCard:h,setShowDetailModal:r,setSelectedCard:s,onFlaggedByClick:f,buttonsToShow:m}}),c&&e.length>0&&n.jsx(a,{display:"flex",justifyContent:"center",width:"100%",padding:2,children:n.jsx(i,{})})]})}));No.displayName="VirtualizedCardList";const Lo=t.memo(No),Oo=t.memo((({showFilterModal:e,setShowFilterModal:r,filters:a,setFilters:s,vessels:l,tab:c,onCompletion:d,onLoadingChange:u})=>{const{id:p}=M(),[h,f]=t.useState([]),[m,g]=t.useState([]),[v,y]=t.useState(!0),x=t.useRef(!0),[w,b]=t.useState(1),[D,C]=t.useState({}),[S,k]=t.useState(!1),[_,E]=t.useState(null),F=t.useRef(),T=t.useRef(),j=t.useRef(),P=t.useRef([]),{regions:R}=ct(),[I,A]=t.useState(""),[N,L]=t.useState(""),[O,Y]=t.useState([]),[W,V]=t.useState([]),[G,U]=t.useState([]),[q,X]=t.useState(new Date),[Z,$]=t.useState(new Date),[J,ee]=t.useState([]),[te,ne]=t.useState([]),[re,oe]=t.useState([]),[ae,ie]=t.useState("non_host"),[se,le]=t.useState([]);t.useEffect((()=>{0===Object.keys(a).length&&s({host_vessel:!1})}),[]);const ce=async(e=!1,t=!1)=>{e||y(!0);try{const e=t?w+1:1,n={page:e,pageSize:100,filters:a,group:1};p&&(n.filters={...n.filters,id:p},n.pageSize=1);const r=await K.post("/artifacts",n),{artifacts:o,totalCount:i,groupedArtifacts:s}=r.data;if(0===o.length&&0===i)return f([]),b(1),void y(!1);P.current=[...P.current,...s];const c=Q(o,s).sort(((e,t)=>new Date(t.timestamp)-new Date(e.timestamp)));if(t?(f((e=>[...e,...c])),b(e)):(f(c),b(1)),x.current=o.length<(p?1:i),y(!1),p&&o.length>0){const e=o[0],t=l.find((t=>t.unit_id===e.unit_id));E({...e,vesselName:t?.name||t?.unit_id}),k(!0)}}catch(n){y(!1)}},de=t.useCallback((()=>{v||!x.current||p||(T.current&&clearTimeout(T.current),T.current=setTimeout((()=>{ce(!1,!0)}),500))}),[v,x.current,p,ce]);t.useEffect((()=>{(async()=>{R&&le(R)})()}),[R]),t.useEffect((()=>{(async()=>{try{const e=await K.get("/artifacts/filters").then((e=>e.data)).catch((e=>{}));0!==Object.keys(e).length&&C(e)}catch(e){}})(),sessionStorage.getItem("eventPath")&&sessionStorage.removeItem("eventPath")}),[]),t.useEffect((()=>{g(h)}),[h]),t.useEffect((()=>{ce()}),[a,p]),t.useEffect((()=>{"events"!==c&&(f((e=>e.slice(0,100))),b(1),y(!1),j.current&&j.current.scrollToTop())}),[c]),t.useEffect((()=>{const e=z(),t=e=>{const t=e?.artifact;t&&f((e=>{if(t?.portal?.is_archived)return e.filter((e=>e._id!==t._id));{const n=P.current.find((e=>e.includes(t._id))),r=e=>e.sort(((e,t)=>new Date(t.timestamp)-new Date(e.timestamp)));if(!n)return r([...e,{...t,isGroup:!1}]);const o=e.findIndex((e=>e.isGroup&&e.groupArtifacts?.some((e=>n.includes(e._id)))));if(-1!==o){const n=[...e];return n[o]={...n[o],groupArtifacts:[...n[o].groupArtifacts,t]},r(n)}const a=e.filter((e=>!e.isGroup&&n.includes(e._id)));if(a.length>0){const o=[...a,t],i=e.filter((e=>e.isGroup||!n.includes(e._id)));return i.push({...o[0],isGroup:!0,groupArtifacts:o}),r(i)}return r([...e,{...t,isGroup:!1}])}}))};return e.on("artifact/changed",t),()=>{e.off("artifact/changed",t),T.current&&clearTimeout(T.current)}}),[]);const ue=t.useRef();return t.useEffect((()=>{if(!p)return ue.current&&clearInterval(ue.current),ue.current=setInterval((()=>{ce(!0)}),3e5),()=>{ue.current&&clearInterval(ue.current)}}),[a,ce,p]),t.useEffect((()=>{"function"!=typeof d.current&&(d.current=e=>{y(e),f([])})}),[d]),t.useEffect((()=>{u(v)}),[v,u]),t.useEffect((()=>{S&&_&&_.artifact_id&&B("EventViewed",{artifactId:_.artifact_id})}),[S,_]),t.useEffect((()=>{S&&_&&_.artifact_id&&B("EventViewed",{artifactId:_.artifact_id})}),[S,_]),n.jsx(n.Fragment,{children:v&&0===h.length?n.jsx(o,{container:!0,display:"flex",justifyContent:"center",alignItems:"center",height:{xs:"90%",sm:"90%"},overflow:"auto",marginBottom:2,size:"grow",children:n.jsx(i,{sx:{color:H.palette.custom.mainBlue},size:60})}):n.jsxs(o,{container:!0,color:"#FFFFFF",flexDirection:"column",height:"100%",children:[n.jsx(o,{container:!0,overflow:"auto",display:"block",border:`1px solid ${H.palette.custom.borderColor}`,borderRadius:"10px",padding:"10px 24px",size:"grow",children:n.jsx(o,{container:!0,height:"100%",overflow:"hidden",ref:F,children:n.jsx(Lo,{ref:j,events:m,setShowDetailModal:k,setSelectedCard:E,isLoading:v,onLoadMore:de,hasMore:x.current&&!p,containerRef:F})})}),n.jsx(ut,{showDetailModal:S,setShowDetailModal:k,selectedCard:_,setSelectedCard:E,id:p}),n.jsx(Fo,{showFilterModal:e,vessels:l,setFilters:s,setShowFilterModal:r,events:h,setFilteredEvents:g,filterItems:D,filters:a,selectedTime:I,setSelectedTime:A,selectedType:N,setSelectedType:L,selectedArea:O,setSelectedArea:Y,selectedVessel:W,setSelectedVessel:V,selectedCategory:G,setSelectedCategory:U,timeStart:q,setTimeStart:X,timeEnd:Z,setTimeEnd:$,selectedSize:J,setSelectedSize:ee,selectedColor:te,setSelectedColor:ne,selectedWeapon:re,setSelectedWeapon:oe,selectedHostVessel:ae,setSelectedHostVessel:ie,regionGroups:se})]})})}));function Yo({showDetailModal:a,setShowDetailModal:i,selectedCard:s,setSelectedCard:c,id:d}){const{screenSize:u}=N(),[p,m]=t.useState(null),{user:g}=L(),v=e(),y=g?.hasPermissions([O.manageArtifacts]),x=s?.location?.coordinates&&Y(s.location.coordinates,!!g?.use_MGRS),w=()=>{c(null),i(!1)},b=[{label:"Location",value:x},{label:"Category",value:s?.super_category||"Unspecified category"},{label:"Sub Category",value:s?.category},{label:"Weapons",value:s?.weapons},{label:"Size",value:s?.size},{label:"Color",value:s?.color},{label:"Imo Number",value:s?.imo_number},{label:"Flag",value:s?.country_flag},{label:"Detected Country",value:s?.home_country},{label:"Orientation",value:s?.vessel_orientation},{label:"Bearing Angle",value:s?.true_bearing?`${Number(s.true_bearing).toFixed(2)}°`:void 0},{label:"Features",value:s?.vessel_features},{label:"Text Detected",value:Array.isArray(s?.text_extraction)&&s.text_extraction.length>0?s.text_extraction.map((e=>e.text)).slice(0,5).join(", "):null},{label:"Description",value:s?.others}],D=["Text Detected","Description","Features"];return t.useEffect((()=>{if(s){const e=s.thumbnail_url,t=s.image_url,n=s.video_url;s.video_path?m(n||t||null):m(t||e||null)}}),[s]),n.jsx(r,{open:Boolean(a),onClose:w,children:n.jsx(Z,{title:"Event Details",onClose:w,showDivider:!0,children:n.jsxs(o,{container:!0,gap:1,minWidth:{xs:300,sm:500},maxWidth:800,children:[n.jsx(o,{size:12,children:s&&n.jsx($,{thumbnailLink:p,originalLink:p,cardId:d||s._id,isImage:!s.video_path,style:{borderRadius:8,height:300,objectFit:"contain",backgroundColor:"#000"},skeletonStyle:{height:300,width:"100%"},showFullscreenIconForMap:!s.video_path,showArchiveButton:y})}),n.jsxs(o,{container:!0,sx:{maxHeight:"400px",overflow:"auto"},children:[n.jsxs(o,{display:"flex",justifyContent:u.xs?"flex-start":"space-between",alignItems:u.xs?"flex-start":"center",paddingX:1,flexDirection:u.xs?"column":"row",size:12,children:[u.xs&&n.jsx(l,{fontSize:{xs:"16px",sm:"20px"},fontWeight:500,color:H.palette.custom.mainBlue,children:"Name"}),n.jsx(l,{fontSize:{xs:"16px",sm:"20px"},fontWeight:500,children:s?.vesselName}),u.xs&&n.jsx(l,{fontSize:{xs:"16px",sm:"20px"},fontWeight:500,color:H.palette.custom.mainBlue,children:"Timestamp"}),n.jsx(l,{fontSize:{xs:"16px",sm:"20px"},fontWeight:500,children:W(s?.timestamp).format(V.dateTimeFormat(g,{exclude_seconds:!0}))})]}),b.map((({label:e,value:t},r)=>n.jsx(h.Fragment,{children:n.jsxs(o,{display:"flex",alignItems:{xs:"flex-start",sm:r%2==0||D.includes(e)?"flex-start":"flex-end"},paddingX:1,flexDirection:"column",size:{xs:12,sm:D.includes(e)?12:5.9},children:[n.jsxs(l,{fontSize:"16px",fontWeight:500,color:H.palette.custom.mainBlue,sx:{display:"flex",alignItems:"center",gap:"6px"},children:[e,"Bearing Angle"===e&&n.jsx(f,{title:"Angle measured clockwise between the True North and the Target as observed from own vessel",children:n.jsx("img",{src:"/icons/info_icon.svg"})})]}),n.jsx(l,{fontSize:"16px",fontWeight:500,onClick:()=>"Location"===e?void(s?.location?.coordinates&&(v(`/dashboard/map?artifact=${s._id}`),w())):null,sx:{cursor:"Location"===e?"pointer":"default",color:"Location"===e?"#007bff":"inherit",textDecoration:"Location"===e?"underline":"none",userSelect:"none","&:hover":"Location"===e?{color:"#0056b3",textDecoration:"underline"}:{}},title:"Location"===e?"Click to view on map":"",children:t??"--"})]})},r)))]})]})})})}const Ho=t.memo((({vessels:e})=>{const{id:r}=M(),[a,i]=t.useState([]),[s,l]=t.useState([]),[c,d]=t.useState(!0),[u,p]=t.useState(!1),[h,f]=t.useState(null),m=t.useRef(),{artifactsFavourites:g}=(()=>{const e=t.useContext(G);if(void 0===e)throw new Error("StoreContext must be used within a StoreProvider");return e})();return t.useEffect((()=>{const t=e.map((e=>e.unit_id));l(a.filter((e=>t.includes(e.unit_id))))}),[a,e]),t.useEffect((()=>{(async(e=!1)=>{e||d(!0);try{i(g),d(!1)}catch(t){d(!1)}})()}),[r,g]),t.useEffect((()=>{const e=z(),t=e=>{const t=e?.artifact;t&&i((e=>{if(t?.portal?.is_archived)return e.filter((e=>e._id!==t._id));return[...e,t].sort(((e,t)=>new Date(t.timestamp)-new Date(e.timestamp)))}))};return e.on("artifact/changed",t),()=>{e.off("artifact/changed",t)}}),[]),n.jsxs(o,{container:!0,color:"#FFFFFF",flexDirection:"column",height:"100%",children:[n.jsx(o,{container:!0,overflow:"auto",display:"block",border:`1px solid ${H.palette.custom.borderColor}`,borderRadius:"10px",padding:"10px 24px",size:"grow",children:n.jsx(o,{container:!0,height:"100%",overflow:"auto",ref:m,children:n.jsx(Lo,{events:s,setShowDetailModal:p,setSelectedCard:f,isLoading:c,containerRef:m})})}),n.jsx(Yo,{showDetailModal:u,setShowDetailModal:p,selectedCard:h,setSelectedCard:f,id:r})]})})),Wo=t.memo((({vessels:e})=>{const[r,a]=t.useState([]),[i,s]=t.useState([]),[l,c]=t.useState(!0),d=t.useRef(!0),[u,p]=t.useState(1),[h,f]=t.useState(!1),[m,g]=t.useState(null),v=t.useRef(),y=t.useRef(),x=t.useRef(),w=async(e=!1,t=!1)=>{e||c(!0);try{const e=t?u+1:1,n={page:e,pageSize:100},r=await K.get("/artifacts/archived",{params:n}),{artifacts:o,totalCount:i}=r.data;t?(a((e=>[...e,...o])),p(e)):(a(o),p(1)),d.current=o.length<i,c(!1)}catch(n){c(!1)}},b=t.useCallback((()=>{!l&&d.current&&(y.current&&clearTimeout(y.current),y.current=setTimeout((()=>{w(!1,!0)}),500))}),[l,d.current,w]);return t.useEffect((()=>{const t=e.map((e=>e.unit_id));s(r.filter((e=>t.includes(e.unit_id))))}),[r,e]),t.useEffect((()=>{w()}),[]),t.useEffect((()=>{const e=z(),t=e=>{const t=e?.artifact;t&&a((e=>{if(!t?.portal?.is_archived)return e.filter((e=>e._id!==t._id));if(!e.find((e=>e._id===t._id)))return[t,...e].sort(((e,t)=>new Date(t.portal.archived_at)-new Date(e.portal.archived_at)));return e}))};return e.on("artifact/changed",t),()=>{e.off("artifact/changed",t),y.current&&clearTimeout(y.current)}}),[]),n.jsxs(o,{container:!0,color:"#FFFFFF",flexDirection:"column",height:"100%",children:[n.jsx(o,{container:!0,overflow:"auto",display:"block",border:`1px solid ${H.palette.custom.borderColor}`,borderRadius:"10px",padding:"10px 24px",size:"grow",children:n.jsx(o,{container:!0,height:"100%",overflow:"hidden",ref:v,children:n.jsx(Lo,{ref:x,events:i,setShowDetailModal:f,setSelectedCard:g,isLoading:l,onLoadMore:b,hasMore:d.current,containerRef:v,buttonsToShow:[U.ARCHIVE]})})}),n.jsx(Yo,{showDetailModal:h,setShowDetailModal:f,selectedCard:m,setSelectedCard:g})]})})),Vo=({open:e,onClose:t,onConfirm:a})=>n.jsx(r,{open:e,onClose:t,children:n.jsx(Z,{title:"Remove from Flagged",onClose:t,headerPosition:"center",children:n.jsxs(o,{container:!0,flexDirection:"column",gap:2,width:{xs:"auto",sm:400},children:[n.jsx(o,{display:"flex",justifyContent:"center",children:n.jsx(l,{fontWeight:"100",textAlign:"center",children:"Do you really want to remove this artifact from flagged artifacts."})}),n.jsxs(o,{container:!0,gap:1,justifyContent:"center",children:[n.jsx(o,{justifyContent:"center",display:"flex",children:n.jsx(w,{variant:"contained",onClick:t,backgroundColor:"#FFFFFF",sx:{background:"#FFFFFF !important",color:H.palette.primary.main},children:"Cancel"})}),n.jsx(o,{justifyContent:"center",display:"flex",children:n.jsx(w,{variant:"contained",onClick:a,children:"Confirm"})})]})]})})}),Bo=t.memo((({card:e,onFlaggedByClick:r})=>{const[a,i]=t.useState(null),{user:s}=L(),[c,d]=t.useState(null),{vesselInfo:u}=J(),[p,h]=t.useState(!1),[m,g]=t.useState(!1),v=t.useMemo((()=>Y(e.artifact?.location?.coordinates,!!s?.use_MGRS)),[e.artifact?.location?.coordinates,s?.use_MGRS]),y=u.find((t=>t.vessel_id===e.artifact?.onboard_vessel_id)),x=y?.name||"Unknown Vessel",w=Boolean(e.artifact?.video_path);t.useEffect((()=>{if(e.artifact){const t=e.artifact.thumbnail_url,n=e.artifact.image_url,r=e.artifact.video_url;w?(d(t||n||null),i(r||null)):(d(t||n||null),i(n||null))}}),[e.artifact,w]);return e.artifact&&u?n.jsxs(o,{container:!0,paddingTop:"0 !important",height:"100%",maxHeight:"350px",sx:{cursor:"pointer"},onClick:()=>r(e.artifact),children:[n.jsxs(o,{container:!0,backgroundColor:"primary.main",borderRadius:2,padding:1,gap:1,children:[n.jsx(o,{size:12,maxHeight:"200px",children:n.jsx($,{thumbnailLink:c,originalLink:a,cardId:e.artifact._id,isImage:!w,style:{borderRadius:8},showVideoThumbnail:w,showArchiveButton:!e.artifact?.portal?.is_archived,isArchived:e.artifact?.portal?.is_archived,vesselId:e.artifact.onboard_vessel_id,buttonsToShow:[U.ARCHIVE],handleUnflagClick:e=>{e.stopPropagation(),h(!0)}})}),n.jsxs(o,{container:!0,size:12,children:[n.jsxs(o,{display:"flex",justifyContent:"space-between",alignItems:"center",paddingX:1,size:12,children:[n.jsx(f,{title:x.length>12?x:"",children:n.jsx(l,{fontSize:"14px",fontWeight:500,children:x.length>12?x.slice(0,12)+"...":x})}),n.jsx(l,{fontSize:"14px",fontWeight:500,children:W(e.artifact.timestamp).format(V.dateTimeFormat(s,{exclude_seconds:!0}))})]}),n.jsxs(o,{display:"flex",justifyContent:"space-between",alignItems:"center",paddingX:1,size:12,children:[n.jsx(l,{fontSize:"14px",fontWeight:500,color:H.palette.custom.mainBlue,children:"Location"}),n.jsx(l,{fontSize:"14px",fontWeight:500,color:H.palette.custom.mainBlue,children:"Category"})]}),n.jsxs(o,{display:"flex",justifyContent:"space-between",alignItems:"center",paddingX:1,size:12,children:[n.jsx(l,{fontSize:"14px",fontWeight:500,maxWidth:"50%",children:v}),n.jsx(l,{fontSize:"14px",fontWeight:500,maxWidth:"50%",textAlign:"right",children:e.artifact.super_category?e.artifact.super_category.length>12?e.artifact.super_category.slice(0,12)+"...":e.artifact.super_category:"Unspecified category"})]}),n.jsx(o,{display:"flex",justifyContent:"space-between",alignItems:"center",paddingX:1,size:12,children:n.jsxs(l,{fontSize:"14px",fontWeight:500,maxWidth:"50%",color:"#FDBF2D",fontFamily:'"Nunito Sans", sans-serif',sx:{fontStyle:"italic",textDecoration:"underline"},children:["Flagged by ",e.flagCount||0," ",1===e.flagCount?"user":"users"]})})]})]}),n.jsx(Vo,{open:p,onClose:e=>{e.stopPropagation(),h(!1)},onConfirm:async t=>{t.stopPropagation(),g(!0);try{await dt.removeAllFlagsFromArtifact(e.artifact._id),h(!1)}catch(n){}finally{g(!1)}},isLoading:m})]}):null})),zo=()=>{const[e,r]=t.useState([]),[a,i]=t.useState(!0),[s,l]=t.useState(null),[c,d]=t.useState(!1),u=t.useRef(),p=t.useRef();t.useEffect((()=>{const e=async()=>{try{i(!0);const e=await dt.getFlaggedArtifacts();r(e)}catch(e){}finally{i(!1)}};e();const t=z();return t.on("artifacts_flagged/changed",e),t.on("artifact/changed",e),()=>{t.off("artifacts_flagged/changed",e),t.off("artifact/changed",e)}}),[]);const h=t.useCallback((e=>{l(e),d(!0)}));return n.jsxs(o,{container:!0,color:"#FFFFFF",flexDirection:"column",height:"100%",children:[n.jsx(o,{container:!0,overflow:"auto",display:"block",border:`1px solid ${H.palette.custom.borderColor}`,borderRadius:"10px",padding:"10px 24px",size:"grow",children:n.jsx(o,{container:!0,height:"100%",overflow:"auto",ref:u,children:n.jsx(Lo,{ref:p,events:e,isLoading:a,containerRef:u,setShowDetailModal:d,setSelectedCard:l,CustomCard:Bo,onFlaggedByClick:h})})}),n.jsx(Yo,{showDetailModal:c,setShowDetailModal:d,selectedCard:s,setSelectedCard:l})]})},Ko=({onSelect:e,isLoading:r})=>{const[o,a]=t.useState(""),[s,l]=t.useState([]),[c,d]=t.useState(!1),[u,p]=t.useState(!1),[h,f]=t.useState(-1),[m,g]=t.useState(!1),[v,y]=t.useState(""),x=t.useRef(),w=t.useRef(),b=t.useRef([]),D=t.useRef(),C=t.useRef(),S=t.useRef(!1);t.useEffect((()=>{if(y(o),d(!1),!S.current)return o?(d(!0),x.current&&clearTimeout(x.current),x.current=setTimeout((async()=>{try{C.current&&C.current.abort(),C.current=new AbortController;const e=await K.post("/suggestions",{query:o},{signal:C.current.signal,meta:{showSnackbar:!1}});C.current=null,l(e.data?.suggestions||[]),p(!0),f(-1)}catch(e){if("CanceledError"===e.name)return;l([]),p(!1),f(-1)}finally{d(!1)}}),600),()=>x.current&&clearTimeout(x.current)):(l([]),p(!1),void f(-1));S.current=!1}),[o]),t.useEffect((()=>{h>=0&&b.current[h]&&b.current[h].scrollIntoView({block:"nearest"})}),[h]);const _=(t,n=!1)=>{S.current=!0,a(t),y(t),p(!1),f(-1),e&&(n?e(t,v):e(t))};return n.jsx(E,{onClickAway:()=>p(!1),children:n.jsxs("div",{ref:D,style:{position:"relative",width:"100%"},children:[n.jsx(k,{fullWidth:!0,placeholder:"Search (Powered by AI)",value:o,onChange:e=>a(e.target.value),autoComplete:"off",inputRef:w,InputProps:{endAdornment:c?n.jsx(i,{size:20,sx:{color:"#fff"}}):null,startAdornment:n.jsx(F,{position:"start",children:n.jsx(T,{sx:{color:"#fff"}})})},sx:{borderRadius:2,input:{color:"#fff",textTransform:"none"},label:{color:"#878787"},"& input.Mui-disabled":{"-webkit-text-fill-color":"unset",color:"#878787"},"& .MuiOutlinedInput-root":{"& fieldset":{borderColor:"#23272F"},"&:hover fieldset":{borderColor:"#1976d2"},"&.Mui-focused fieldset":{borderColor:"#1976d2"}}},onFocus:()=>{s.length>0&&p(!0)},onKeyDown:t=>{if(!c&&!r)if("ArrowDown"===t.key)t.preventDefault(),g(!0),!u&&s.length>0&&p(!0),f((e=>Math.min(e+1,s.length-1)));else if("ArrowUp"===t.key)t.preventDefault(),g(!0),f((e=>Math.max(e-1,0)));else if("Enter"===t.key){if(t.preventDefault(),""===o.trim())return void(e&&e(""));h>=0&&s[h]?m?_(s[h]):e&&e(s[h],v):s.length>0&&o.trim().toLowerCase()!==s[0].trim().toLowerCase()?_(s[0],!0):_(v)}},disabled:r}),u&&!r&&s.length>0&&n.jsx(j,{elevation:4,style:{position:"absolute",width:"100%",zIndex:10,maxHeight:400,overflowY:"auto",background:H.palette.primary.main,borderRadius:10,marginTop:4,padding:0},children:n.jsx("ul",{style:{listStyle:"none",margin:0,padding:0,paddingTop:6},children:s.map(((e,t)=>n.jsxs("li",{ref:e=>b.current[t]=e,tabIndex:0,onClick:()=>_(e),onMouseDown:e=>e.preventDefault(),onMouseEnter:()=>{g(!0),f(t)},onMouseLeave:()=>{g(!0),f(-1)},style:{color:"#fff",cursor:"pointer",background:h===t?"#0B1222":"inherit",marginBottom:6,padding:"10px 16px",display:"flex",alignItems:"center",gap:12,outline:"none",textTransform:"none"},onKeyDown:t=>{"Enter"!==t.key&&" "!==t.key||_(e)},children:[n.jsx(T,{sx:{color:"#fff",mr:1}}),n.jsx("span",{style:{textTransform:"none"},children:e})]},t)))})})]})})},Qo=t.memo((()=>{const{user:e}=L(),{vesselInfo:r,fetchVesselsInfo:a}=J(),[i,s]=t.useState(""),[c,d]=t.useState({host_vessel:!1}),[u,p]=t.useState(!1),h=P(),[f,m]=t.useState(!1),[g,v]=t.useState([]),[y,x]=t.useState(null),[b,D]=t.useState(""),[C,S]=t.useState(!1),{isMobile:k,devMode:_}=N(),M=t.useRef(null),E=t.useRef();t.useEffect((()=>{("/dashboard/events"===h.pathname||h.pathname.startsWith("/dashboard/events"))&&p(!0)}),[h]);const F=t.useMemo((()=>[{value:"events",label:"Events",component:g.length>0&&n.jsx(Oo,{showFilterModal:f,setShowFilterModal:m,filters:c,setFilters:d,vessels:g,tab:i,onCompletion:M,onLoadingChange:S}),display:!0},{value:"favourites",label:"Favorites",component:g.length>0&&n.jsx(Ho,{vessels:g}),display:!0},{value:"archived",label:"Archived",component:n.jsx(Wo,{vessels:g}),display:e?.hasPermissions([O.manageArtifacts])},{value:"flagged",label:"Flagged",component:n.jsx(zo,{vessels:g}),display:e?.hasPermissions([O.manageArtifacts])}]),[e,f,c,g,i]),T=async()=>{try{if(r){const t=(e=r)&&Array.isArray(e)?e.filter((e=>!(!1===e.is_active&&!_))):[];v(t)}else a()}catch(t){}var e};t.useEffect((()=>{T()}),[r]),t.useEffect((()=>{i||s(F.find((e=>e.display))?.value||"")}),[F]);if(!u)return null;const j=Object.entries(c).filter((([e,t])=>!(!t||Array.isArray(t)&&0===t.length||!Array.isArray(t)&&""===t)&&(("type"!==e||"both"!==t)&&("end_time"!==e||!t)))).length;return e&&F.some((e=>e.display))&&i&&n.jsxs(o,{container:!0,color:"#FFFFFF",flexDirection:"column",width:"100%",height:"100%",overflow:"auto",sx:{backgroundColor:H.palette.custom.darkBlue},children:[n.jsxs(o,{container:!0,padding:2,display:"flex",columnGap:{xs:2,lg:0},rowGap:2,justifyContent:"space-between",alignItems:"center",flexWrap:"wrap",children:[n.jsx(o,{size:{xs:"grow",lg:4.5},children:n.jsx(R,{value:i,onChange:(e,t)=>{s(t)},sx:{width:"100%",padding:"4px",border:`2px solid ${H.palette.custom.borderColor}`,borderRadius:"8px",backgroundColor:"transparent","& .MuiTabs-flexContainer":{height:"100%"},"& .MuiButtonBase-root":{width:100/F.filter((e=>e.display)).length+"%",borderRadius:"8px"},"& .MuiButtonBase-root.Mui-selected":{backgroundColor:H.palette.custom.mainBlue}},children:F.filter((e=>e.display)).map((e=>n.jsx(I,{label:e.label,value:e.value,sx:{maxWidth:"none"}},e.value)))})}),"events"===i&&n.jsxs(o,{container:!0,columnGap:2,justifyContent:"space-between",size:{xs:12,lg:7.4},children:[n.jsx(o,{size:{xs:"grow",lg:5.8},children:!q(X.stagingAndProduction)&&n.jsx(Ko,{onSelect:async(e,t)=>{if(""===e)return d({host_vessel:!1}),D(""),void x(null);t&&e&&e!==t?(D(t),x(e)):(D(""),x(null));try{M.current?.(!0),E.current&&E.current.abort(),E.current=new AbortController;const t=await K.post("/completions",{text:e},{signal:E.current.signal,meta:{showSnackbar:!1}});E.current=null,t.data&&"object"==typeof t.data&&d(t.data)}catch(n){M.current?.(!1)}},isLoading:C})}),n.jsx(o,{alignItems:"center",display:"flex",justifyContent:"flex-end",gap:2,size:"auto",children:n.jsx(A,{badgeContent:j,color:"primary",invisible:0===j,sx:{height:k?"100%":"auto","& .MuiBadge-badge":{backgroundColor:H.palette.custom.mainBlue,color:"#FFFFFF",borderRadius:"50%",height:20,width:20,border:"2px solid #FFFFFF",top:3,right:3}},children:n.jsx(w,{className:"events-step-1",variant:"outlined",startIcon:n.jsx("img",{src:"/icons/filter_icon.svg",width:20,height:20,alt:"Filter"}),sx:{"&.MuiButtonBase-root":{borderColor:0===j?H.palette.custom.borderColor:H.palette.custom.mainBlue,height:{xs:q(X.stagingAndProduction)?"50px":"100%",lg:"auto"},color:"#FFFFFF",padding:{xs:"0",lg:"10px 20px"},fontWeight:"bold"},"& .MuiButton-icon":{marginRight:{xs:0,lg:"10px"}}},onClick:()=>m(!0),disabled:C,children:!k&&"Filter"})})})]})]}),"events"===i&&y&&b&&n.jsx(o,{item:!0,xs:12,padding:2,pt:0,children:n.jsxs(l,{sx:{fontWeight:"bold",fontSize:16,pl:1},children:[n.jsx("span",{style:{color:H.palette.custom.mainBlue},children:"Showing results for"}),' "',y,'"'," ",n.jsx("span",{style:{textDecoration:"line-through",color:"#888",marginLeft:8},children:b})]})}),F.filter((e=>e.display)).map((e=>n.jsx(o,{display:i!==e.value&&"none",paddingX:2,paddingBottom:2,width:"100%",size:"grow",children:e.component},e.value)))]})}));export{Qo as default};

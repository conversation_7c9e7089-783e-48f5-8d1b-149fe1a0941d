import{r as e,j as t,K as i,T as s,x as n,a0 as o,am as a,Y as l,aD as r,cx as d,aL as c,bF as u,G as h,W as m,ac as p,ba as x,aI as g,L as f,cy as j,as as b,az as v,cz as F,bB as _,cA as y,cB as C,cC as w,cD as S,bI as z,bX as k,bQ as M,bR as I,R as B,bS as R,bT as N,an as W,I as V,bY as D,ai as P,bJ as A,bb as U,bd as L,bK as E,aO as G,ag as T,af as H,bf as $,X as O,bO as Y,ax as J}from"./vendor-DvOQ6qlC.js";import{t as q,D as K,a as Q,m as X,e as Z,n as ee,X as te,u as ie,b as se,Y as ne,p as oe}from"./index-C0IC_AUQ.js";import{u as ae}from"./AppHook-DfcCRIWI.js";import{M as le}from"./ModalContainer-ChSAv7kt.js";import{s as re}from"./S3.controller-D6r5Uqoo.js";import{E as de,D as ce}from"./EditButton-BmJG3wb9.js";import{C as ue}from"./CustomFooter-DagBAbf1.js";import{D as he}from"./DataGrid-DsxndKbs.js";import{u as me}from"./GroupRegionHook-Cfdf5fGr.js";import{u as pe}from"./VesselInfoHook-CSuF_4V0.js";import{F as xe,a as ge,b as fe}from"./utils-guRmN1PB.js";import{c as je}from"./validation-schemas-CU7MiM1v.js";import"./maps-R0vlfPHe.js";import"./charts-Bh3hGOgg.js";const be=({value:u,onChange:h,label:m="Upload Image",maxSizeBytes:p=5242880,acceptedTypes:x="image/jpeg,image/jpg",disabled:g=!1,error:f=!1,helperText:j=""})=>{const[b,v]=e.useState(!1),[F,_]=e.useState(""),[y,C]=e.useState(""),[w,S]=e.useState(!0),z=e.useRef(null);e.useEffect((()=>{u instanceof File||(async()=>{S(!0),_("");try{if("string"==typeof u&&u)if(!u.startsWith("http")&&u.includes("/")){const e=await re.fetchCloudfrontSignedUrl(u,re.s3Config.buckets.assets.name,re.s3Config.buckets.assets.region);e?C(e):_("Failed to load existing image")}else u.startsWith("http")&&C(u);else null==u&&C("")}catch(e){_("Failed to load existing image")}finally{S(!1)}})()}),[u]);const k=()=>y||("string"==typeof u&&u?u:"");return t.jsxs(i,{sx:{width:"100%"},children:[t.jsx(s,{variant:"body2",sx:{mb:1,color:"#FFFFFF"},children:m}),t.jsx("input",{ref:z,type:"file",accept:x,onChange:async e=>{const t=e.target.files[0];if(!t)return;const i=(e=>{if(!e)return"No file selected";if(!["image/jpeg","image/jpg"].includes(e.type.toLowerCase()))return"Please select a JPG or JPEG image file only";if(e.size>p)return`File size must be less than ${p/1048576}MB`;return null})(t);i?_(i):await(async e=>{try{v(!0),_("");const t=new FileReader;t.onload=t=>{const i=t.target.result;C(i),h(e),v(!1)},t.onerror=()=>{_("Failed to read file"),v(!1)},t.readAsDataURL(e)}catch(t){_(t.message||"Failed to process image"),v(!1)}})(t)},style:{display:"none"},disabled:g||b}),t.jsx(i,{sx:{border:`2px dashed ${f?q.palette.error.main:n(q.palette.custom.mainBlue,.3)}`,borderRadius:2,p:2,pt:6,pb:6,textAlign:"center",cursor:g||b?"not-allowed":"pointer",backgroundColor:n(q.palette.custom.mainBlue,.05),transition:"all 0.2s ease-in-out","&:hover":g||b?{}:{borderColor:q.palette.custom.mainBlue,backgroundColor:n(q.palette.custom.mainBlue,.1)}},onClick:()=>{!g&&z.current&&z.current.click()},children:k()?t.jsx(i,{sx:{position:"relative"},children:w?t.jsx(i,{sx:{position:"absolute",top:0,left:0,right:0,bottom:0,display:"flex",alignItems:"center",justifyContent:"center"},children:t.jsx(o,{size:40})}):t.jsxs(t.Fragment,{children:[t.jsx(i,{component:"img",src:k(),alt:"Preview",sx:{maxWidth:"100%",maxHeight:200,borderRadius:1,objectFit:"contain"}}),!g&&t.jsx(a,{title:"Remove image",children:t.jsx(l,{onClick:e=>{e.stopPropagation(),C(""),_(""),h(""),z.current&&(z.current.value="")},disabled:b,sx:{position:"absolute",top:8,right:8,backgroundColor:n("#000000",.6),color:"#FFFFFF","&:hover":{backgroundColor:n("#000000",.8)},"&:disabled":{backgroundColor:n("#000000",.3),color:n("#FFFFFF",.5)},width:32,height:32},size:"small",children:t.jsx(r,{fontSize:"small"})})})]})}):t.jsx(i,{children:b?t.jsxs(i,{sx:{display:"flex",flexDirection:"column",alignItems:"center",gap:2},children:[t.jsx(o,{size:40}),t.jsx(s,{variant:"body2",color:"#FFFFFF",children:"Uploading..."})]}):t.jsxs(i,{sx:{display:"flex",flexDirection:"column",alignItems:"center",gap:2},children:[t.jsx(d,{sx:{fontSize:48,color:q.palette.custom.mainBlue}}),t.jsx(s,{variant:"body2",color:"#FFFFFF",children:"Click to upload image"}),t.jsxs(s,{variant:"caption",color:n("#FFFFFF",.7),children:["Supports: JPG, JPEG only (Max: ",Math.round(p/1048576),"MB)"]})]})})}),(F||j)&&t.jsxs(i,{sx:{mt:1},children:[F&&t.jsx(c,{severity:"error",sx:{mb:1},children:F}),j&&!F&&t.jsx(s,{variant:"caption",color:n("#FFFFFF",.7),children:j})]})]})},ve=({open:i,onClose:s,onSubmit:n,units:o=[],unitsLoading:a=!1,assignedUnitIds:l=[],assignedUnitIdsLoading:r=!1,regions:d=[]})=>{const c=K(),{deviceHeight:j}=ae(),[b,v]=e.useState({name:"",thumbnail_file:null,unit_id:"",is_active:!0,region_group_id:"",home_port_location:""}),[F,_]=e.useState({}),[y,C]=e.useState(!1),w=e=>t=>{const i="is_active"===e?t.target.checked:t.target.value;v((t=>({...t,[e]:i}))),F[e]&&_((t=>({...t,[e]:""})))},S=()=>{v({name:"",thumbnail_url:"",unit_id:"",is_active:!0,region_group_id:"",home_port_location:""}),_({}),s()};return t.jsx(u,{open:i,onClose:S,children:t.jsxs(le,{title:"Create New Vessel",onClose:S,children:[t.jsxs(h,{container:!0,direction:"column",sx:{gap:2,maxHeight:j<700?"60vh":"80vh",overflow:"auto",flexWrap:"nowrap",flexDirection:"column"},children:[t.jsx(h,{children:t.jsx(m,{value:b.name,sx:{minWidth:{xs:250,sm:500}},onChange:w("name"),label:"Vessel Name",variant:"filled",required:!0,error:!!F.name})}),t.jsx(h,{children:t.jsxs(m,{select:!0,value:b.unit_id,sx:{minWidth:{xs:250,sm:500}},onChange:w("unit_id"),label:"Unit ID (Optional)",variant:"filled",disabled:a||r,error:!!F.unit_id,helperText:a||r?"Loading units...":F.unit_id,children:[t.jsx(p,{value:"",children:t.jsx("em",{children:"No Unit Assigned"})}),0!==o.length||a?o.map((e=>{const i=l.includes(e.unit_id);return t.jsx(p,{value:e.unit_id,disabled:i,children:e.unit_id},e.unit_id)})):t.jsx(p,{disabled:!0,children:"No units available"})]})}),t.jsx(h,{children:t.jsx(m,{select:!0,value:b.region_group_id,sx:{minWidth:{xs:250,sm:500}},onChange:w("region_group_id"),label:"Select Region Group",variant:"filled",error:!!F.region_group_id,children:0===d.length?t.jsx(p,{disabled:!0,children:"No regions available"}):d.map((e=>t.jsxs(p,{value:e._id,children:[e.name," (",e.timezone,")"]},e._id)))})}),t.jsx(h,{children:t.jsx(m,{type:"text",value:b.home_port_location||"",onChange:w("home_port_location"),label:"Home Port Location (latitude,longitude)",variant:"filled",placeholder:"e.g., 14.5995, 120.9842",helperText:"Enter coordinates as latitude, longitude (comma separated)",error:!!F.home_port_location,fullWidth:!0})}),t.jsx(h,{children:t.jsx(be,{value:b.thumbnail_file,onChange:e=>v((t=>({...t,thumbnail_file:e}))),label:"Vessel Thumbnail (optional)",maxSizeBytes:5242880,acceptedTypes:"image/jpeg,image/jpg",error:!!F.thumbnail_file,helperText:F.thumbnail_file||"Upload a JPG or JPEG image for the vessel thumbnail"})}),t.jsx(h,{children:t.jsx(x,{control:t.jsx(g,{checked:b.is_active,onChange:w("is_active"),sx:{height:"50px",width:"80px",borderRadius:"50px","& .MuiSwitch-switchBase":{padding:"15px 4px",transform:"translate(9px, -2px)"},"& .MuiSwitch-track":{backgroundColor:"#FFFFFF",height:"30px",borderRadius:"50px"},"& .Mui-checked+.MuiSwitch-track":{backgroundColor:q.palette.custom.mainBlue+" !important",opacity:"1 !important"},"& .Mui-checked.MuiSwitch-switchBase":{transform:"translate(36px, -2px)"},"& .MuiSwitch-thumb":{backgroundColor:"#FFFFFF",height:"28px",width:"28px"},"& .Mui-disabled":{opacity:.4},"& .Mui-disabled+.MuiSwitch-track":{opacity:"0.3 !important"}}}),label:b.is_active?"Active Vessel":"Inactive Vessel"})})]}),t.jsx(h,{sx:{justifyContent:"center",display:"flex",gap:1},children:t.jsx(f,{variant:"contained",onClick:async()=>{if((()=>{const e={};return b.name.trim()||(e.name="Vessel name is required"),b.region_group_id||(e.region_group_id="Region group is required"),_(e),0===Object.keys(e).length})()){C(!0);try{const e=new FormData;if(e.append("name",b.name),""!==b.unit_id&&void 0!==b.unit_id&&e.append("unit_id",b.unit_id),e.append("is_active",b.is_active),e.append("region_group_id",b.region_group_id),b.home_port_location&&b.home_port_location.trim()){const t=b.home_port_location.split(",").map((e=>parseFloat(e.trim())));2!==t.length||isNaN(t[0])||isNaN(t[1])||e.append("home_port_location",JSON.stringify([t[1],t[0]]))}b.thumbnail_file instanceof File&&e.append("thumbnail_file",b.thumbnail_file);const t=await n(e);t.success?S():c(t.error||"Failed to create vessel",{variant:"error"})}catch{c("An unexpected error occurred",{variant:"error"})}finally{C(!1)}}},disabled:y||!b.name||!!Object.keys(F).find((e=>F[e])),children:y?"Creating...":"Create Vessel"})})]})})},Fe=({open:i,onClose:n,vessel:o,onSubmit:a,units:l=[],unitsLoading:r=!1,assignedUnitIds:d=[],assignedUnitIdsLoading:c=!1,regions:j=[]})=>{const b=K(),{deviceHeight:v}=ae(),[F,_]=e.useState({name:"",thumbnail_file:null,unit_id:"",is_active:!0,region_group_id:"",home_port_location:""}),[y,C]=e.useState({}),[w,S]=e.useState(!1);e.useEffect((()=>{if(o){let e="";if(o.home_port_location&&o.home_port_location.coordinates&&2===o.home_port_location.coordinates.length){const[t,i]=o.home_port_location.coordinates;e=`${i}, ${t}`}_({name:o.name||"",thumbnail_file:o.thumbnail_s3_key||null,unit_id:o.unit_id||"",is_active:void 0===o.is_active||o.is_active,region_group_id:o.region_group_id||"",home_port_location:e})}}),[o]);const z=e=>t=>{const i="is_active"===e?t.target.checked:t.target.value;_((t=>({...t,[e]:i}))),y[e]&&C((t=>({...t,[e]:""})))},k=()=>{C({}),n()};return o?t.jsx(u,{open:i,onClose:k,children:t.jsxs(le,{title:"Edit Vessel",onClose:k,children:[t.jsxs(h,{container:!0,direction:"column",sx:{gap:2,maxHeight:v<700?"60vh":"80vh",overflow:"auto",flexWrap:"nowrap",flexDirection:"column"},children:[t.jsx(h,{children:t.jsx(m,{value:F.name,sx:{minWidth:{xs:250,sm:500}},onChange:z("name"),label:"Vessel Name",variant:"filled",required:!0,error:!!y.name})}),t.jsx(h,{children:t.jsxs(m,{select:!0,value:F.unit_id,sx:{minWidth:{xs:250,sm:500}},onChange:z("unit_id"),label:"Unit ID (Optional)",variant:"filled",disabled:r||c,error:!!y.unit_id,helperText:r||c?"Loading units...":y.unit_id,children:[t.jsx(p,{value:"",children:t.jsx("em",{children:"No Unit Assigned"})}),0!==l.length||r?l.map((e=>{const i=o&&o.unit_id===e.unit_id,s=d.includes(e.unit_id)&&!i;return t.jsx(p,{value:e.unit_id,disabled:s,children:e.unit_id},e.unit_id)})):t.jsx(p,{disabled:!0,children:"No units available"})]})}),t.jsx(h,{children:t.jsx(m,{select:!0,value:F.region_group_id,sx:{minWidth:{xs:250,sm:500}},onChange:z("region_group_id"),label:"Select Region Group",variant:"filled",error:!!y.region_group_id,children:0===j.length?t.jsx(p,{disabled:!0,children:"No regions available"}):j.map((e=>t.jsxs(p,{value:e._id,children:[e.name," (",e.timezone,")"]},e._id)))})}),t.jsx(h,{children:t.jsx(m,{type:"text",value:F.home_port_location||"",onChange:z("home_port_location"),label:"Home Port Location (latitude, longitude)",variant:"filled",placeholder:"e.g., 14.5995, 120.9842",helperText:"Enter coordinates as latitude, longitude (comma separated)",error:!!y.home_port_coordinates,fullWidth:!0})}),t.jsx(h,{children:t.jsx(be,{value:F.thumbnail_file,onChange:e=>_((t=>({...t,thumbnail_file:e}))),label:"Vessel Thumbnail (optional)",maxSizeBytes:5242880,acceptedTypes:"image/jpeg,image/jpg",error:!!y.thumbnail_file,helperText:y.thumbnail_file||"Upload a JPG or JPEG image for the vessel thumbnail"})}),t.jsxs(h,{marginBottom:5,children:[t.jsx(x,{control:t.jsx(g,{checked:F.is_active,onChange:z("is_active"),sx:{height:"50px",width:"80px",borderRadius:"50px","& .MuiSwitch-switchBase":{padding:"15px 4px",transform:"translate(9px, -2px)"},"& .MuiSwitch-track":{backgroundColor:"#FFFFFF",height:"30px",borderRadius:"50px"},"& .Mui-checked+.MuiSwitch-track":{backgroundColor:q.palette.custom.mainBlue+" !important",opacity:"1 !important"},"& .Mui-checked.MuiSwitch-switchBase":{transform:"translate(36px, -2px)"},"& .MuiSwitch-thumb":{backgroundColor:"#FFFFFF",height:"28px",width:"28px"},"& .Mui-disabled":{opacity:.4},"& .Mui-disabled+.MuiSwitch-track":{opacity:"0.3 !important"}}}),label:F.is_active?"Active Vessel":"Inactive Vessel"}),t.jsx(s,{color:"warning",fontSize:12,maxWidth:500,children:"Warning: Inactivating this vessel will revoke its access from Invite Tokens, API Keys, Users and Notifications"})]})]}),t.jsx(h,{sx:{justifyContent:"center",display:"flex",gap:1},children:t.jsx(f,{variant:"contained",onClick:async()=>{if((()=>{const e={};return F.name.trim()||(e.name="Vessel name is required"),F.region_group_id||(e.region_group_id="Region group is required"),C(e),0===Object.keys(e).length})()){S(!0);try{const e=new FormData;let t=!1;F.name!==o.name&&(e.append("name",F.name),t=!0),F.unit_id!==o.unit_id&&(e.append("unit_id",F.unit_id),t=!0),F.is_active!==o.is_active&&(e.append("is_active",F.is_active),t=!0),F.thumbnail_file instanceof File?(e.append("thumbnail_file",F.thumbnail_file),t=!0):""===F.thumbnail_file&&o.thumbnail_s3_key?(e.append("remove_thumbnail",!0),t=!0):F.region_group_id!==o.region_group_id&&(e.append("region_group_id",F.region_group_id),t=!0);const i=o.home_port_location?[o.home_port_location.coordinates[1],o.home_port_location.coordinates[0]]:null;let s=null;if(F.home_port_location&&F.home_port_location.trim()){const e=F.home_port_location.split(",").map((e=>parseFloat(e.trim())));2!==e.length||isNaN(e[0])||isNaN(e[1])||(s=e)}if(JSON.stringify(i)!==JSON.stringify(s)&&(s?e.append("home_port_location",JSON.stringify([s[1],s[0]])):e.append("home_port_location",JSON.stringify(null)),t=!0),!t)return void k();const n=await a(e);n.success?k():b(n.error||"Failed to update vessel",{variant:"error"})}catch{b("An unexpected error occurred",{variant:"error"})}finally{S(!1)}}},disabled:w||!F.name||!!Object.keys(y).find((e=>y[e])),children:w?"Updating...":"Update Vessel"})})]})}):null},_e=({unitsHistory:e,timezone:o})=>e&&0!==e.length?t.jsxs(h,{container:!0,direction:"column",sx:{gap:1,py:1,maxHeight:{xs:"none",lg:"50vh"},overflowY:"auto",flexWrap:"nowrap"},children:[t.jsxs(h,{container:!0,alignItems:"center",sx:{gap:1,mb:1},children:[t.jsx(j,{sx:{color:q.palette.custom.mainBlue,fontSize:20}}),t.jsxs(s,{variant:"body2",fontWeight:600,color:"#FFFFFF",children:["Unit History (",e.length," ",1===e.length?"entry":"entries",")"]})]}),e.map(((e,a)=>{const l=!e.unmount_timestamp,r=Q(e.mount_timestamp).tz(o),d=e.unmount_timestamp?Q(e.unmount_timestamp).tz(o):null;return t.jsxs(h,{container:!0,alignItems:"center",sx:{gap:1,pl:3},children:[t.jsx(h,{children:t.jsx(i,{sx:{width:8,height:8,borderRadius:"50%",bgcolor:l?q.palette.success.main:q.palette.custom.offline,border:`2px solid ${l?q.palette.success.main:q.palette.custom.offline}`}})}),t.jsx(h,{size:"grow",children:t.jsx(b,{sx:{bgcolor:n(q.palette.custom.darkBlue,.3),border:`1px solid ${n(q.palette.custom.mainBlue,.2)}`,borderRadius:2,p:1.5},children:t.jsxs(h,{container:!0,justifyContent:"space-between",alignItems:"center",sx:{gap:1},children:[t.jsxs(h,{size:"grow",children:[t.jsx(s,{variant:"body2",fontWeight:600,color:"#FFFFFF",children:e.unit_id}),t.jsxs(h,{container:!0,alignItems:"center",sx:{gap:1,mt:.5},children:[t.jsx(v,{sx:{color:q.palette.success.main,fontSize:14}}),t.jsxs(s,{variant:"caption",color:q.palette.success.main,children:["Mounted: ",r.format("MMM DD, YYYY HH:mm:ss A")]})]}),d&&t.jsxs(h,{container:!0,alignItems:"center",sx:{gap:1,mt:.5},children:[t.jsx(F,{sx:{color:q.palette.warning.main,fontSize:14}}),t.jsxs(s,{variant:"caption",color:q.palette.warning.main,children:["Unmounted: ",d.format("MMM DD, YYYY HH:mm:ss A")]})]})]}),t.jsx(h,{children:t.jsx(_,{label:l?"Active":"Inactive",size:"small",sx:{bgcolor:n(l?q.palette.success.main:q.palette.custom.offline,.2),color:l?q.palette.success.main:q.palette.custom.offline,border:`1px solid ${l?q.palette.success.main:q.palette.custom.offline}`,fontWeight:600,fontSize:"0.7rem"}})})]})})})]},a)}))]}):t.jsxs(h,{container:!0,alignItems:"center",gap:1,sx:{py:2},children:[t.jsx(j,{sx:{color:q.palette.custom.offline,fontSize:20}}),t.jsx(s,{variant:"body2",color:q.palette.custom.offline,children:"No unit history available"})]}),ye=({open:a,onClose:d,imageUrl:c,vesselName:h})=>{const[m,p]=e.useState(!0),[x,g]=e.useState(!1);return e.useEffect((()=>{a&&c&&(p(!0),g(!1))}),[a,c]),t.jsx(u,{open:a,onClose:d,closeAfterTransition:!0,BackdropComponent:y,BackdropProps:{timeout:500,sx:{backgroundColor:"rgba(0, 0, 0, 0.9)"}},children:t.jsxs(i,{sx:{position:"absolute",top:"50%",left:"50%",transform:"translate(-50%, -50%)",maxWidth:"90vw",maxHeight:"90vh",outline:"none",display:"flex",flexDirection:"column",alignItems:"center"},children:[t.jsx(l,{onClick:d,sx:{position:"absolute",top:-50,right:-50,color:"#FFFFFF",bgcolor:n(q.palette.custom.darkBlue,.8),"&:hover":{bgcolor:n(q.palette.custom.darkBlue,1)},zIndex:1},children:t.jsx(r,{})}),t.jsxs(i,{sx:{position:"relative",borderRadius:2,overflow:"hidden",boxShadow:"0 8px 32px rgba(0, 0, 0, 0.5)",bgcolor:n(q.palette.custom.darkBlue,.9),border:`2px solid ${n(q.palette.custom.mainBlue,.3)}`},children:[m&&t.jsx(i,{sx:{display:"flex",alignItems:"center",justifyContent:"center",minWidth:400,minHeight:300},children:t.jsx(o,{size:60,sx:{color:q.palette.custom.mainBlue}})}),x?t.jsxs(i,{sx:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",minWidth:400,minHeight:300,color:q.palette.custom.offline,p:4},children:[t.jsx(C,{sx:{fontSize:80,mb:2}}),t.jsx(s,{variant:"h6",color:"inherit",children:"Image not available"}),t.jsx(s,{variant:"body2",color:"inherit",sx:{mt:1},children:h})]}):t.jsx("img",{src:c,alt:h,onLoad:()=>p(!1),onError:()=>{g(!0),p(!1)},style:{maxWidth:"85vw",maxHeight:"80vh",objectFit:"contain",display:m?"none":"block"}})]}),!x&&t.jsx(s,{variant:"h6",sx:{mt:2,color:"#FFFFFF",textAlign:"center",bgcolor:n(q.palette.custom.darkBlue,.8),px:3,py:1,borderRadius:1},children:h})]})})},Ce=({thumbnailS3Key:s,vesselName:a,size:l=50,enableZoom:r=!0})=>{const[d,c]=e.useState(!1),[u,h]=e.useState(!0),[m,p]=e.useState(!1),[x,g]=e.useState("");e.useEffect((()=>{(async()=>{if(!s)return g(""),void h(!1);try{h(!0),c(!1);const e=await re.fetchCloudfrontSignedUrl(s,re.s3Config.buckets.assets.name,re.s3Config.buckets.assets.region);e?g(e):c(!0)}catch(e){c(!0)}finally{h(!1)}})()}),[s]);const f=()=>{r&&!d&&x&&p(!0)};return s&&!d&&x?t.jsxs(t.Fragment,{children:[t.jsxs(i,{position:"relative",sx:{cursor:r?"pointer":"default","&:hover .zoom-overlay":r?{opacity:1}:{opacity:0}},onClick:f,children:[u&&t.jsx(i,{sx:{width:l,bgcolor:n(q.palette.custom.mainBlue,.1),border:`2px solid ${n(q.palette.custom.mainBlue,.3)}`,borderRadius:1,display:"flex",alignItems:"center",justifyContent:"center"},children:t.jsx(o,{size:.3*l})}),t.jsx(i,{component:"img",src:x,alt:a,onError:()=>{c(!0),h(!1)},onLoad:()=>{h(!1)},sx:{width:l,objectFit:"cover",border:`2px solid ${n(q.palette.custom.mainBlue,.3)}`,borderRadius:1,display:u?"none":"block",transition:"transform 0.2s ease-in-out","&:hover":r?{transform:"scale(1.05)"}:{}}}),r&&!u&&!d&&t.jsx(i,{className:"zoom-overlay",sx:{position:"absolute",top:0,left:0,right:0,bottom:0,bgcolor:n("#000000",.6),display:"flex",alignItems:"center",justifyContent:"center",borderRadius:1,opacity:0,transition:"opacity 0.2s ease-in-out"},children:t.jsx(w,{sx:{color:"#FFFFFF",fontSize:.4*l}})})]}),t.jsx(ye,{open:m,onClose:()=>p(!1),imageUrl:x,vesselName:a})]}):t.jsx(i,{sx:{width:l,bgcolor:n(q.palette.custom.mainBlue,.1),border:`2px solid ${n(q.palette.custom.mainBlue,.3)}`,borderRadius:1,display:"flex",alignItems:"center",justifyContent:"center",cursor:r?"pointer":"default"},onClick:r?f:void 0,children:t.jsx(C,{sx:{color:q.palette.custom.mainBlue,fontSize:.4*l}})})},we=({isLoading:n,filteredVessels:o,pagination:r,handlePageChange:d,handlePageSizeChange:c,handleEditClick:m,timezone:p,isVesselEditDisabled:x})=>{const[g,f]=e.useState(!1),[b,v]=e.useState(null),F=()=>{f(!1),v(null)},_=[...[{field:"serial",headerName:"",maxWidth:50,renderCell:({row:e})=>e.serial+"."},{field:"name",headerName:"Vessel Name",flex:1,minWidth:250,renderCell:({row:e})=>t.jsx(h,{container:!0,alignItems:"center",sx:{height:"100%",gap:1},children:t.jsx(s,{fontSize:"14px",fontWeight:500,children:e.name})})},{field:"unit_id",headerName:"Unit ID",flex:1,renderCell:({row:e})=>t.jsx(h,{container:!0,alignItems:"center",sx:{height:"100%",gap:1},children:t.jsx(s,{fontSize:"14px",fontWeight:400,color:e.unit_id?"inherit":q.palette.custom.offline,children:e.unit_id||"No Unit Assigned"})})},{field:"regionGroup",headerName:"Region Group",flex:1,valueGetter:e=>e?.name||"--"},{field:"home_port_location",headerName:"Coordinates",flex:1,minWidth:200,renderCell:({row:e})=>{const i=e.home_port_location;if(!i||!i.coordinates||2!==i.coordinates.length)return t.jsx(h,{container:!0,alignItems:"center",sx:{height:"100%",gap:1},children:t.jsx(s,{fontSize:"14px",fontWeight:400,color:q.palette.custom.offline,children:"No coordinates"})});const[n,o]=i.coordinates;return t.jsx(h,{container:!0,alignItems:"center",sx:{height:"100%",gap:1},children:t.jsx(s,{fontSize:"14px",fontWeight:400,children:`${o}, ${n}`})})}},{field:"isLive",headerName:"Live Status",flex:1,renderCell:({row:e})=>t.jsx(h,{container:!0,alignItems:"center",sx:{height:"100%",gap:1},children:t.jsx(s,{fontSize:"14px",fontWeight:400,color:e.isLive?q.palette.custom.live:q.palette.custom.offline,children:e.isLive?"Live":"Offline"})})},{field:"is_active",headerName:"Status",flex:1,renderHeader:()=>t.jsxs(i,{sx:{display:"flex",alignItems:"center",gap:.5},className:"MuiDataGrid-columnHeaderTitle",children:[t.jsx(s,{variant:"body2",fontWeight:600,children:"Status"}),t.jsx(a,{title:"If this value is false, it will be considered as a dev vessel and will only be shown to developers for testing.",placement:"top",arrow:!0,children:t.jsx(l,{size:"small",sx:{padding:.25},children:t.jsx(S,{sx:{fontSize:16,opacity:.7}})})})]}),renderCell:({row:e})=>t.jsx(h,{container:!0,alignItems:"center",sx:{height:"100%",gap:1},children:t.jsx(s,{fontSize:"14px",fontWeight:400,color:e.is_active?q.palette.success.main:q.palette.warning.main,children:e.is_active?"Active":"Inactive"})})},{field:"creation_timestamp",headerName:"Created",flex:1,valueGetter:e=>Q(e).tz(p).format("MMM DD, YYYY")},{field:"user",headerName:"Created By",flex:1,valueGetter:e=>e?.name||"--"},{field:"actions",headerName:"Actions",flex:1,headerAlign:"center",renderCell:e=>t.jsxs(h,{container:!0,justifyContent:"center",sx:{gap:1},children:[t.jsx(h,{children:t.jsx(a,{title:"View Unit History",children:t.jsx(l,{onClick:()=>{return t=e.row,v(t),void f(!0);var t},sx:{color:q.palette.custom.offline,"&:hover":{color:q.palette.custom.mainBlue}},children:t.jsx(j,{})})})}),t.jsx(h,{sx:{cursor:x?"not-allowed":"auto"},children:t.jsx(de,{onClick:()=>m(e.row),disabled:x,buttonStyle:{"&.MuiButtonBase-root.Mui-disabled":{color:"#9A9CA2",opacity:1}}})})]}),sortable:!1}].map((e=>({...e,filterable:!1,resizable:!1,disableColumnMenu:!0,disableReorder:!0,disableExport:!0,flex:1})))];return t.jsxs(t.Fragment,{children:[t.jsx(h,{size:"grow",sx:{overflow:"auto"},children:t.jsx(he,{loading:n,disableRowSelectionOnClick:!0,rows:o,columns:_,getRowId:e=>e._id,slots:{footer:()=>t.jsx(ue,{page:r.page,rowsPerPage:r.limit,totalRows:r.total,onPageChange:d,onRowsPerPageChange:c}),noRowsOverlay:()=>t.jsxs(h,{sx:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",height:"100%"},children:[t.jsx(z,{sx:{fontSize:"100px",color:q.palette.custom.borderColor}}),t.jsx(s,{variant:"h6",component:"div",gutterBottom:!0,color:q.palette.custom.borderColor,children:"No vessels available"})]})}})}),t.jsx(u,{open:g,onClose:F,children:t.jsx(le,{title:b?`Unit History - ${b.name}`:"Unit History",onClose:F,children:b&&t.jsxs(h,{container:!0,sx:{gap:3,minWidth:{xs:300,sm:600,md:800}},children:[t.jsxs(h,{container:!0,alignItems:"center",sx:{gap:2},children:[t.jsx(Ce,{thumbnailS3Key:b.thumbnail_s3_key,vesselName:b.name,size:200,enableZoom:!0}),t.jsxs(h,{children:[t.jsx(s,{variant:"h6",fontWeight:600,color:"#FFFFFF",children:b.name}),t.jsxs(s,{variant:"body2",color:q.palette.custom.offline,children:["Unit ID: ",b.unit_id||"No Unit Assigned"]}),t.jsxs(s,{variant:"body2",color:q.palette.custom.offline,children:["Status: ",b.is_active?"Active":"Inactive"]})]})]}),t.jsx(h,{size:{xs:12},children:t.jsx(_e,{unitsHistory:b.units_history,timezone:p})})]})})})]})},Se=({isLoading:e,filteredVessels:r,pagination:d,handlePageChange:c,handlePageSizeChange:u,handleExpandClick:m,handleEditClick:p,expandedRow:x,timezone:g,isVesselEditDisabled:f})=>t.jsxs(h,{container:!0,size:"grow",sx:{overflow:"auto",display:"block",border:`1px solid ${q.palette.custom.borderColor}`,borderRadius:"10px",p:"10px 24px"},children:[t.jsx(h,{container:!0,size:"grow",sx:{py:1},children:["#","Vessel Name","Details"].map(((e,i)=>t.jsx(h,{sx:{color:"#"==e?q.palette.custom.darkBlue:q.palette.custom.mainBlue,minWidth:"#"==e?"30px":"auto",flex:"Vessel Name"==e?1:0,padding:0,border:"none"},children:e},i)))}),e&&t.jsx(h,{container:!0,size:"grow",sx:{display:"flex",justifyContent:"center",alignItems:"center",height:{xs:"80%",sm:"80%"},overflow:"auto",mb:2},children:t.jsx(o,{size:40})}),!e&&0===r.length&&t.jsx(h,{container:!0,size:"grow",sx:{display:"flex",justifyContent:"center",alignItems:"center",height:{xs:"80%",sm:"80%"},overflow:"auto",mb:2},children:t.jsxs(h,{sx:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",height:"100%"},children:[t.jsx(z,{sx:{fontSize:"100px",color:q.palette.custom.borderColor}}),t.jsx(s,{variant:"h6",component:"div",gutterBottom:!0,color:q.palette.custom.borderColor,children:"No vessels available"})]})}),!e&&0!==r.length&&t.jsx(h,{container:!0,height:{xs:"80%",sm:"80%"},overflow:"auto",marginBottom:2,size:"grow",children:t.jsx(k,{children:t.jsx(M,{sx:{minWidth:0},"aria-labelledby":"tableTitle",children:t.jsx(I,{children:r.map((e=>t.jsxs(B.Fragment,{children:[t.jsx(R,{hover:!0,children:t.jsx(N,{colSpan:5,sx:{paddingX:"0 !important",borderBottom:0},children:t.jsx(h,{container:!0,display:"flex",children:t.jsx(h,{container:!0,display:"flex",flex:1,alignItems:"center",justifyContent:"space-between",children:t.jsxs(h,{container:!0,justifyContent:"space-between",alignItems:"center",gap:2,width:"100%",flexWrap:"nowrap",overflow:"auto",color:"#FFFFFF",children:[t.jsx(h,{size:"auto",children:t.jsx(s,{children:e.serial+"."})}),t.jsx(h,{size:"grow",children:t.jsx(s,{children:e.name})}),t.jsx(h,{size:"auto",children:t.jsx(l,{onClick:()=>m(e),sx:{padding:0},children:x?._id==e._id?t.jsx(W,{sx:{color:n("#FFFFFF",.6),padding:0,marginRight:2}}):t.jsx(V,{sx:{color:n("#FFFFFF",.6),padding:0,marginRight:2}})})})]})})})})}),t.jsx(R,{children:t.jsx(N,{colSpan:5,sx:{padding:0,borderBottom:0},children:t.jsx(D,{in:x._id==e._id,sx:{width:"100%",backgroundColor:n(q.palette.custom.offline,.08),borderRadius:"10px",padding:"0 20px","& .MuiCollapse-wrapperInner":{display:"flex",flexDirection:"column"}},children:t.jsxs(h,{container:!0,sx:{display:"flex",width:"100%",py:2,gap:"10px",flexWrap:"wrap"},children:[t.jsx(h,{size:{xs:12},children:t.jsxs(h,{container:!0,spacing:2,children:[t.jsx(h,{size:{xs:12,sm:6,md:6},children:t.jsx(h,{container:!0,justifyContent:{xs:"center",sm:"flex-start"},children:t.jsx(Ce,{thumbnailS3Key:x.thumbnail_s3_key,vesselName:x.name,size:"100%",enableZoom:!1})})}),t.jsx(h,{size:{xs:12,sm:6,md:6},children:t.jsx(h,{container:!0,spacing:1,children:[{label:"Unit ID",value:x.unit_id||"No Unit Assigned",color:x.unit_id?"#FFFFFF":q.palette.custom.offline},{label:"Status",value:x.is_active?"Active":"Inactive",color:x.is_active?q.palette.success.main:q.palette.warning.main},{label:"Live Status",value:x.isLive?"Live":"Offline",color:x.isLive?q.palette.custom.live:q.palette.custom.offline},{label:"Created",value:Q(x.creation_timestamp).tz(g).format("MMM DD, YYYY")},{label:"Created By",value:x.user?.name||"--"},{label:"Coordinates",value:(()=>{const e=x.home_port_location;if(!e||!e.coordinates||2!==e.coordinates.length)return"No coordinates";const[t,i]=e.coordinates;return`${i}, ${t}`})(),color:(()=>{const e=x.home_port_location;return e&&e.coordinates&&2===e.coordinates.length?"#FFFFFF":q.palette.custom.offline})()},{label:"Actions",renderCell:()=>t.jsx(h,{container:!0,gap:1,children:t.jsx(h,{item:!0,sx:{cursor:f?"not-allowed":"auto"},children:t.jsx(de,{onClick:()=>p(e),disabled:f,buttonStyle:{"&.MuiButtonBase-root.Mui-disabled":{color:"#9A9CA2",opacity:1}}})})})}].map(((e,n)=>t.jsx(h,{size:{xs:12},children:t.jsxs(h,{container:!0,children:[t.jsx(h,{size:{xs:4,sm:3},children:t.jsxs(s,{variant:"body2",fontWeight:600,color:"#FFFFFF",children:[e.label,":"]})}),t.jsxs(h,{size:{xs:8,sm:9},display:"flex",justifyContent:"space-between",children:[e.renderCell?e.renderCell():t.jsx(s,{variant:"body2",color:e.color||"#FFFFFF",children:e.value}),"Status"===e.label&&t.jsx(i,{sx:{display:"flex",alignItems:"center",gap:.5,color:"#FFFFFF"},children:t.jsx(a,{title:"If this value is false, it will be considered as a dev vessel and will only be shown to developers for testing.",placement:"top",arrow:!0,children:t.jsx(l,{size:"small",sx:{padding:.25},children:t.jsx(S,{sx:{fontSize:16,opacity:.7}})})})})]})]})},n)))})})]})}),t.jsxs(h,{size:{xs:12},sx:{mt:2},children:[t.jsx(P,{sx:{bgcolor:n(q.palette.custom.borderColor,.3),mb:2}}),t.jsx(_e,{unitsHistory:x.units_history,timezone:g})]})]},x._id)})})})]},e.serial)))})})})}),!e&&0!==r.length&&t.jsx(h,{size:{xs:12},sx:{mt:1},children:t.jsx(ue,{page:d.page,rowsPerPage:d.limit,totalRows:d.total,onPageChange:c,onRowsPerPageChange:u})})]});function ze({searchQuery:i,showAddVessel:s,setShowAddVessel:n,isVesselEditDisabled:o}){const{isMobile:a,timezone:l}=ae(),{vesselInfo:r}=pe(),{regions:d}=me(),[c,u]=e.useState(!0),[m,p]=e.useState([]),[x,g]=e.useState({total:0,page:1,limit:10,totalPages:0}),[f,j]=e.useState(!1),[b,v]=e.useState(null),[F,_]=e.useState({}),[y,C]=e.useState(""),[w,S]=e.useState([]),[z,k]=e.useState(!1),[M,I]=e.useState([]),[B,R]=e.useState(!1);e.useEffect((()=>{const e=X();V(),D();const t=()=>{N(),D()};return e.on("vessel/changed",t),()=>{e.off("vessel/changed",t)}}),[]),e.useEffect((()=>{const e=setTimeout((()=>{C(i),g((e=>({...e,page:1})))}),500);return()=>clearTimeout(e)}),[i]);const N=async()=>{try{u(!0);const{data:e}=await Z.get("/vesselManagement",{params:{page:x.page,limit:x.limit,search:y}});if(Object.keys(e).length>0){const{vessels:t,pagination:i}=e;p(t),g((e=>e.page!==i.page||e.limit!==i.limit||e.total!==i.total||e.totalPages!==i.totalPages?i:e))}}catch(e){}finally{u(!1)}},W=e.useMemo((()=>r&&r.length>0&&m&&m.length>0?m.map((e=>{const t=r.find((t=>t.vessel_id===e._id));return t?{...e,isLive:t.is_live}:e})):m),[m,r]);e.useEffect((()=>{N()}),[x.page,x.limit,y]);const V=async()=>{try{k(!0);const{data:e}=await Z.get("/vesselManagement/unitIds");S(e||[])}catch(e){S([])}finally{k(!1)}},D=async()=>{try{R(!0);const{data:e}=await Z.get("/vesselManagement/assignedUnitIds");I(e||[])}catch(e){I([])}finally{R(!1)}},P=(e,t)=>{g((e=>({...e,page:t})))},A=e=>{g((t=>({...t,limit:e.target.value,page:1})))},U=e=>{v(e),j(!0)},L=e.useMemo((()=>W.map(((e,t)=>({...e,serial:(x.page-1)*x.limit+t+1})))),[W,x.page,x.limit]);return t.jsxs(h,{container:!0,direction:"column",sx:{color:"#FFFFFF",height:"100%"},children:[!a&&t.jsx(we,{isLoading:c,filteredVessels:L,pagination:x,handlePageChange:P,handlePageSizeChange:A,handleEditClick:U,timezone:l,isVesselEditDisabled:o}),a&&t.jsx(Se,{isLoading:c,filteredVessels:L,pagination:x,handlePageChange:P,handlePageSizeChange:A,handleExpandClick:e=>{_(F._id===e._id?{}:e)},handleEditClick:U,expandedRow:F,timezone:l,isVesselEditDisabled:o}),!o&&t.jsx(ve,{open:s,onClose:()=>n(!1),onSubmit:async e=>{try{const{data:t}=await Z.post("/vesselManagement",e);return Object.keys(t).length>0?(D(),{success:!0}):{success:!1,error:"Failed to create vessel"}}catch(t){return{success:!1,error:"An unexpected error occurred"}}},units:w,unitsLoading:z,assignedUnitIds:M,assignedUnitIdsLoading:B,regions:d}),!o&&t.jsx(Fe,{open:f,onClose:()=>{j(!1),v(null)},vessel:b,onSubmit:async e=>{try{const{data:t}=await Z.put(`/vesselManagement/${b._id}`,e);return Object.keys(t).length>0?(j(!1),v(null),D(),{success:!0}):{success:!1,error:"Failed to update vessel"}}catch(t){return{success:!1,error:"An unexpected error occurred"}}},units:w,unitsLoading:z,assignedUnitIds:M,assignedUnitIdsLoading:B,regions:d})]})}const ke=({regionGroup:e,showUpdateModal:i,setShowUpdateModal:s,onSuccess:n,vessels:a})=>{const{fetchVesselsInfo:l}=pe(),{fetchRegions:r}=me();return a&&e?._id?t.jsx(u,{open:i,onClose:()=>{},children:t.jsx(le,{title:"Update Region Group",onClose:()=>{s(!1)},children:t.jsx(xe,{initialValues:{name:e.name,timezone:e.timezone},validationSchema:je,onSubmit:async(t,{setSubmitting:i})=>{try{i(!0);const o=await te.update({id:e._id,...t});n&&n(o.regionGroup),i(!1),s(!1),l(),r()}catch(o){}},children:({errors:e,touched:i,isSubmitting:s,values:n})=>t.jsx(ge,{children:t.jsxs(h,{container:!0,flexDirection:"column",gap:2,width:{xs:300,lg:500},children:[t.jsx(h,{children:t.jsx(fe,{as:m,required:!n.name,name:"name",value:n.name,label:"Region Name",variant:"filled",error:i.name&&Boolean(e.name),helperText:i.name&&e.name,size:"small",fullWidth:!0,sx:{backgroundColor:"transparent","& .MuiFilledInput-root":{backgroundColor:"transparent",borderBottom:"none",border:e=>`1px solid ${e.palette.custom.borderColor}`,borderRadius:"8px"},"& .MuiInputBase-input":{padding:1.5},"& .MuiFilledInput-root::after,.MuiFilledInput-root::before":{border:"none !important"},"& .MuiInputBase-root":{backgroundColor:"transparent",borderBottom:"none",border:e=>`1px solid ${e.palette.custom.borderColor}`,borderRadius:"8px"},"& .MuiInputLabel-shrink":{display:"none"}}})}),t.jsx(h,{children:t.jsx(fe,{as:m,required:!0,select:!0,name:"timezone",value:n.timezone,label:"Timezone",variant:"filled",size:"small",error:i.timezone&&Boolean(e.timezone),helperText:i.timezone&&e.timezone,fullWidth:!0,sx:{"& .MuiInputBase-input":{padding:1.5}},children:ee.map((e=>t.jsxs(p,{value:e.offset,children:[e.representative," (",e.offset,")"]},e.offset)))})}),t.jsx(h,{justifyContent:"center",display:"flex",children:t.jsx(f,{type:"submit",variant:"contained",disabled:!n.name||!n.timezone||s,sx:{backgroundColor:e=>e.palette.custom.mainBlue,color:"#FFFFFF",padding:"10px 24px"},children:s?t.jsxs(t.Fragment,{children:[t.jsx(o,{size:18}),t.jsx("span",{style:{marginLeft:10},children:"Updating"})]}):"Submit"})})]})})})})}):null},Me=({regionGroup:e,setRegionGroup:i,setDeleting:n,onSuccess:o})=>{const a=()=>{i()},{fetchVesselsInfo:l}=pe(),{fetchRegions:r}=me();return t.jsx(u,{open:Boolean(e),onClose:a,children:t.jsx(le,{title:"Delete Region Group",onClose:a,children:t.jsxs(h,{container:!0,flexDirection:"column",gap:2,width:{xs:300,sm:"auto"},children:[t.jsx(h,{children:t.jsxs(s,{children:['Are you sure you want to delete region group "',e?.name,'"?']})}),t.jsxs(h,{container:!0,gap:1,justifyContent:"center",children:[t.jsx(h,{justifyContent:"center",display:"flex",children:t.jsx(f,{variant:"contained",onClick:a,className:"btn-cancel",children:"Cancel"})}),t.jsx(h,{justifyContent:"center",display:"flex",children:t.jsx(f,{variant:"contained",color:"error",onClick:()=>{a(),n(e._id),te.delete({id:e._id}).then((()=>o&&o(e))).catch(console.error).finally((()=>n())),l(),r()},sx:{textTransform:"none",padding:"10px 24px"},children:"Delete"})})]})]})})})},Ie=({showCreateModal:e,setShowCreateModal:i,onSuccess:s,vessels:n})=>{const{fetchVesselsInfo:a}=pe(),{fetchRegions:l}=me();return n?t.jsx(u,{open:e,onClose:()=>{},children:t.jsx(le,{title:"Create New Region Group",onClose:()=>{i(!1)},children:t.jsx(xe,{initialValues:{name:"",timezone:""},validationSchema:je,onSubmit:async(e,{setSubmitting:t,resetForm:i})=>{try{t(!0);const n=await te.create(e);s&&s(n.regionGroup),i(),t(!1),a(),l()}catch(n){}},children:({errors:e,touched:i,isSubmitting:s,values:n})=>t.jsx(ge,{children:t.jsxs(h,{container:!0,flexDirection:"column",gap:2,width:{xs:300,lg:500},children:[t.jsx(h,{children:t.jsx(fe,{as:m,required:!n.name,name:"name",value:n.name,label:"Region Name",variant:"filled",error:i.name&&Boolean(e.name),helperText:i.name&&e.name,size:"small",fullWidth:!0,sx:{backgroundColor:"transparent","& .MuiFilledInput-root":{backgroundColor:"transparent",borderBottom:"none",border:e=>`1px solid ${e.palette.custom.borderColor}`,borderRadius:"8px"},"& .MuiInputBase-input":{padding:1.5},"& .MuiFilledInput-root::after,.MuiFilledInput-root::before":{border:"none !important"},"& .MuiInputBase-root":{backgroundColor:"transparent",borderBottom:"none",border:e=>`1px solid ${e.palette.custom.borderColor}`,borderRadius:"8px"},"& .MuiInputLabel-shrink":{display:"none"}}})}),t.jsx(h,{children:t.jsx(fe,{as:m,required:!0,select:!0,name:"timezone",value:n.timezone,label:"Timezone",variant:"filled",size:"small",error:i.timezone&&Boolean(e.timezone),helperText:i.timezone&&e.timezone,fullWidth:!0,sx:{"& .MuiInputBase-input":{padding:1.5}},children:ee.map((e=>t.jsxs(p,{value:e.offset,children:[e.representative," (",e.offset,")"]},e.offset)))})}),t.jsx(h,{justifyContent:"center",display:"flex",children:t.jsx(f,{type:"submit",variant:"contained",disabled:!n.name||!n.timezone||s,sx:{backgroundColor:e=>e.palette.custom.mainBlue,color:"#FFFFFF",padding:"10px 24px"},children:s?t.jsxs(t.Fragment,{children:[t.jsx(o,{size:18}),t.jsx("span",{style:{marginLeft:2},children:"Submitting"})]}):"Submit"})})]})})})})}):null};function Be({searchQuery:a,vessels:l,managedVessels:r,showCreateModal:d,setShowCreateModal:c}){const{isMobile:u,timezone:m}=ae(),{user:x}=ie(),{regions:g,fetchRegions:f}=me(),[j,b]=e.useState([]),[v,F]=e.useState([]),[y,C]=e.useState(),[w,S]=e.useState(!1),[k,M]=e.useState(),[I,B]=e.useState(),[R,N]=e.useState(!0),[W,V]=e.useState(1),[D,P]=e.useState(10);e.useEffect((()=>{T().then((()=>N(!1))).catch(console.error)}),[g,r]),e.useEffect((()=>{let e=[];const t=a.trim().toLowerCase();t?(e=j.filter((e=>e.name.toLowerCase().match(t)||e.vessels.some((e=>e.name.toLowerCase().match(t)))||e.managedVessels.some((e=>e.name.toLowerCase().match(t)))||e.created_by.name.toLowerCase().match(t))),F(e)):F(j),V(1)}),[a,j]);const T=async()=>{try{if(g){const e=g.map(((e,t)=>{const i=[];return e.vessels&&e.vessels.length>0&&r.length>0&&e.vessels.forEach((e=>{const t=r.find((t=>t.vessel_id===e.vessel_id));t&&i.push(t)})),{...e,serial:t+1,managedVessels:i}}));b(e)}else f()}catch(e){}},H=async(e="create",t=null)=>{if("create"===e){const e=[];t.vessels&&t.vessels.length>0&&r.length>0&&t.vessels.forEach((t=>{const i=r.find((e=>e.vessel_id===t.vessel_id));i&&e.push(i)}));const i={...t,managedVessels:e};b((e=>[...e,i].map(((e,t)=>({...e,serial:t+1}))))),c(!1)}else if("update"===e){const e=[];t.vessels&&t.vessels.length>0&&r.length>0&&t.vessels.forEach((t=>{const i=r.find((e=>e.vessel_id===t.vessel_id));i&&e.push(i)}));const i={...t,managedVessels:e};b((e=>e.map((e=>e._id===t._id?{...i,serial:e.serial}:e)))),S(!1)}else"delete"===e&&(b((e=>e.filter((e=>e._id!==t._id)))),B(!1))},$=({page:e,rowsPerPage:i,totalRows:o,onPageChange:a,onRowsPerPageChange:l})=>{const r=(e-1)*i+1,d=Math.min(e*i,o);return t.jsxs(h,{container:!0,justifyContent:{sm:"space-between",xs:"center"},alignItems:"center",padding:"10px",backgroundColor:e=>n(e.palette.custom.offline,.08),gap:2,sx:{borderRadius:"5px"},children:[t.jsx(h,{padding:"10px 20px",size:"auto",children:t.jsx(s,{fontSize:{xs:"12px",lg:"14px"},fontWeight:600,children:`${r} - ${d} of ${o}`})}),t.jsx(h,{size:"auto",children:t.jsx(A,{count:Math.ceil(o/i),page:e,onChange:a,shape:"rounded",siblingCount:u?0:1,boundaryCount:1,sx:{"& .MuiButtonBase-root, .MuiPaginationItem-root":{color:"#FFFFFF",minHeight:"30px",fontSize:u?"9px":"14px",borderRadius:"8px",minWidth:"32px",display:"flex",justifyContent:"center",alignItems:"center",backgroundColor:e=>n(e.palette.custom.offline,.2)},"& .MuiButtonBase-root:hover, .MuiButtonBase-root.Mui-selected":{color:"#FFFFFF",backgroundColor:e=>e.palette.custom.mainBlue}}})}),t.jsx(h,{justifyContent:"flex-end",display:"flex",size:"auto",children:t.jsx(U,{variant:"outlined",children:t.jsx(L,{value:i,onChange:l,sx:{"& .MuiOutlinedInput-notchedOutline":{border:"none"},"& .MuiSelect-select":{padding:"10px",fontSize:u?"12px":"16px",backgroundColor:e=>e.palette.custom.mainBlue,borderRadius:"5px",color:"#FFFFFF",minWidth:u?0:"80px"}},children:[5,10,20].map((e=>t.jsx(p,{value:e,children:u?e:`${e} / Page`},e)))})})})]})},O=(e,t)=>{V(t)},Y=e=>{P(e.target.value)},J=[{field:"serial",headerName:"S.No",minWidth:50},{field:"name",headerName:"Name",minWidth:300},{field:"timezone",headerName:"Timezone",minWidth:200,valueGetter:e=>`UTC ${e}`},{field:"managedVessels",headerName:"Vessels",minWidth:150,renderCell:n=>{const[o,a]=e.useState(null),l=n.row.managedVessels||[],r=()=>{a(null)};return t.jsx(t.Fragment,{children:l&&l.length>0?t.jsxs(i,{sx:{display:"flex",alignItems:"center",height:"100%"},children:[t.jsx(_,{sx:{paddingLeft:.5,paddingRight:2,display:"flex",flexDirection:"row-reverse",width:"fit-content",minWidth:{xs:"100%",xl:"50%"},borderRadius:"5px",justifyContent:"space-between",cursor:"pointer"},icon:t.jsx(E,{fontSize:"small",sx:{cursor:"pointer",opacity:.5}}),label:`${l.length} vessel${l.length>1?"s":""}`,onClick:e=>{a(e.currentTarget)}}),t.jsx(G,{anchorEl:o,open:Boolean(o),onClose:r,sx:{width:"100%"},children:l.map(((e,i)=>t.jsx(p,{onClick:r,children:e.name||`Vessel ${e.vessel_id}`},i)))})]}):t.jsx(i,{sx:{display:"flex",alignItems:"center",height:"100%"},children:t.jsx(s,{variant:"body2",sx:{opacity:.6},children:"No vessels"})})})}},{field:"creation_timestamp",headerName:"Created On",minWidth:150,valueGetter:e=>Q(e).tz(m).format(se.dateTimeFormat(x,{exclude_seconds:!0,exclude_hours:!0,exclude_minutes:!0}))},{field:"created_by",headerName:"Created By",minWidth:250,valueGetter:e=>e.name||""},{field:"actions",headerName:"Actions",minWidth:300,renderCell:e=>t.jsx(h,{container:!0,children:t.jsx(h,{children:I===e.row._id?t.jsx(o,{size:18}):t.jsxs(h,{container:!0,gap:1,children:[t.jsx(h,{children:t.jsx(de,{onClick:()=>{C(e.row),S(!0)}})}),t.jsx(h,{children:t.jsx(ce,{onClick:()=>M(e.row)})})]})})})}],q=e.useMemo((()=>[...J.map((e=>({...e,filterable:!1,sortable:!1,resizable:!1,disableColumnMenu:!0,disableReorder:!0,disableExport:!0,flex:1})))]),[J]);return t.jsxs(h,{container:!0,color:"#FFFFFF",flexDirection:"column",height:"100%",children:[t.jsx(h,{overflow:"auto",size:"grow",children:t.jsx(he,{loading:R,disableRowSelectionOnClick:!0,rows:v.slice((W-1)*D,W*D),columns:q,getRowId:e=>e._id,slots:{footer:()=>t.jsx($,{page:W,rowsPerPage:D,totalRows:v.length,onPageChange:O,onRowsPerPageChange:Y}),noRowsOverlay:()=>t.jsxs(h,{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",height:"100%",children:[t.jsx(z,{sx:{fontSize:"100px",color:e=>e.palette.custom.borderColor}}),t.jsx(s,{variant:"h6",component:"div",gutterBottom:!0,color:e=>e.palette.custom.borderColor,children:"No data available"})]})}})}),t.jsx(Ie,{showCreateModal:d,setShowCreateModal:c,vessels:l,managedVessels:r,onSuccess:e=>H("create",e)}),t.jsx(ke,{regionGroup:y,showUpdateModal:w,setShowUpdateModal:S,onSuccess:e=>H("update",e),vessels:l,managedVessels:r}),t.jsx(Me,{regionGroup:k,setRegionGroup:M,setDeleting:B,onSuccess:e=>H("delete",e)})]})}function Re(){const{user:i}=ie(),[s,n]=e.useState(""),{isMobile:o}=ae(),[a,l]=e.useState(!1),[r,d]=e.useState(!1),[c,u]=e.useState(""),{vesselInfo:m,fetchVesselsInfo:p}=pe(),[x,g]=e.useState([]),j=e.useMemo((()=>!i||!i._id||!ne()),[i]);e.useEffect((()=>{i&&i.hasPermissions([oe.manageRegionsGroups])&&b()}),[m,i]);const b=async()=>{m&&Array.isArray(m)?g(m):p()},v=e.useMemo((()=>[{value:"vessels",label:"Vessels",component:t.jsx(ze,{showAddVessel:a,setShowAddVessel:l,searchQuery:c,isVesselEditDisabled:j}),display:i?.hasPermissions([oe.manageVessels])},{value:"regionGroups",label:"Regions",component:t.jsx(Be,{searchQuery:c,vessels:x,managedVessels:m,showCreateModal:r,setShowCreateModal:d}),display:i?.hasPermissions([oe.manageRegionsGroups])}]),[i,a,c,j,r,d,x,m]),F=e=>{u(e.target.value)};return e.useEffect((()=>{s||n(v.find((e=>e.display))?.value||"")}),[v]),i&&v.some((e=>e.display))&&s&&t.jsxs(h,{container:!0,direction:"column",sx:{color:"#FFFFFF",height:"100%",overflow:"auto",backgroundColor:q.palette.custom.darkBlue},children:[t.jsxs(h,{container:!0,padding:2,display:"flex",rowGap:2,justifyContent:"space-between",alignItems:"center",flexWrap:"wrap",children:[t.jsx(h,{size:{xs:12,lg:2.5},children:t.jsx(T,{value:s,onChange:(e,t)=>n(t),sx:{width:"100%",padding:"4px",border:`2px solid ${q.palette.custom.borderColor}`,borderRadius:"8px",backgroundColor:"transparent","& .MuiTabs-flexContainer":{height:"100%"},"& .MuiButtonBase-root":{width:100/v.filter((e=>e.display)).length+"%",borderRadius:"8px"},"& .MuiButtonBase-root.Mui-selected":{backgroundColor:q.palette.custom.mainBlue}},children:v.filter((e=>e.display)).map((e=>t.jsx(H,{label:e.label,value:e.value,sx:{maxWidth:"none"}},e.value)))})}),t.jsxs(h,{container:!0,columnGap:2,justifyContent:"space-between",size:{xs:12,lg:9.4},children:["vessels"===s&&t.jsxs(h,{size:{xs:"grow",lg:"100%"},display:"flex",alignItems:"center",justifyContent:"space-between",minHeight:50,gap:1,children:[t.jsx(h,{size:{xs:"grow",lg:5.8},height:"100%",children:t.jsx($,{type:"text",value:c,onChange:F,startAdornment:t.jsx(O,{position:"start",children:t.jsx(Y,{sx:{color:"#FFFFFF"}})}),placeholder:"Search by vessel name or unit ID",sx:{color:"#FFFFFF",width:"100%","& .MuiOutlinedInput-notchedOutline":{border:"2px solid",borderColor:q.palette.custom.borderColor+" !important",borderRadius:"8px"}}})}),t.jsx(h,{alignItems:"center",display:"flex",justifyContent:"flex-end",gap:2,size:"auto",height:"100%",children:t.jsx(f,{variant:"contained",sx:{"&.MuiButtonBase-root":{color:"#FFFFFF",height:{xs:"100%",lg:"auto"},padding:{xs:0,lg:"10px 20px"},backgroundColor:q.palette.custom.mainBlue,fontWeight:"bold"},"&.MuiButtonBase-root.Mui-disabled":{backgroundColor:"#9A9CA2"},"& .MuiButton-icon":{marginRight:{xs:0,lg:"10px"}}},startIcon:t.jsx(J,{}),onClick:()=>l(!0),disabled:j,children:!o&&"Add Vessel"})})]}),"regionGroups"===s&&t.jsxs(h,{size:{xs:"grow",lg:12},display:"flex",alignItems:"center",justifyContent:"space-between",minHeight:50,gap:1,children:[t.jsx(h,{size:{xs:"grow",lg:5.8},height:"100%",children:t.jsx($,{type:"text",value:c,onChange:F,startAdornment:t.jsx(O,{position:"start",children:t.jsx(Y,{sx:{color:"#FFFFFF"}})}),placeholder:"Search by name, unit or created by",sx:{color:"#FFFFFF",width:"100%","& .MuiOutlinedInput-notchedOutline":{border:"2px solid",borderColor:e=>e.palette.custom.borderColor+" !important",borderRadius:"8px"}}})}),t.jsx(h,{alignItems:"center",display:"flex",justifyContent:"flex-end",gap:2,size:"auto",height:"100%",children:t.jsx(f,{variant:"contained",sx:{"&.MuiButtonBase-root":{color:"#FFFFFF",height:{xs:"100%",lg:"auto"},padding:{xs:0,lg:"10px 20px"},backgroundColor:e=>e.palette.custom.mainBlue,fontWeight:"bold","& .MuiButton-icon":{marginRight:{xs:0,lg:"10px"}}}},startIcon:t.jsx(J,{}),onClick:()=>d(!0),children:!o&&"Create New Region"})})]})]})]}),v.filter((e=>e.display)).map((e=>t.jsx(h,{size:"grow",sx:{display:s!==e.value?"none":"block",px:2,pb:2,width:"100%"},children:e.component},e.value)))]})}export{Re as default};

import{j as e,G as o,x as t,T as n,bJ as a,bb as s,bd as i,ac as r}from"./vendor-DvOQ6qlC.js";import{t as d}from"./index-C0IC_AUQ.js";import{u as l}from"./AppHook-DfcCRIWI.js";const u=({page:u,rowsPerPage:p,totalRows:x,onPageChange:c,onRowsPerPageChange:g})=>{const{isMobile:m}=l(),h=(u-1)*p+1,f=Math.min(u*p,x);return e.jsxs(o,{container:!0,justifyContent:{sm:"space-between",xs:"center"},alignItems:"center",padding:"10px",backgroundColor:t(d.palette.custom.offline,.08),gap:2,sx:{borderRadius:"5px"},children:[e.jsx(o,{padding:"10px 20px",order:1,size:"auto",children:e.jsx(n,{fontSize:{xs:"12px",lg:"14px"},fontWeight:600,children:`${0===f?0:h} - ${f} of ${x}`})}),e.jsx(o,{order:{xs:3,md:2},size:"auto",children:e.jsx(a,{count:Math.ceil(x/p),page:u,onChange:c,shape:"rounded",siblingCount:m?0:1,boundaryCount:1,sx:{"& .MuiButtonBase-root, .MuiPaginationItem-root":{color:"#FFFFFF",minHeight:"30px",fontSize:m?"9px":"14px",borderRadius:"8px",minWidth:"32px",display:"flex",justifyContent:"center",alignItems:"center",backgroundColor:t(d.palette.custom.offline,.2)},"& .MuiButtonBase-root:hover, .MuiButtonBase-root.Mui-selected":{color:"#FFFFFF",backgroundColor:d.palette.custom.mainBlue}}})}),e.jsx(o,{justifyContent:"flex-end",display:"flex",order:{xs:2,md:3},size:"auto",children:e.jsx(s,{variant:"outlined",children:e.jsx(i,{value:p,onChange:g,sx:{"& .MuiOutlinedInput-notchedOutline":{border:"none"},"& .MuiSelect-select":{padding:"10px",fontSize:m?"12px":"16px",backgroundColor:d.palette.custom.mainBlue,borderRadius:"5px",color:"#FFFFFF",minWidth:m?0:"80px"}},children:[5,10,20].map(((o,t)=>e.jsx(r,{value:o,children:m?o:`${o} / Page`},t)))})})})]})};export{u as C};

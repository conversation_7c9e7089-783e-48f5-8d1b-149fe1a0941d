import{j as n,bF as e,G as i,T as o,L as s}from"./vendor-DvOQ6qlC.js";import{M as t}from"./ModalContainer-B8xU4Z17.js";const r=({title:r,message:l,initialState:a,onClose:c,onConfirm:d,isDanger:j=!1})=>n.jsx(e,{open:a,onClose:c,children:n.jsx(t,{title:r,onClose:c,children:n.jsxs(i,{container:!0,flexDirection:"column",gap:2,width:{xs:300,sm:"auto"},children:[n.jsx(i,{children:n.jsx(o,{fontWeight:"100",children:l})}),n.jsxs(i,{container:!0,gap:1,justifyContent:"center",children:[n.jsx(i,{justifyContent:"center",display:"flex",children:n.jsx(s,{variant:"outlined",onClick:c,children:"Cancel"})}),n.jsx(i,{justifyContent:"center",display:"flex",children:n.jsx(s,{variant:"contained",color:j?"error":void 0,onClick:d,children:"Confirm"})})]})]})})});export{r as C};

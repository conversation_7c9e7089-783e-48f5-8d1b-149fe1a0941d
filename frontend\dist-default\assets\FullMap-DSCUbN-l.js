import{r as e,C as t,_ as s,W as n,aa as o,D as a,j as r,a2 as i,a3 as l,a4 as c,a5 as d,a6 as u,T as p,aj as h,ak as f,al as m,B as g,G as x,Y as b,aD as y,ap as w,b3 as j,b4 as v,aA as C,az as S,R as k,ay as _,b5 as F,F as D,H as O,a0 as A,aw as M,ax as T,J as P,b6 as I,b7 as E,b8 as L,aB as R,x as z,b9 as V,ba as W,bb as N,bc as $,bd as B,ac as H,be as U,bf as G,am as Z,bg as Y,bh as X,aL as q}from"./vendor-DvOQ6qlC.js";import{_ as J,m as K,n as Q,v as ee,o as te,s as se,p as ne,q as oe,r as ae,u as re,a as ie,x as le,C as ce,F as de,G as ue,H as pe,N as he,O as fe,Q as me,R as ge,U as xe,W as be,X as ye,Y as we,Z as je}from"./Aritfact.controller-B_TK6zcN.js";import{u as ve}from"./AppHook-CeBj5aBe.js";import{M as Ce,S as Se,G as ke}from"./maps-R0vlfPHe.js";import{e as _e,U as Fe,p as De,y as Oe,m as Ae,a as Me,s as Te,c as Pe,d as Ie,S as Ee,b as Le,z as Re,A as ze,B as Ve,D as We,u as Ne,t as $e,o as Be}from"./index-BuG1pLrZ.js";import{P as He}from"./PreviewMedia-DIcmio_Q.js";import{a as Ue}from"./ArtifactFlag.controller-ehZ-A4E3.js";import{s as Ge}from"./S3.controller-F4d4iwS6.js";import{u as Ze}from"./VesselInfoHook-CWAUBsFy.js";import{u as Ye}from"./GroupRegionHook-BUoKtS-s.js";import"./charts-Bh3hGOgg.js";import"./utils-guRmN1PB.js";import"./ModalContainer-B8xU4Z17.js";const Xe=["slots","slotProps","InputProps","inputProps"],qe=e.forwardRef((function(e,i){const l=t({props:e,name:"MuiDateField"}),{slots:c,slotProps:d,InputProps:u,inputProps:p}=l,h=s(l,Xe),f=l,m=c?.textField??(e.enableAccessibleFieldDOMStructure?ne:n),g=o({elementType:m,externalSlotProps:d?.textField,externalForwardedProps:h,additionalProps:{ref:i},ownerState:f});g.inputProps=a({},p,g.inputProps),g.InputProps=a({},u,g.InputProps);const x=(e=>{const t=J(e),{forwardedProps:s,internalProps:n}=K(t,"date");return Q({forwardedProps:s,internalProps:n,valueManager:se,fieldValueManager:te,validator:ee,valueType:"date"})})(g),b=oe(x),y=ae(a({},b,{slots:c,slotProps:d}));return r.jsx(m,a({},y))}));function Je(e){return i("MuiDatePickerToolbar",e)}l("MuiDatePickerToolbar",["root","title"]);const Ke=["value","isLandscape","onChange","toolbarFormat","toolbarPlaceholder","views","className","onViewChange","view"],Qe=c(ce,{name:"MuiDatePickerToolbar",slot:"Root",overridesResolver:(e,t)=>t.root})({}),et=c(p,{name:"MuiDatePickerToolbar",slot:"Title",overridesResolver:(e,t)=>t.title})({variants:[{props:{isLandscape:!0},style:{margin:"auto 16px auto auto"}}]}),tt=e.forwardRef((function(n,o){const i=t({props:n,name:"MuiDatePickerToolbar"}),{value:l,isLandscape:c,toolbarFormat:p,toolbarPlaceholder:h="––",views:f,className:m}=i,g=s(i,Ke),x=re(),b=ie(),y=(e=>{const{classes:t}=e;return u({root:["root"],title:["title"]},Je,t)})(i),w=e.useMemo((()=>{if(!l)return h;const e=le(x,{format:p,views:f},!0);return x.formatByString(l,e)}),[l,p,h,x,f]),j=i;return r.jsx(Qe,a({ref:o,toolbarTitle:b.datePickerToolbarTitle,isLandscape:c,className:d(y.root,m)},g,{children:r.jsx(et,{variant:"h4",align:c?"left":"center",ownerState:j,className:y.title,children:w})}))}));function st(s,n){const o=re(),r=de(),i=t({props:s,name:n}),l=e.useMemo((()=>null==i.localeText?.toolbarTitle?i.localeText:a({},i.localeText,{datePickerToolbarTitle:i.localeText.toolbarTitle})),[i.localeText]);return a({},i,{localeText:l},ue({views:i.views,openTo:i.openTo,defaultViews:["year","day"],defaultOpenTo:"day"}),{disableFuture:i.disableFuture??!1,disablePast:i.disablePast??!1,minDate:pe(o,i.minDate,r.minDate),maxDate:pe(o,i.maxDate,r.maxDate),slots:a({toolbar:tt},i.slots)})}const nt=e.forwardRef((function(e,t){const s=ie(),n=re(),o=st(e,"MuiDesktopDatePicker"),r=a({day:he,month:he,year:he},o.viewRenderers),i=a({},o,{viewRenderers:r,format:le(n,o,!1),yearsPerRow:o.yearsPerRow??4,slots:a({openPickerIcon:me,field:qe},o.slots),slotProps:a({},o.slotProps,{field:e=>a({},h(o.slotProps?.field,e),fe(o),{ref:t}),toolbar:a({hidden:!0},o.slotProps?.toolbar)})}),{renderPicker:l}=ge({props:i,valueManager:se,valueType:"date",getOpenDialogAriaText:xe({utils:n,formatKey:"fullDate",contextTranslation:s.openDatePickerDialogue,propsTranslation:i.localeText?.openDatePickerDialogue}),validator:ee});return l()}));nt.propTypes={autoFocus:f.bool,className:f.string,closeOnSelect:f.bool,dayOfWeekFormatter:f.func,defaultValue:f.object,disabled:f.bool,disableFuture:f.bool,disableHighlightToday:f.bool,disableOpenPicker:f.bool,disablePast:f.bool,displayWeekNumber:f.bool,enableAccessibleFieldDOMStructure:f.any,fixedWeekNumber:f.number,format:f.string,formatDensity:f.oneOf(["dense","spacious"]),inputRef:m,label:f.node,loading:f.bool,localeText:f.object,maxDate:f.object,minDate:f.object,monthsPerRow:f.oneOf([3,4]),name:f.string,onAccept:f.func,onChange:f.func,onClose:f.func,onError:f.func,onMonthChange:f.func,onOpen:f.func,onSelectedSectionsChange:f.func,onViewChange:f.func,onYearChange:f.func,open:f.bool,openTo:f.oneOf(["day","month","year"]),orientation:f.oneOf(["landscape","portrait"]),readOnly:f.bool,reduceAnimations:f.bool,referenceDate:f.object,renderLoading:f.func,selectedSections:f.oneOfType([f.oneOf(["all","day","empty","hours","meridiem","minutes","month","seconds","weekDay","year"]),f.number]),shouldDisableDate:f.func,shouldDisableMonth:f.func,shouldDisableYear:f.func,showDaysOutsideCurrentMonth:f.bool,slotProps:f.object,slots:f.object,sx:f.oneOfType([f.arrayOf(f.oneOfType([f.func,f.object,f.bool])),f.func,f.object]),timezone:f.string,value:f.object,view:f.oneOf(["day","month","year"]),viewRenderers:f.shape({day:f.func,month:f.func,year:f.func}),views:f.arrayOf(f.oneOf(["day","month","year"]).isRequired),yearsOrder:f.oneOf(["asc","desc"]),yearsPerRow:f.oneOf([3,4])};const ot=e.forwardRef((function(e,t){const s=ie(),n=re(),o=st(e,"MuiMobileDatePicker"),r=a({day:he,month:he,year:he},o.viewRenderers),i=a({},o,{viewRenderers:r,format:le(n,o,!1),slots:a({field:qe},o.slots),slotProps:a({},o.slotProps,{field:e=>a({},h(o.slotProps?.field,e),fe(o),{ref:t}),toolbar:a({hidden:!1},o.slotProps?.toolbar)})}),{renderPicker:l}=be({props:i,valueManager:se,valueType:"date",getOpenDialogAriaText:xe({utils:n,formatKey:"fullDate",contextTranslation:s.openDatePickerDialogue,propsTranslation:i.localeText?.openDatePickerDialogue}),validator:ee});return l()}));ot.propTypes={autoFocus:f.bool,className:f.string,closeOnSelect:f.bool,dayOfWeekFormatter:f.func,defaultValue:f.object,disabled:f.bool,disableFuture:f.bool,disableHighlightToday:f.bool,disableOpenPicker:f.bool,disablePast:f.bool,displayWeekNumber:f.bool,enableAccessibleFieldDOMStructure:f.any,fixedWeekNumber:f.number,format:f.string,formatDensity:f.oneOf(["dense","spacious"]),inputRef:m,label:f.node,loading:f.bool,localeText:f.object,maxDate:f.object,minDate:f.object,monthsPerRow:f.oneOf([3,4]),name:f.string,onAccept:f.func,onChange:f.func,onClose:f.func,onError:f.func,onMonthChange:f.func,onOpen:f.func,onSelectedSectionsChange:f.func,onViewChange:f.func,onYearChange:f.func,open:f.bool,openTo:f.oneOf(["day","month","year"]),orientation:f.oneOf(["landscape","portrait"]),readOnly:f.bool,reduceAnimations:f.bool,referenceDate:f.object,renderLoading:f.func,selectedSections:f.oneOfType([f.oneOf(["all","day","empty","hours","meridiem","minutes","month","seconds","weekDay","year"]),f.number]),shouldDisableDate:f.func,shouldDisableMonth:f.func,shouldDisableYear:f.func,showDaysOutsideCurrentMonth:f.bool,slotProps:f.object,slots:f.object,sx:f.oneOfType([f.arrayOf(f.oneOfType([f.func,f.object,f.bool])),f.func,f.object]),timezone:f.string,value:f.object,view:f.oneOf(["day","month","year"]),viewRenderers:f.shape({day:f.func,month:f.func,year:f.func}),views:f.arrayOf(f.oneOf(["day","month","year"]).isRequired),yearsOrder:f.oneOf(["asc","desc"]),yearsPerRow:f.oneOf([3,4])};const at=["desktopModeMediaQuery"],rt=e.forwardRef((function(e,n){const o=t({props:e,name:"MuiDatePicker"}),{desktopModeMediaQuery:i=ye}=o,l=s(o,at);return g(i,{defaultMatches:!0})?r.jsx(nt,a({ref:n},l)):r.jsx(ot,a({ref:n},l))}));const it=new class{async fetchAll(){return(await _e.get("/homePorts")).data}},lt=({artifact:t,artifactInfowWindow:s,vesselInfo:n,user:o})=>{const[a,i]=e.useState(null),[l,c]=e.useState(null),[d,u]=e.useState(!1),[h,f]=e.useState(null),[m,g]=e.useState(!0),j=o?.hasPermissions([De.manageArtifacts]),v=e.useMemo((()=>Oe(h||t,n)),[h,t,n]),C=async()=>{await Ue.getUserFlaggedArtifactIds(),u(Ue.isArtifactFlaggedByUser(t._id))};e.useEffect((()=>{const e=Ae();return C(),(async()=>{try{g(!0);const e=await we.getArtifactDetail(t._id);f(e)}catch(e){if("AbortError"===e.name||"CanceledError"===e.name||"ERR_CANCELED"===e.code)return;f(t)}finally{g(!1)}})(),e.on("artifacts_flagged/changed",C),()=>e.off("artifacts_flagged/changed",C)}),[t._id]),e.useEffect((()=>{if(h){const e=h.thumbnail_url,t=h.video_url,s=h.image_url;h.video_path?(c(e||s||null),i(t||null)):(c(e||s||null),i(s||null))}}),[h]);return r.jsxs(x,{container:!0,direction:"column",sx:{padding:2,backgroundColor:"#343B44",color:"white",borderRadius:2,maxWidth:330,gap:2},children:[r.jsx("style",{children:"\n                    .gm-style-iw-chr, .gm-style-iw-tc {\n                        display: none !important;\n                    }\n                    .gm-style .gm-style-iw-c {\n                        background-color: #343B44 !important;\n                        outline: none;\n                        padding: 0;\n                    }\n                    .gm-style .gm-style-iw-d {\n                        overflow: auto !important;\n                    }\n                    .gm-style .gm-style-iw-c .gm-style-iw-d::-webkit-scrollbar-thumb {\n                        background-color: #fff !important;\n                    }\n                    .gm-style .gm-style-iw-d::-webkit-scrollbar-track, .gm-style .gm-style-iw-d::-webkit-scrollbar-track-piece {\n                        background: #343B44 !important;\n                    }\n                "}),r.jsxs(x,{container:!0,justifyContent:"space-between",alignItems:"center",children:[r.jsx(p,{variant:"h6",children:t.name||"Artifact"}),r.jsx(b,{onClick:()=>{s.close()},sx:{color:"white",border:"1px solid white","&:hover":{backgroundColor:"white",color:"#4F5968"}},children:r.jsx(y,{sx:{fontSize:"14px"}})})]}),r.jsx(x,{sx:{position:"relative",backgroundColor:"#343B44",display:"flex",alignItems:"center",justifyContent:"center",height:200,borderRadius:1},children:m||!h?r.jsx(w,{variant:"rectangular",width:"100%",height:"100%",sx:{borderRadius:1,minHeight:200,minWidth:290}}):r.jsx(He,{thumbnailLink:l,originalLink:a,cardId:h._id,isImage:!h.video_path,style:{borderRadius:8},showFullscreenIconForMap:!h.video_path,showVideoThumbnail:h.video_path,showArchiveButton:j,isArchived:h?.portal?.is_archived,vesselId:h?.onboard_vessel_id,skeletonStyle:{minHeight:200,minWidth:290},flaggedArtifact:d})}),r.jsx(x,{sx:{maxHeight:"280px",display:"flex",flexDirection:"column",overflowY:"auto"},children:m||!h?r.jsxs(r.Fragment,{children:[r.jsx(w,{variant:"text",width:"80%",height:24}),r.jsx(w,{variant:"text",width:"70%",height:24}),r.jsx(w,{variant:"text",width:"60%",height:24}),r.jsx(w,{variant:"text",width:"75%",height:24}),r.jsx(w,{variant:"text",width:"90%",height:24}),r.jsx(w,{variant:"text",width:"70%",height:24}),r.jsx(w,{variant:"text",width:"60%",height:24}),r.jsx(w,{variant:"text",width:"90%",height:24}),r.jsx(w,{variant:"text",width:"85%",height:24})]}):r.jsxs(r.Fragment,{children:[r.jsxs(p,{children:[r.jsx("strong",{children:"Timestamp"}),":"," ",h.timestamp?Me(h.timestamp).tz(v).format(Pe.dateTimeFormat()):"Not available"," ",v&&Te(v)]}),r.jsxs(p,{children:[r.jsx("strong",{children:"Location:"})," ",Ie([h.location?.coordinates?.[0],h.location?.coordinates?.[1]],!!o?.use_MGRS),r.jsx("br",{})]}),r.jsxs(p,{children:[r.jsx("strong",{children:"Super Category:"})," ",h.super_category||"Not available"]}),r.jsxs(p,{children:[r.jsx("strong",{children:"Category:"})," ",h.category||"Not available"]}),r.jsxs(p,{children:[r.jsx("strong",{children:"Color:"})," ",h.color||"Not available"]}),r.jsxs(p,{children:[r.jsx("strong",{children:"Size:"})," ",h.size||"Not available"]}),r.jsxs(p,{children:[r.jsx("strong",{children:"Orientation:"})," ",h.vessel_orientation||"Not available"]}),r.jsxs(p,{children:[r.jsx("strong",{children:"Detected Country:"})," ",h.home_country||"Not available"]}),r.jsxs(p,{children:[r.jsx("strong",{children:"Bearing Angle:"})," ",h.true_bearing?`${Number(h.true_bearing).toFixed(2)}°`:"Not available"]}),r.jsxs(p,{children:[r.jsx("strong",{children:"Features:"})," ",h.vessel_features||"Not available"]}),r.jsxs(p,{children:[r.jsx("strong",{children:"Description:"})," ",h.others||"Not available"]})]})})]})},ct=e=>r.jsx(Fe,{children:r.jsx(lt,{...e})}),dt=({currentArtifacts:t,currentClusterInfoWindow:s,vesselInfo:n,user:o,ts:a})=>{(new Date).getTime();const[i,l]=e.useState(0),[c,d]=e.useState(0),[u,h]=e.useState([]),f=e.useMemo((()=>{const e=t.filter((e=>!u.includes(e._id)));return i>=e.length&&e.length>0&&l(e.length-1),e}),[t,u]),m=o?.hasPermissions([De.manageArtifacts]),g=e.useMemo((()=>f[i]),[f,i]),[C,S]=e.useState(null),[k,_]=e.useState(null),[F,D]=e.useState(!0),[O,A]=e.useState(!0),[M,T]=e.useState(!1),[P,I]=e.useState(null),E=e.useRef(new Map),L=e.useRef(new Set),R=e.useRef(null),z=e.useRef(0),V=e.useMemo((()=>g&&g.duplications.length>=1),[g]),W=e.useMemo((()=>V?g.duplications.length+1:0),[V,g]),N=e.useMemo((()=>{if(!V||0===c)return g;const e=c-1,t=g?.duplications?.[e];return t||g}),[g,V,c]),$=e.useRef(null),B=e.useRef(null);e.useEffect((()=>{const e=V&&c>0?N:g;e&&f.length>0&&(async(e,t=!0)=>{R.current&&R.current.abort(),R.current=new AbortController;const s=++z.current;if(t&&E.current.has(e))return E.current.get(e);try{const t=await we.getArtifactDetail(e,R.current.signal);return s===z.current?(E.current.set(e,t),t):null}catch(n){return"AbortError"===n.name||"CanceledError"===n.name||"ERR_CANCELED"===n.code?null:f.find((t=>t._id===e))}})(e._id).then((t=>{const s=t.thumbnail_url,n=t.video_url,o=t.image_url;t&&e._id===N._id&&(I(t),0===c?t.video_path?(_(s||o||null),S(n||null),$.current=s||o||null,B.current=n||null):(_(s||o||null),S(o||null),$.current=s||o||null,B.current=o||null):(_($.current),S(B.current)),D(!1),A(!1),(async()=>{const e=(i+1)%f.length,t=f[e];if(t&&!L.current.has(t._id)&&!E.current.has(t._id)){L.current.add(t._id);const e=new AbortController;try{const s=await we.getArtifactDetail(t._id,e.signal);e.signal.aborted||E.current.set(t._id,s)}catch(s){s.name}finally{L.current.delete(t._id)}}})())}))}),[g,i,f,N,c]);const H=e.useMemo((()=>Oe(P||g,n)),[P,g,n]),U=e=>{R.current&&R.current.abort(),D(!0),l((t=>"prev"===e?t>0?t-1:f.length-1:(t+1)%f.length)),d(0)},G=e.useCallback((()=>{A(!0),c>0&&d(c-1)}),[c]),Z=e.useCallback((()=>{A(!0),c<W-1&&d(c+1)}),[c,W]),Y=async()=>{await Ue.getUserFlaggedArtifactIds(),g&&T(Ue.isArtifactFlaggedByUser(g._id))};return e.useEffect((()=>{Y()}),[]),e.useEffect((()=>{const e=Ae(),t=e=>{const t=e?.artifact;t&&(h((e=>[...e,t._id])),E.current.delete(t._id),I(null),S(null),_(null),D(!0))};return e.on("artifact/changed",t),e.on("artifacts_flagged/changed",Y),()=>{e.off("artifact/changed",t),e.off("artifacts_flagged/changed",Y)}}),[]),e.useEffect((()=>()=>{R.current&&R.current.abort()}),[]),r.jsxs(x,{container:!0,direction:"column",style:{color:"white",padding:"20px",background:"#343B44",maxWidth:"330px",height:"570px",display:"flex",flexDirection:"column"},children:[r.jsx("style",{children:"\n                    .gm-style-iw-chr, .gm-style-iw-tc {\n                        display: none !important;\n                    }\n                    .gm-style .gm-style-iw-c {\n                        background-color: #343B44 !important;\n                        outline: none;\n                        padding: 0;\n                    }\n                    .gm-style .gm-style-iw-d {\n                        overflow: auto !important;\n                    }\n                    .gm-style .gm-style-iw-c .gm-style-iw-d::-webkit-scrollbar-thumb {\n                        background-color: #fff !important;\n                    }\n                    .gm-style .gm-style-iw-d::-webkit-scrollbar-track, .gm-style .gm-style-iw-d::-webkit-scrollbar-track-piece {\n                        background: #343B44 !important;\n                    }\n                    .content-scroll::-webkit-scrollbar {\n                        width: 2px;\n                    }\n                    .content-scroll::-webkit-scrollbar-track {\n                        background: #343B44;\n                        border-radius: 1px;\n                    }\n                    .content-scroll::-webkit-scrollbar-thumb {\n                        background: #fff;\n                        border-radius: 1px;\n                    }\n                    .content-scroll::-webkit-scrollbar-thumb:hover {\n                        background: #ccc;\n                    }\n                "}),r.jsxs(x,{sx:{height:"230px",marginBottom:"20px"},children:[r.jsxs(x,{container:!0,justifyContent:"space-between",alignItems:"center",style:{marginBottom:"10px"},children:[r.jsxs(p,{variant:"h6",children:["Artifact ",f.length>1?`${i+1} / ${f.length}`:""]}),r.jsx(b,{onClick:()=>{R.current&&R.current.abort(),s.close()},sx:{color:"white",border:"1px solid white","&:hover":{backgroundColor:"white",color:"#4F5968"}},children:r.jsx(y,{sx:{fontSize:"16px"}})})]}),r.jsx(x,{sx:{position:"relative",backgroundColor:"#343B44",display:"flex",alignItems:"center",justifyContent:"center",height:200,borderRadius:1},children:F?r.jsx(w,{variant:"rectangular",width:"100%",height:"100%",sx:{borderRadius:1,minHeight:200,minWidth:290}}):r.jsx(He,{thumbnailLink:k,originalLink:C,cardId:P?._id||N._id,isImage:!P?.video_path,style:{borderRadius:8},showFullscreenIcon:!0,showFullscreenIconForMap:!P?.video_path,showVideoThumbnail:P?.video_path,showArchiveButton:m,isArchived:P?.portal?.is_archived,vesselId:P?.onboard_vessel_id,skeletonStyle:{minHeight:200,minWidth:290},flaggedArtifact:M,isBounding:V,det_nbbox:P?.det_nbbox,isUnified:V,unifiedArtifacts:V?[g,...g?.duplications||[]]:[]})})]}),V&&r.jsx(x,{container:!0,justifyContent:"center",sx:{marginBottom:"10px"},children:r.jsxs(x,{container:!0,justifyContent:"space-between",alignItems:"center",sx:{maxWidth:"250px"},children:[r.jsx(b,{onClick:G,disabled:0===c,sx:{color:"white",fontSize:"20px",fontWeight:"bold","&:hover":{backgroundColor:"white",color:"#4F5968"},"&:disabled":{color:"rgba(255,255,255,0.3)"}},children:r.jsx(j,{sx:{fontSize:"20px"}})}),r.jsxs(p,{variant:"caption",sx:{color:"white",fontWeight:500,minWidth:"40px",textAlign:"center",padding:"5px 17px",borderRadius:"100px"},children:["Total Detections (",String(c+1).padStart(2,"0"),"/",String(W).padStart(2,"0"),")"]}),r.jsx(b,{onClick:Z,disabled:c===W-1,sx:{color:"white",fontWeight:"bold","&:hover":{backgroundColor:"white",color:"#4F5968"},"&:disabled":{color:"rgba(255,255,255,0.3)"}},children:r.jsx(v,{sx:{fontSize:"20px"}})})]})}),r.jsx(x,{sx:{maxHeight:"265px",overflowY:"auto",flex:1,display:"flex",flexDirection:"column",paddingRight:"2px",marginTop:"10px"},className:"content-scroll",children:F||O||!P?r.jsxs(r.Fragment,{children:[r.jsx(w,{variant:"text",width:"80%",height:24,sx:{marginTop:"5px"}}),r.jsx(w,{variant:"text",width:"70%",height:24}),r.jsx(w,{variant:"text",width:"60%",height:24}),r.jsx(w,{variant:"text",width:"75%",height:24}),r.jsx(w,{variant:"text",width:"90%",height:24}),r.jsx(w,{variant:"text",width:"85%",height:24}),r.jsx(w,{variant:"text",width:"95%",height:24}),r.jsx(w,{variant:"text",width:"90%",height:24}),r.jsx(w,{variant:"text",width:"85%",height:24}),r.jsx(w,{variant:"text",width:"95%",height:24})]}):r.jsxs(r.Fragment,{children:[r.jsxs(p,{children:[r.jsx("strong",{children:"Timestamp"}),":"," ",P.timestamp?Me(P.timestamp).tz(H).format(Pe.dateTimeFormat()):"Not available"," ",H&&Te(H)]}),r.jsxs(p,{children:[r.jsx("strong",{children:"Location:"})," ",Ie([P.location?.coordinates?.[1],P.location?.coordinates?.[0]],!!o?.use_MGRS),r.jsx("br",{})]}),r.jsxs(p,{children:[r.jsx("strong",{children:"Super Category"}),": ",P.super_category||"Not available"]}),r.jsxs(p,{children:[r.jsx("strong",{children:"Category"}),": ",P.category||"Not available"]}),r.jsxs(p,{children:[r.jsx("strong",{children:"Size"}),": ",P.size||"Not available"]}),r.jsxs(p,{children:[r.jsx("strong",{children:"Color"}),": ",P.color||"Not available"]}),r.jsxs(p,{children:[r.jsx("strong",{children:"Orientation:"})," ",P.vessel_orientation||"Not available"]}),r.jsxs(p,{children:[r.jsx("strong",{children:"Detected Country:"})," ",P.home_country||"Not available"]}),r.jsxs(p,{children:[r.jsx("strong",{children:"Bearing Angle:"})," ",P.true_bearing?`${Number(P.true_bearing).toFixed(2)}°`:"Not available"]}),r.jsxs(p,{children:[r.jsx("strong",{children:"Features:"})," ",P.vessel_features||"Not available"]}),r.jsxs(p,{children:[r.jsx("strong",{children:"Description:"})," ",P.others||"Not available"]})]})}),r.jsx(x,{sx:{height:"35px",flexShrink:0,marginTop:"auto"},children:f.length>1&&r.jsxs(x,{container:!0,justifyContent:"space-between",sx:{marginBottom:"1px"},children:[r.jsx(b,{onClick:()=>U("prev"),sx:{color:"white",fontSize:"24px",fontWeight:"bold","&:hover":{backgroundColor:"white",color:"#4F5968"}},children:r.jsx(j,{sx:{fontSize:"24px"}})}),r.jsx(b,{onClick:()=>U("next"),sx:{color:"white",fontWeight:"bold","&:hover":{backgroundColor:"white",color:"#4F5968"}},children:r.jsx(v,{sx:{fontSize:"24px"}})})]})})]})},ut=e=>r.jsx(Fe,{children:r.jsx(Ee,{children:r.jsx(dt,{...e})})});function pt(e){if(isNaN(e))return"00:00";const t=Math.floor(e/60),s=Math.floor(e%60);return`${String(t).padStart(2,"0")}:${String(s).padStart(2,"0")}`}function ht({src:t}){const s=e.useRef(),n=e.useRef(),[o,a]=e.useState(0),[i,l]=e.useState(!1),[c,d]=e.useState(0),[u,h]=e.useState(0);return r.jsxs(x,{style:{background:"#282C39",borderRadius:"10px",padding:"8px 12px",display:"flex",alignItems:"center",gap:"10px",color:"white",width:"100%",height:"40px"},children:[r.jsx(b,{onClick:()=>{s.current&&(s.current.paused?(s.current.play(),l(!0)):(s.current.pause(),l(!1)))},sx:{padding:0,background:"none",border:"none",color:"white",cursor:"pointer",fontWeight:"bold"},children:i?r.jsx(C,{sx:{fontSize:20}}):r.jsx(S,{sx:{fontSize:20}})}),r.jsx(x,{ref:n,onClick:e=>{if(!s.current||!n.current)return;const t=n.current.getBoundingClientRect(),o=(e.clientX-t.left)/t.width*u;s.current.currentTime=o,d(o),a(o/u*100),s.current.play(),l(!0)},style:{flex:1,height:"7px",background:"white",borderRadius:"3px",position:"relative",cursor:"pointer"},children:r.jsx(x,{style:{height:"7px",width:`${o}%`,background:"#3A8DFF",borderRadius:"3px"}})}),r.jsx(x,{style:{minWidth:"70px",textAlign:"right",fontSize:"12px"},children:r.jsxs(p,{sx:{fontWeight:"bold"},children:[pt(c)," / ",pt(u)]})}),r.jsx("audio",{ref:s,src:t,onTimeUpdate:()=>{if(!s.current)return;const e=s.current.currentTime;d(e),a(e/u*100||0)},onLoadedMetadata:()=>{s.current&&h(s.current.duration)},onEnded:()=>{l(!1)},style:{display:"none"}})]})}const ft=({audioPoint:t,audioInfoWindow:s,user:n})=>{const[o,a]=e.useState(null);e.useEffect((()=>{t&&(async({audioPoint:e})=>{try{const t=await Ge.getSignedUrl({bucket_name:e.bucket_name,key:e.audio_path,region:e.aws_region});a(t)}catch(t){}})({audioPoint:t})}),[t]);const i=t&&t.host_location&&Array.isArray(t.host_location.coordinates)&&t.host_location.coordinates.length>0;return r.jsxs(x,{container:!0,direction:"column",sx:{padding:2,backgroundColor:"#343B44",color:"white",borderRadius:2,maxWidth:330,gap:2},children:[r.jsx("style",{children:"\n                    .gm-style-iw-chr, .gm-style-iw-tc {\n                        display: none !important;\n                    }\n                    .gm-style .gm-style-iw-c {\n                        background-color: #343B44 !important;\n                        outline: none;\n                        padding: 0;\n                    }\n                    .gm-style .gm-style-iw-d {\n                        overflow: auto !important;\n                    }\n                    .gm-style .gm-style-iw-c .gm-style-iw-d::-webkit-scrollbar-thumb {\n                        background-color: #fff !important;\n                    }\n                    .gm-style .gm-style-iw-d::-webkit-scrollbar-track, .gm-style .gm-style-iw-d::-webkit-scrollbar-track-piece {\n                        background: #343B44 !important;\n                    }\n                    \n                    audio {\n                     background-color: #343B44 !important;\n                    }\n                "}),r.jsxs(x,{container:!0,justifyContent:"space-between",alignItems:"center",children:[r.jsx(p,{variant:"h6",children:"Artifact Audio"}),r.jsx(b,{onClick:()=>{s.close()},sx:{color:"white",border:"1px solid white","&:hover":{backgroundColor:"white",color:"#4F5968"}},children:r.jsx(y,{sx:{fontSize:"14px"}})})]}),r.jsx(x,{sx:{position:"relative",backgroundColor:"#343B44",display:"flex",alignItems:"center",justifyContent:"center",borderRadius:1,marginBottom:"10px",marginTop:"10px"},children:o?r.jsx(ht,{src:o}):r.jsx(w,{sx:{width:"100%",height:"50px"}})}),r.jsxs(x,{children:[r.jsxs(p,{children:[r.jsx("strong",{children:"Frequency:"})," ",t.frequency||"Not available"]}),i&&r.jsxs(p,{children:[r.jsx("strong",{children:"Location:"})," ",Ie(t.host_location.coordinates,!(!n||!n.use_MGRS))]}),r.jsxs(p,{children:[r.jsx("strong",{children:"Timestamp"}),":"," ",t.timestamp?Me(t.timestamp).format(Pe.dateTimeFormat()):"Not available"," "]})]})]})},mt=e=>r.jsx(Fe,{children:r.jsx(ft,{...e})}),gt=({markers:t,currentAudioClusterInfoWindow:s,user:n})=>{const[o,a]=e.useState(0),i=t.map((e=>e.audioArtifactData)),l=i[o],[c,d]=e.useState(null),[u,h]=e.useState(!0),f=e.useRef(null);e.useEffect((()=>{l&&m({audio:l})}),[l]);const m=async({audio:e})=>{try{const t=await Ge.getSignedUrl({bucket_name:e.bucket_name,key:e.audio_path,region:e.aws_region});d(t),t&&h(!1)}catch(t){}},g=e=>{f.current&&(f.current.pause(),f.current.currentTime=0),h(!0),a((t=>"prev"===e?t>0?t-1:i.length-1:(t+1)%i.length))},C=l&&l.host_location&&Array.isArray(l.host_location.coordinates)&&l.host_location.coordinates.length>0;return r.jsxs(x,{container:!0,direction:"column",style:{color:"white",padding:"20px",background:"#343B44",maxWidth:"330px",height:"auto"},children:[r.jsx("style",{children:"\n                    .gm-style-iw-chr, .gm-style-iw-tc {\n                        display: none !important;\n                    }\n                    .gm-style .gm-style-iw-c {\n                        background-color: #343B44 !important;\n                        outline: none;\n                        padding: 0;\n                    }\n                    .gm-style .gm-style-iw-d {\n                        overflow: auto !important;\n                    }\n                    .gm-style .gm-style-iw-c .gm-style-iw-d::-webkit-scrollbar-thumb {\n                        background-color: #fff !important;\n                    }\n                    .gm-style .gm-style-iw-d::-webkit-scrollbar-track, .gm-style .gm-style-iw-d::-webkit-scrollbar-track-piece {\n                        background: #343B44 !important;\n                    }\n                "}),r.jsxs(x,{sx:{height:"auto"},children:[r.jsxs(x,{container:!0,justifyContent:"space-between",alignItems:"center",style:{marginBottom:"10px"},children:[r.jsxs(p,{variant:"h6",children:["Artifact Audio ",i.length>1?`${o+1} / ${i.length}`:""]}),r.jsx(b,{onClick:()=>{s.close()},sx:{color:"white",border:"1px solid white","&:hover":{backgroundColor:"white",color:"#4F5968"}},children:r.jsx(y,{sx:{fontSize:"16px"}})})]}),r.jsx(x,{sx:{position:"relative",backgroundColor:"#343B44",display:"flex",alignItems:"center",justifyContent:"center",borderRadius:1},children:u?r.jsx(w,{sx:{width:"100%",height:"50px"}}):r.jsx(ht,{src:c})})]}),r.jsxs(x,{sx:{height:"auto",display:"flex",flexDirection:"column"},children:[r.jsxs(p,{children:[r.jsx("strong",{children:"Frequency:"})," ",l.frequency||"Not available"]}),C&&r.jsxs(p,{children:[r.jsx("strong",{children:"Location:"})," ",Ie(l.host_location.coordinates,!(!n||!n.use_MGRS))]}),r.jsxs(p,{children:[r.jsx("strong",{children:"Timestamp"}),":"," ",l.timestamp?Me(l.timestamp).format(Pe.dateTimeFormat()):"Not available"," "]})]}),r.jsx(x,{sx:{height:"auto"},children:i.length>1&&r.jsxs(x,{container:!0,justifyContent:"space-between",sx:{marginBottom:"1px"},children:[r.jsx(b,{onClick:()=>g("prev"),sx:{color:"white",fontSize:"24px",fontWeight:"bold","&:hover":{backgroundColor:"white",color:"#4F5968"}},children:r.jsx(j,{sx:{fontSize:"24px"}})}),r.jsx(b,{onClick:()=>g("next"),sx:{color:"white",fontWeight:"bold","&:hover":{backgroundColor:"white",color:"#4F5968"}},children:r.jsx(v,{sx:{fontSize:"24px"}})})]})})]})},xt=e=>r.jsx(Fe,{children:r.jsx(gt,{...e})}),bt=[{url:`data:image/svg+xml;charset=UTF-8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="${Pe.icons.image}" /></svg>`,height:50,width:50,textColor:"#FFFFFF",textSize:14},{url:`data:image/svg+xml;charset=UTF-8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="${Pe.icons.image}" /></svg>`,height:60,width:60,textColor:"#FFFFFF",textSize:16},{url:`data:image/svg+xml;charset=UTF-8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="${Pe.icons.image}" /></svg>`,height:70,width:70,textColor:"#FFFFFF",textSize:18}],yt=(e,t,s,n)=>{const o=new s.maps.OverlayView;o.draw=function(){},o.setMap(n);let a=null;if(e instanceof s.maps.Marker)a=o.getProjection().fromLatLngToDivPixel(e.getPosition());else{if(!(e instanceof s.maps.Data.Feature))return;{const t=e.getGeometry().getArray(),s=t[Math.floor(t.length/2)];a=o.getProjection().fromLatLngToDivPixel(s)}}var r,i="";return i+=a.y>0?"b":"t","tr"===(i+=a.x<0?"l":"r")?r=new s.maps.Size("coordinate"===t?-150:"artifact"===t?-290:"audio"===t?-390:"ais"===t?-150:0,"coordinate"===t?100:"artifact"===t?540:"audio"===t?250:"ais"===t?200:0):"tl"===i?r=new s.maps.Size("coordinate"===t?150:"artifact"===t?290:"audio"===t?320:"ais"===t?150:0,"coordinate"===t?100:"artifact"===t?590:"audio"===t?250:"ais"===t?200:0):"br"===i?r=new s.maps.Size("coordinate"===t?-130:"artifact"===t?-200:"audio"===t?-250:"ais"===t?-130:0,"coordinate"===t?0:"artifact"===t||"audio"===t?50:0):"bl"===i&&(r=new s.maps.Size("coordinate"===t?130:"artifact"===t?200:"audio"===t||"ais"===t?130:0,"coordinate"===t?0:"artifact"===t||"audio"===t?50:0)),r},wt=({filteredCoordinates:e,prevFilteredCoordinates:t,flightPaths:s,map:n,journeyStart:o,journeyEnd:a,showFilteredCoordinates:r,coordinates:i,google:l,setFlightPaths:c})=>{const d=[];if(Object.keys(e).forEach((n=>{s[n]&&t.current[n]&&t.current[n]===e[n].length&&d.push(n)})),Object.keys(s).filter((e=>s[e])).filter((e=>!d.includes(e))).forEach((e=>{s[e].forEach((e=>{e&&e.setMap(null)})),s[e]=null})),!n||0===Object.keys(e).length)return;const u=Me(o).valueOf(),p="now"===a?Me().valueOf():Me(a).valueOf();Object.keys(e).filter((e=>!d.includes(e))).forEach((t=>{const s=e[t],o=i[t],a=o?.filter((e=>{const t=new Date(e[1]).getTime();return t>=u&&t<=p})),d=(Date.now(),((e,t)=>{if(!e||0===e.length)return[];const s=[];let n=[],o=null;return e.forEach(((a,r)=>{const i={lat:a[2],lng:a[3],timestamp:a[1],isStationary:a[4]};return 0===r?(n.push(i),void(o=i)):e.length===r+1?(s.push({path:[n[n.length-1],i],polylineType:Pe.polylineTypes.SOLID}),void(o=i)):!t&&o.isStationary&&i.isStationary?(o.stationary_connected=!0,void(o=i)):!t&&o.stationary_connected?(s.push({path:[o,i],polylineType:Pe.polylineTypes.SOLID}),n=[i],void(o=i)):(new Date(i.timestamp)-new Date(o.timestamp)>72e5?(n.length>0&&s.push({path:[...n],polylineType:Pe.polylineTypes.SOLID}),s.push({path:[o,i],polylineType:Pe.polylineTypes.DASHED}),n=[i]):n.push(i),void(o=i))})),n.length>0&&s.push({path:[...n],polylineType:Pe.polylineTypes.SOLID}),s})(a,r));if(s.length>0){const e=d.map((({path:e,polylineType:s})=>{const o=new l.maps.Polyline({path:e.map((e=>({lat:e.lat,lng:e.lng}))),geodesic:!0,strokeColor:Pe.polylineColors[t]||"#FF0000",strokeOpacity:s==Pe.polylineTypes.SOLID?1:0,strokeWeight:2,zIndex:-999,icons:s==Pe.polylineTypes.DASHED?[{icon:{path:"M 0,-2 0,2",strokeOpacity:1,scale:2,strokeColor:Pe.polylineColors[t]||"#FF0000"},offset:"0",repeat:"20px"}]:s===Pe.polylineTypes.DOTTED?[{icon:{path:l.maps.SymbolPath.CIRCLE,fillOpacity:1,scale:2,strokeColor:Pe.polylineColors[t]||"#FF0000",fillColor:Pe.polylineColors[t]||"#FF0000"},offset:"0",repeat:"10px"}]:null});return o.setMap(n),o}));c((s=>({...s,[t]:e})))}}))},jt=({user:e,filteredCoordinates:t,prevFilteredCoordinates:s,dataSetLayers:n,setDataSetLayers:o,map:a,infoWindow:r,datapointsDistance:i,showDatapoints:l,google:c,timezone:d,interval:u,vesselInfo:p,vessels:h})=>{Date.now();const f=[];Object.keys(t).forEach((e=>{s.current[e]&&s.current[e]===t[e].length&&f.push(e)})),Object.keys(n).filter((e=>!f.includes(e))).forEach((e=>{const t=n[e];t.vesselMarker&&(t.vesselMarker.setMap(null),delete t.vesselMarker),t.forEach((e=>t.remove(e))),t.setMap(null),delete n[e]})),a&&0!==Object.keys(t).length&&Object.keys(t).filter((e=>!f.includes(e))).forEach((s=>{const n=t[s];if(0===n.length)return;const f=((e,t,s)=>{const n=[];return e.forEach(((o,a)=>{if(0===n.length||a===e.length-1)return n.push(o);const r=n[n.length-1];t.maps.geometry.spherical.computeDistanceBetween(new t.maps.LatLng(r[2],r[3]),new t.maps.LatLng(o[2],o[3]))>s&&n.push(o)})),n})(((e,t)=>{let s=null;return e.filter(((e,n)=>{if(0===n)return s=e,!0;const o=new Date(s[1]).getTime();return(new Date(e[1]).getTime()-o)/6e4>=t&&(s=e,!0)}))})(n,u),c,i).filter(((e,t,s)=>!!l||(0===t||t===s.length-1))),m={type:"FeatureCollection",features:f.map(((e,t,s)=>({type:"Feature",geometry:{type:"LineString",coordinates:[[e[3],e[2]],[e[3],e[2]]]},properties:{rotation:t<s.length-1?e[4]?c.maps.geometry.spherical.computeHeading(new c.maps.LatLng(e[2],e[3]),new c.maps.LatLng(s[t+1][2],s[t+1][3])):e[5]:0,coordinate:e,isFirstCoordinate:0===t}})))},g=new c.maps.Data;g.addGeoJson(m),g.setStyle((e=>({zIndex:999,strokeWeight:15,fillColor:"transparent",strokeColor:"transparent",icons:[{icon:{path:e.getProperty("isFirstCoordinate")?c.maps.SymbolPath.CIRCLE:c.maps.SymbolPath.FORWARD_CLOSED_ARROW,scale:e.getProperty("isFirstCoordinate")?4:2,strokeColor:"#000000",strokeWeight:e.getProperty("isFirstCoordinate")?1:.5,rotation:e.getProperty("isFirstCoordinate")?0:e.getProperty("rotation"),offset:"0%",anchor:e.getProperty("isFirstCoordinate")?new c.maps.Point(0,0):new c.maps.Point(0,3),fillColor:Pe.polylineColors[s]||"#0000FF",fillOpacity:1}}]}))),g.setMap(a),g.addListener("mouseover",(async t=>{const n=t.feature,o=n.getProperty("coordinate");let i=d,l="";const u=p&&p.find((e=>e.vessel_id===s));u&&u.timezone&&(i=u.timezone,l=Te(i));const f=`\n                            <div style="color: #fff; align-items: center; padding: 15px;">\n                                <strong>${h.find((e=>e.id===s))?.name}</strong><br/>\n                                <strong>Location:</strong> ${Ie([o[3],o[2]],!!e?.use_MGRS)}<br/>\n                                <strong>Time:</strong> ${Me(o[1]).tz(i).format(Le.dateTimeFormat(e))} ${l} <br/>\n                                <style>\n                                    .gm-style-iw-chr { display: none !important; }\n                                    .gm-style-iw-tc { display: none !important; }\n                                    .gm-style .gm-style-iw-c  { background-color: #343B44 !important; outline: none; padding: 0; }\n                                    .gm-style .gm-style-iw-d { overflow:auto !important; }\n                                    p { margin: 0; color:white }\n                                    strong { color:white }\n                                </style>\n                            </div>\n                        `;r.setContent(f),r.setPosition(t.latLng),r.open(a);const m=await yt(n,"coordinate",c,a);r.setOptions({pixelOffset:m})})),g.addListener("mouseout",(()=>{r.close()})),o((e=>({...e,[s]:g})))}))},vt=({user:e,filteredAisData:t,prevFilteredAisData:s,aisDataLayers:n,setAisDataLayers:o,map:a,infoWindow:r,google:i,timezone:l,vesselInfo:c,showAisData:d})=>{Date.now();const u=[];if(!d)return Object.keys(n).forEach((e=>{const t=n[e];t.forEach((e=>t.remove(e))),t.setMap(null),delete n[e]})),void o({});if(0===Object.keys(t).length)return Object.keys(n).forEach((e=>{const t=n[e];t.forEach((e=>t.remove(e))),t.setMap(null),delete n[e]})),void o({});if(Object.keys(t).forEach((e=>{n[e]&&s.current[e]&&s.current[e].length===t[e].length&&u.push(e)})),Object.keys(n).filter((e=>!u.includes(e))).forEach((e=>{const t=n[e];t.forEach((e=>t.remove(e))),t.setMap(null),delete n[e]})),!a)return;const p={};Object.keys(t).filter((e=>!u.includes(e))).forEach((s=>{const n=t[s];if(0===n.length)return;const o={type:"FeatureCollection",features:n.map(((e,t)=>({type:"Feature",geometry:{type:"LineString",coordinates:[[e.location.coordinates[0],e.location.coordinates[1]],[e.location.coordinates[0],e.location.coordinates[1]]]},properties:{aisPoint:e,index:t,rotation:e.details.message.nav_heading_true}})))},d=new i.maps.Data;d.addGeoJson(o);d.setStyle((e=>{const t=function({rotationInRadians:e}){return e*(180/Math.PI)}({rotationInRadians:e.getProperty("rotation")}),s=0===t,n=e.getProperty("aisPoint"),o=ze(n.details.message.design_ais_ship_type_name)||"#8B5CF6";return{zIndex:999,strokeWeight:15,fillColor:"transparent",strokeColor:"transparent",icons:[{icon:{path:s?i.maps.SymbolPath.CIRCLE:"M12 2L16 5V18L12 16L8 18V5Z",scale:s?3:.9,strokeColor:"#000000",strokeWeight:1,fillColor:o,fillOpacity:1,anchor:s?new i.maps.Point(0,0):new i.maps.Point(12,12),rotation:s?0:t}}]}})),d.setMap(a),d.addListener("mouseover",(async t=>{const s=t.feature,n=s.getProperty("aisPoint");let o=l,d="";const u=c&&c.find((e=>e.vessel_id===n.onboard_vessel_id));u&&u.timezone&&(o=u.timezone,d=Te(o));const p=`\n                            <div style="color: #fff; align-items: center; padding: 15px;">\n                               ${n.metadata.mmsi?`<strong>MMSI:</strong> ${n.metadata.mmsi}<br/>`:""}\n                                ${n.name?`<strong>Name:</strong> ${n.name}<br/>`:""}\n                                ${n.details.message.sensor_ais_class?`<strong>AIS Ship Message Class:</strong> ${n.details.message.sensor_ais_class}<br/>`:""}\n                                ${n.details.message.nav_state?`<strong>AIS Ship  State :</strong> ${n.details.message.nav_state}<br/>`:""}\n                                ${n.details.message.design_ais_ship_type_name?`<strong>AIS Ship Type:</strong> ${n.details.message.design_ais_ship_type_name}<br/>`:""}\n                                ${n.details.message.design_length_type?`<strong>Ship Length Type:</strong> ${n.details.message.design_length_type}<br/>`:""}\n                                <strong>Location:</strong> ${Ie(n.location.coordinates,!!e?.use_MGRS)}<br/>\n                                ${n.details.message.design_beam?`<strong>Ship Beam:</strong> ${n.details.message.design_beam}<br/>`:""}\n                                ${n.details.message.design_length?`<strong>Ship Length:</strong> ${n.details.message.design_length}<br/>`:""}\n                                ${n.details.message.nav_speed_over_ground?`<strong>Speed over ground:</strong> ${n.details.message.nav_speed_over_ground}<br/>`:""}\n                                ${n.details.message.comm_callsign_vhf?`<strong>VHF Call Sign:</strong> ${n.details.message.comm_callsign_vhf}<br/>`:""}\n                                <strong>Timestamp:</strong> ${Me(n.timestamp).tz(o).format(Le.dateTimeFormat(e))} ${d}<br/>\n        \n                                <style>\n                                    .gm-style-iw-chr {\n                                        display: none !important;\n                                    }\n                                    .gm-style-iw-tc {\n                                        display: none !important;\n                                    }\n                                    .gm-style .gm-style-iw-c  {\n                                        background-color: #343B44 !important;\n                                        outline: none;\n                                        padding: 0;\n                                    }\n                                    .gm-style .gm-style-iw-d {\n                                        overflow:auto !important;\n                                    }\n                                    p {\n                                        margin: 0;\n                                        color:white\n                                    }\n                                    strong {\n                                        color:white\n                                    }\n                                </style>\n                            </div>\n                        `;r.setContent(p),r.setPosition(t.latLng),r.open(a);const h=await yt(s,"ais",i,a);r.setOptions({pixelOffset:h})})),d.addListener("mouseout",(()=>{r.close()})),p[s]=d})),o((e=>({...e,...p})))},Ct=({user:e,filteredAudioData:t,prevFilteredAudioData:s,audioDataMarkers:n,setAudioDataMarkers:o,audioClustererRef:a,map:r,google:i,showAudioData:l,audioInfoWindow:c,currentAudioClusterInfoWindow:d,artifactInfowWindow:u,currentClusterInfoWindow:p,infoWindow:h})=>{Date.now();const f=[];if(!l)return Object.keys(n).forEach((e=>{n[e].forEach((e=>{e.setMap(null),i.maps.event.clearInstanceListeners(e)})),n[e]=[]})),o({}),void(a.current&&(a.current.clearMarkers(),a.current=null));if(Object.keys(t).forEach((e=>{n[e]&&s.current[e]&&s.current[e]===t[e].length&&f.push(e)})),Object.keys(n).filter((e=>!f.includes(e))).forEach((e=>{n[e].forEach((e=>{e.setMap(null),i.maps.event.clearInstanceListeners(e)})),n[e]=[]})),!r||0===Object.keys(t).length)return;const m=Object.keys(n).map((e=>n[e])).flat();Object.keys(t).filter((e=>!f.includes(e))).forEach((s=>{const n=t[s];if(0===n.length)return;const a=Pe.polylineColors[s]||"#8B5CF6",l=`data:image/svg+xml;charset=UTF-8,${encodeURIComponent((d=a,`\n            <svg width="11" height="14" viewBox="0 0 11 14" xmlns="http://www.w3.org/2000/svg">\n            <path d="M1.83301 0.150391H6.11621C6.56579 0.150472 6.99475 0.31964 7.30957 0.620117L10.3604 3.53418V3.53516C10.675 3.83549 10.8496 4.24195 10.8496 4.66504V12.25C10.8496 13.1258 10.1018 13.8494 9.16699 13.8496H1.83301C0.898152 13.8494 0.150391 13.1258 0.150391 12.25V1.75C0.150391 0.87421 0.898152 0.150559 1.83301 0.150391ZM7.58203 6.53809C7.25693 6.32348 6.81134 6.39623 6.58203 6.71191C6.35071 7.03085 6.43315 7.46589 6.7627 7.68457V7.68555C7.40898 8.11661 7.8252 8.82593 7.8252 9.625C7.8252 10.3743 7.45717 11.0473 6.88086 11.4814L6.76367 11.5645C6.43354 11.7827 6.34971 12.221 6.58203 12.5381C6.81052 12.8497 7.25919 12.9273 7.58301 12.7109C8.59776 12.0347 9.27051 10.9042 9.27051 9.625C9.27051 8.34582 8.60018 7.21539 7.58203 6.53906V6.53809ZM4.11621 7.28711C3.93403 7.28717 3.75943 7.36544 3.64355 7.50488L2.90918 8.38086H2.29199C1.96372 8.38086 1.68359 8.63878 1.68359 8.96875V10.2812C1.68359 10.6112 1.96372 10.8691 2.29199 10.8691H2.90918L3.64258 11.7432V11.7441C3.75845 11.8842 3.93358 11.9628 4.11621 11.9629H4.125C4.45327 11.9629 4.7334 11.705 4.7334 11.375V7.875C4.7334 7.54503 4.45327 7.28711 4.125 7.28711H4.11621ZM6.59082 8.30762C6.41732 8.17555 6.21371 8.18422 6.05957 8.28027C5.91019 8.37339 5.80873 8.54654 5.80859 8.74414V10.5059C5.80873 10.7036 5.91021 10.876 6.05957 10.9688C6.21345 11.0642 6.41652 11.0724 6.58984 10.9424H6.59082C6.99473 10.6345 7.25391 10.1593 7.25391 9.625C7.25391 9.09074 6.99473 8.61546 6.59082 8.30762ZM5.80859 4.15625C5.80859 4.60916 6.18847 4.96272 6.64551 4.96289H9.69824L9.42773 4.7041L6.06152 1.49121L5.80859 1.24902V4.15625Z" fill="${d}" stroke="black" stroke-width="0.3"/>\n            </svg>\n            `))}`;var d;const u=n.map((t=>{const n=new i.maps.Marker({position:{lat:t.host_location.coordinates[1],lng:t.host_location.coordinates[0]},icon:{url:l,scaledSize:new i.maps.Size(20,20),scale:6,strokeColor:"#FFFFFF",strokeWeight:2,fillColor:Pe.polylineColors[s]||"#8B5CF6",fillOpacity:.8},zIndex:1e3,map:null});return n.audioArtifactData=t,n.addListener("click",(async()=>{const s=document.createElement("div");Re.createRoot(s).render(k.createElement(mt,{audioPoint:t,audioInfoWindow:c,user:e})),c.setContent(s);const o=await yt(n,"audio",i,r);c.setOptions({pixelOffset:o}),c.open(r,n)})),m.push(n),n}));o((e=>({...e,[s]:u})))})),a.current&&a.current.clearMarkers(),a.current=new Ce({markers:m,map:r,zoomOnClick:!1,styles:bt,algorithm:new Se({maxZoom:22,radius:30}),onClusterClick:()=>{}});a.current.addListener("click",(async t=>{const s=t.markers,n=document.createElement("div"),o=(e=>{const t=new i.maps.OverlayView;t.draw=function(){},t.setMap(r);const s=e._position,n=t.getProjection().fromLatLngToDivPixel(s);if(!n)return new i.maps.Size(0,0);const o=`${n.y>0?"b":"t"}${n.x<0?"l":"r"}`;return"tr"==o?new i.maps.Size(-200,250):"tl"==o?new i.maps.Size(200,440):"br"==o?new i.maps.Size(-220,80):"bl"==o?new i.maps.Size(200,40):void 0})(t);u&&u.close(),p&&p.close(),h&&h.close();Re.createRoot(n).render(k.createElement(xt,{markers:s,currentAudioClusterInfoWindow:d,user:e})),d?(d.setContent(n),d.setOptions({pixelOffset:o})):d=new i.maps.InfoWindow({content:n,pixelOffset:o}),d.setPosition(t._position),d.open(r)}))};let St=null;const kt=new Map;function _t(e,t,s){const n=function(e,t){return St||(St=new e.maps.OverlayView,St.onAdd=function(){},St.draw=function(){},St.onRemove=function(){},St.setMap(t)),St}(e,t),o=n.getProjection();if(!o)return new e.maps.Size(0,0);const a=o.fromLatLngToDivPixel(s),r=`${a.y>0?"b":"t"}${a.x<0?"l":"r"}`;return"tr"===r?new e.maps.Size(-300,450):"tl"===r?new e.maps.Size(300,540):"br"===r?new e.maps.Size(-220,80):new e.maps.Size(200,40)}const Ft=(e,t,s,n,o,a,r,i,l,c,d,u,p,h)=>{Date.now();if(!e)return Object.keys(t.current).forEach((e=>{t.current[e].forEach((e=>{e.setMap(null),s.maps.event.clearInstanceListeners(e)})),t.current[e]=[]})),void(n.current&&(n.current.clearMarkers(),n.current.setMap(null),n.current=null));const f=[];if(Object.keys(a).forEach((e=>{t.current[e]&&t.current[e].length&&r.current[e]&&r.current[e]===a[e].length&&f.push(e)})),Object.keys(t.current).filter((e=>!f.includes(e))).forEach((e=>{t.current[e].forEach((e=>{e.setMap(null),s.maps.event.clearInstanceListeners(e)})),t.current[e]=[]})),0===Object.keys(a).length)return void(n.current&&(n.current.clearMarkers(),n.current.setMap(null),n.current=null));if(!o)return;const m=Object.keys(t.current).map((e=>t.current[e])).flat();Object.keys(a).filter((e=>!f.includes(e))).forEach((e=>{const n=a[e].map((t=>{if(!t.location)return null;const n=Pe.polylineColors[e]||"#3873E4",a=function(e,t,s){const n=`${t}|${s}`;if(!kt.has(n)){const o=e=>`\n      <svg width="40" height="36" viewBox="0 0 40 36" xmlns="http://www.w3.org/2000/svg">\n        <path d="M21.7871 13.543L15.374 22.9488L13.7263 20.8906L13.725 20.8891C13.2717 20.3273 12.5901 20 11.875 20C11.1636 20 10.4713 20.3246 10.0226 20.892C10.0223 20.8924 10.022 20.8929 10.0216 20.8933L5.02469 27.1395C5.02455 27.1396 5.02441 27.1398 5.02427 27.14C4.4496 27.8538 4.34146 28.8337 4.73708 29.6546C5.13248 30.475 5.96319 31 6.875 31H33.125C34.0038 31 34.8173 30.516 35.225 29.7303C35.6283 28.9533 35.5816 28.0138 35.0845 27.288C35.0845 27.2879 35.0844 27.2878 35.0844 27.2878L25.71 13.5386C25.2656 12.8869 24.5331 12.5 23.75 12.5C22.9711 12.5 22.2279 12.8836 21.7887 13.5406C21.7881 13.5414 21.7876 13.5422 21.7871 13.543ZM0.5 5.5C0.5 3.01833 2.51833 1 5 1H35C37.4817 1 39.5 3.01833 39.5 5.5V30.5C39.5 32.9817 37.4817 35 35 35H5C2.51833 35 0.5 32.9817 0.5 30.5V5.5ZM8.75 13.5C9.87717 13.5 10.9582 13.0522 11.7552 12.2552C12.5522 11.4582 13 10.3772 13 9.25C13 8.12283 12.5522 7.04183 11.7552 6.2448C10.9582 5.44777 9.87717 5 8.75 5C7.62283 5 6.54183 5.44777 5.7448 6.2448C4.94777 7.04183 4.5 8.12283 4.5 9.25C4.5 10.3772 4.94777 11.4582 5.7448 12.2552C6.54183 13.0522 7.62283 13.5 8.75 13.5Z" fill="${e}" stroke="#282C39"/>\n      </svg>`,a="video"===t?`\n      <svg width="40" height="35" viewBox="0 0 40 35" xmlns="http://www.w3.org/2000/svg">\n        <path d="M17.1777 9.7661L17.1775 9.766C16.9126 9.60044 16.6082 9.5088 16.2959 9.5006C15.9837 9.4924 15.6749 9.56794 15.4017 9.71937C15.1284 9.87081 14.9007 10.0926 14.7422 10.3618C14.5836 10.6309 14.5 10.9376 14.5 11.25V24.1249C14.5 24.1249 14.5 24.125 14.5 24.125C14.5 24.4413 14.5856 24.7516 14.7479 25.0231C14.9102 25.2946 15.143 25.517 15.4216 25.6667C15.7002 25.8164 16.0142 25.8878 16.3302 25.8734C16.6461 25.8589 16.9523 25.759 17.216 25.5844L17.2161 25.5844L27.2161 18.9619L27.2167 18.9615C27.4603 18.7996 27.6594 18.5793 27.7959 18.3207C27.9324 18.0621 28.0019 17.7734 27.9981 17.481C27.9942 17.1886 27.9172 16.9018 27.774 16.6468C27.6308 16.3919 27.4259 16.1769 27.1782 16.0214L27.1777 16.0211L17.1777 9.7661ZM2.18414 2.18414C3.26247 1.1058 4.72501 0.5 6.25 0.5H33.75C35.275 0.5 36.7375 1.1058 37.8159 2.18414C38.8942 3.26247 39.5 4.725 39.5 6.25V28.75C39.5 30.275 38.8942 31.7375 37.8159 32.8159C36.7375 33.8942 35.275 34.5 33.75 34.5H6.25C4.725 34.5 3.26247 33.8942 2.18414 32.8159C1.1058 31.7375 0.5 30.275 0.5 28.75V6.25C0.5 4.72501 1.1058 3.26247 2.18414 2.18414Z" fill="${s}" stroke="#282C39"/>\n      </svg>`:o(s);kt.set(n,{url:`data:image/svg+xml;charset=UTF-8,${encodeURIComponent(a)}`,scaledSize:new e.maps.Size(20,20)})}return kt.get(n)}(s,t.video_exists?"video":"image",n),r=t.location.coordinates[1],c=t.location.coordinates[0],d=new s.maps.Marker({position:{lat:r,lng:c},icon:a,map:null});return d.artifactId=t._id,d.artifact=t,d.addListener("click",(async()=>{i&&i.close(),l&&l.close();const e=document.createElement("div");Re.createRoot(e).render(k.createElement(ct,{artifact:t,artifactInfowWindow:i,timezone:p,vesselInfo:h,user:u}));const n=await yt(d,"artifact",s,o);i.setOptions({pixelOffset:n}),i.setContent(e),i.open(o,d)})),m.push(d),d})).filter(Boolean);t.current={...t.current,[e]:n}})),n.current&&n.current.clearMarkers(),n.current=new Ce({markers:m,map:o,zoomOnClick:!1,styles:bt,algorithm:new Se({maxZoom:22,radius:30}),onClusterClick:()=>{}});n.current.addListener("click",(async e=>{const t=(new Date).getTime(),n=e.markers;n.length;const a=[],r=[];for(let s=0;s<n.length;s++){const e=n[s];e.artifact&&(r.push(e.artifact),e.artifactId&&a.push(e.artifactId))}const f=document.createElement("div"),m=(e=>_t(s,o,e._position))(e);i&&i.close(),l&&l.close(),c&&c.close(),d&&d.close();Re.createRoot(f).render(k.createElement(ut,{currentArtifacts:r,currentClusterInfoWindow:l,timezone:p,vesselInfo:h,user:u,ts:t})),l?(l.setContent(f),l.setOptions({pixelOffset:m})):l=new s.maps.InfoWindow({content:f,pixelOffset:m}),l.setPosition(e._position),l.open(o)}))},Dt=({vessels:t=[],loadingVessels:s,setLoadingVessels:n,errorsomeVessels:o,setAvailableVessels:a,setEmptyVessels:i,emptyVessels:l,focusedVessel:c,datapointsDistance:d=Pe.datapointsDistance,journeyStart:u=Pe.journeyStart,journeyEnd:p=Pe.journeyEnd,initialZoom:h=Pe.zoom,interval:f=Pe.interval,setArtifactsCategories:m,selectedArtifactsCategory:g,timeSlider:x,showDatapoints:b,showArtifacts:y,showAisData:j,showAudioData:v,homePortsArtifactsMode:C,selectedNumberOfArtifacts:S,artifactsType:k,showFilteredCoordinates:F,setSelectedArtifactsCategory:D,targetArtifactId:O,setTargetArtifactId:A,clearUrlParameter:M})=>{const T={lat:0,lng:0},{google:P,timezone:I}=ve(),{vesselInfo:E}=Ze(),L=We(),{pathname:R}=_(),[z,V]=e.useState(null),[W,N]=e.useState(!0),[$,B]=e.useState({}),[H,U]=e.useState({}),[G,Z]=e.useState({}),[Y,X]=e.useState({}),[q,J]=e.useState({}),[K,Q]=e.useState({}),[ee,te]=e.useState({}),[se,ne]=e.useState({}),[oe,ae]=e.useState({}),[re,ie]=e.useState(!0),[le,ce]=e.useState({}),de=e.useRef(null),ue=e.useRef(null),pe=e.useRef([]),[he,fe]=e.useState(!0),[me,ge]=e.useState({}),xe=e.useRef({}),[be,ye]=e.useState({}),[je,Ce]=e.useState(T),Se=e.useMemo((()=>new P.maps.InfoWindow({disableAutoPan:!0})),[P]),Fe=e.useMemo((()=>new P.maps.InfoWindow({disableAutoPan:!0})),[P]),De=e.useMemo((()=>new P.maps.InfoWindow({disableAutoPan:!0})),[P]);let Oe=e.useMemo((()=>new P.maps.InfoWindow({disableAutoPan:!0})),[P]),Te=e.useMemo((()=>new P.maps.InfoWindow({disableAutoPan:!0})),[P]);const Ie=e.useRef({}),Ee=e.useRef({}),Le=e.useRef({}),Re=e.useRef({});e.useRef({});const{user:ze}=Ne();e.useEffect((()=>{})),e.useEffect((()=>{const e=H[c];if(!e&&je)return;if(!e&&!je)return Ce(T);const t=e?.[e.length-1];t&&Ce({lat:t[2],lng:t[3]})}),[c]);const $e=e.useCallback((e=>{z&&(Ee.current=Object.keys(e).reduce(((t,s)=>({...t,[s]:e[s].length})),{}))}),[z]),Be=e.useCallback((e=>{z&&(Le.current={...e})}),[z]),He=e.useCallback((e=>{z&&(Re.current=Object.keys(e).reduce(((t,s)=>({...t,[s]:e[s].length})),{}))}),[z]);e.useEffect((()=>{$e(be);(async()=>{const e={...me};Object.keys(e).forEach((t=>{e[t]=me[t].filter((e=>Me(e.timestamp).valueOf()>=u.valueOf()&&Me(e.timestamp).valueOf()<=("now"===p?Me().valueOf():p.valueOf()))).filter((e=>!(!g||0===g.length)&&g.includes(e.super_category))).filter((e=>"video"===k?e.video_exists:"image"!==k||!e.video_exists)),e[t].length>=S&&"all"!==S&&(e[t]=e[t].slice(0,S-1))}));const t=await(async(e,t)=>{if(!e||0===Object.keys(e).length)return e;if("ALL"===t)return e;try{const s=await it.fetchAll();if(!s||0===s.length)return e;const n={},o=Pe.HOME_PORT_RADIUS;for(const a in e){const r=e[a];"ONLY_NON_HOME_PORTS"===t?n[a]=r.filter((e=>!s.some((t=>Ve({lat:e.location.coordinates[1],lng:e.location.coordinates[0]},{lat:t.lat,lng:t.lng})<=o)))):"ONLY_HOME_PORTS"===t&&(n[a]=r.filter((e=>s.some((t=>Ve({lat:e.location.coordinates[1],lng:e.location.coordinates[0]},{lat:t.lat,lng:t.lng})<=o)))))}return n}catch(s){return e}})(e,C);ye(t)})()}),[me,g,S,k,u,p,C]);const Ue=e.useCallback((e=>{z&&(Ie.current=Object.keys(e).reduce(((t,s)=>({...t,[s]:e[s].length})),{}))}),[z]);e.useEffect((()=>{Date.now();Ue(H);const e={},t=Me(u).valueOf(),s="now"===p?Me().valueOf():Me(p).valueOf();Object.keys($).forEach((n=>{const o=[],a=$[n];let r=null;for(const e of a){const n=new Date(e[1]).getTime();n<t||n>s||(r&&(r[5]=P.maps.geometry.spherical.computeHeading(new P.maps.LatLng(r[2],r[3]),new P.maps.LatLng(e[2],e[3])),o.push(r)),r=e)}r&&(r[5]=0,o.push(r)),e[n]=o})),U(e)}),[$,u,p]),e.useEffect((()=>{Be(K);Date.now();const e=Object.values(q).flat().sort(((e,t)=>new Date(t.timestamp).getTime()-new Date(e.timestamp).getTime())).reduce(((e,t)=>(e.processed.includes(t.metadata.mmsi)||(e.messages[t.onboard_vessel_id]||(e.messages[t.onboard_vessel_id]=[]),e.messages[t.onboard_vessel_id].push(t),e.processed.push(t.metadata.mmsi)),e)),{messages:{},processed:[]}).messages;Q(e)}),[q,Be]),e.useEffect((()=>{He(oe);Date.now();const e={},t=Me(u).valueOf(),s="now"===p?Me().valueOf():Me(p).valueOf();Object.keys(se).forEach((n=>{const o=[],a=se[n];for(const e of a){const n=new Date(e.timestamp).getTime();n<t||n>s||o.push(e)}e[n]=o})),ae(e)}),[se,u,p,He]);const Ge=e.useRef(t);e.useEffect((()=>{const e=t.filter((e=>Ge.current.find((t=>t.id===e.id))&&Object.keys($).includes(e.id))).map((e=>e.id)),s=t.filter((e=>Ge.current.find((t=>t.id===e.id))&&Object.keys(me).includes(e.id))).map((e=>e.id)),n=t.filter((e=>Ge.current.find((t=>t.id===e.id))&&Object.keys(q).includes(e.id))).map((e=>e.id)),o=t.filter((e=>Ge.current.find((t=>t.id===e.id))&&Object.keys(se).includes(e.id))).map((e=>e.id));if(0===t.length)return B({}),ge({}),J({}),void ne({});Object.keys($).some((e=>!t.find((t=>t.id===e))))&&B((e=>{const s={};return Object.keys($).filter((e=>t.find((t=>t.id===e)))).forEach((t=>{s[t]=e[t]})),s})),Object.keys(me).some((e=>!t.find((t=>t.id===e))))&&ge((e=>{const s={};return Object.keys(me).filter((e=>t.find((t=>t.id===e)))).forEach((t=>{s[t]=e[t]})),s})),Object.keys(q).some((e=>!t.find((t=>t.id===e))))&&J((e=>{const s={};return Object.keys(q).filter((e=>t.find((t=>t.id===e)))).forEach((t=>{s[t]=e[t]})),s})),Object.keys(se).some((e=>!t.find((t=>t.id===e))))&&ne((e=>{const s={};return Object.keys(se).filter((e=>t.find((t=>t.id===e)))).forEach((t=>{s[t]=e[t]})),s})),Ge.current=t,Xe({skipVessels:e}),qe({skipVessels:s}),Je({skipVessels:n}),Ke({skipVessels:o})}),[t]),e.useEffect((()=>{Xe(),qe(),Ke()}),[x]);const Ye=e.useRef(null);e.useEffect((()=>("now"===p?(Ye.current&&clearInterval(Ye.current),Ye.current=setInterval((()=>{Xe(),qe(),Je(),Ke()}),3e5)):clearInterval(Ye.current),()=>clearInterval(Ye.current))),[x,p,$]),e.useEffect((()=>{(async()=>{try{const e=await _e.get("/artifacts/filters").then((e=>e.data)).catch((e=>{}));if(0!==Object.keys(e).length){const t=e.superCategories||[];m(t),D(t)}}catch(e){}})();const e=Ae(),t=e=>{const t=e?.artifact;t&&(t.lat=t.location.coordinates[1],t.lng=t.location.coordinates[0],ge((e=>{if(!t.onboard_vessel_id)return e;const s={...e},n=t.onboard_vessel_id,o=s[n]?[...s[n]]:[];if(t?.portal?.is_archived)s[n]=o.filter((e=>e._id!==t._id));else{const e=o.findIndex((e=>e._id===t._id));-1!==e?o[e]=t:o.push(t),s[n]=o}return s})))};return e.on("artifact/changed",t),()=>e.off("artifact/changed",t)}),[]),e.useEffect((()=>{Ie.current={}}),[d,b,F]),e.useEffect((()=>{z&&wt({filteredCoordinates:H,prevFilteredCoordinates:Ie,flightPaths:Y,map:z,journeyStart:u,journeyEnd:p,showFilteredCoordinates:F,coordinates:$,google:P,setFlightPaths:X})}),[z,$,H,P,F,u,p]),e.useEffect((()=>{z&&jt({user:ze,filteredCoordinates:H,prevFilteredCoordinates:Ie,dataSetLayers:G,setDataSetLayers:Z,map:z,infoWindow:Se,datapointsDistance:d,showDatapoints:b,google:P,timezone:I,interval:f,vesselInfo:E,vessels:t})}),[z,H,d,b,P,I,f]),e.useEffect((()=>{z&&Ft(y,xe,P,de,z,be,Ee,Fe,Te,Oe,De,ze,I,E)}),[z,be,y]),e.useEffect((()=>{z&&vt({user:ze,filteredAisData:K,prevFilteredAisData:Le,aisDataLayers:ee,setAisDataLayers:te,map:z,infoWindow:Se,google:P,timezone:I,vesselInfo:E,showAisData:j})}),[z,j,P,E,K]),e.useEffect((()=>{z&&Ct({user:ze,filteredAudioData:oe,prevFilteredAudioData:Re,audioDataMarkers:le,setAudioDataMarkers:ce,map:z,google:P,showAudioData:v,audioClustererRef:ue,audioInfoWindow:De,currentAudioClusterInfoWindow:Oe,artifactInfowWindow:Fe,currentClusterInfoWindow:Te,infoWindow:Se})}),[z,oe,v,P,E]),e.useEffect((()=>{a(t.filter((e=>H[e.id]&&H[e.id].length>0)).map((e=>e.id))),i(t.filter((e=>H[e.id]&&0===H[e.id].length)).map((e=>e.id))),W||0===t.length&&R.includes("/map")&&L("No vessel selected",{variant:"warning"})}),[t,H]),e.useEffect((()=>{const e=[...o,...l].some((e=>e.id!==c));W||0==s.length&&e&&Object.values(H).every((e=>0===e.length))&&R.includes("/map")&&L("No coordinates found for the selected vessels",{variant:"warning"})}),[s,o,l]),e.useEffect((()=>{Object.keys(G).forEach((e=>{const t=G[e];if(!t)return;const s=H[e];if(!s||0===s.length)return;const n=s[s.length-1];if(!n)return;const o=c===e?"#ff0000":Pe.polylineColors[e]||"#0000FF";t.vesselMarker?(t.vesselMarker.setPosition({lat:n[2],lng:n[3]}),t.vesselMarker.setIcon({path:Pe.icons.location,strokeColor:"#000000",strokeWeight:.5,scale:1.2,fillColor:o,fillOpacity:1,anchor:new P.maps.Point(10,20)})):t.vesselMarker=new P.maps.Marker({position:{lat:n[2],lng:n[3]},map:z,zIndex:1001,icon:{path:Pe.icons.location,strokeColor:"#000000",strokeWeight:.5,scale:1.2,fillColor:o,fillOpacity:1,anchor:new P.maps.Point(10,20)}})})),Object.keys(G).forEach((e=>{const t=G[e];t&&t.vesselMarker&&!H[e]&&(t.vesselMarker.setMap(null),delete t.vesselMarker)}))}),[c,G,H,z]);const Xe=({skipVessels:e=[]}={})=>{const t=Ge.current.filter((t=>!e.includes(t.id))).map((e=>e.id));if(0===t.length)return;n((e=>(pe.current=e.concat(t),pe.current)));const s={vesselIds:t.join(","),startTimestampISO:Me(x[0]).toISOString(),endTimestampISO:"now"===p?Me().toISOString():Me(x[1]).toISOString()};_e.get("/vesselLocations/bulk",{params:s},{meta:{showSnackbar:!1}}).then((e=>{const t=e.data;Object.entries(t).forEach((([e,t])=>{t.length>0?(a((t=>t.some((t=>t===e))?t:[...t,e])),i((t=>t.filter((t=>t!==e))))):(i((t=>t.some((t=>t===e))?t:[...t,e])),a((t=>t.filter((t=>t!==e)))))})),B((e=>({...e,...Object.keys(t).reduce(((e,s)=>(Ge.current.find((e=>e.id===s))&&(e[s]=t[s]),e)),{})})))})).catch((e=>{})).finally((()=>{n((e=>(pe.current=e.filter((e=>!t.includes(e))),pe.current))),N(!1)}))},qe=({skipVessels:e=[]}={})=>{const t=Ge.current.filter((t=>!e.includes(t.id))).map((e=>e.id));if(0===t.length)return;const s={vesselIds:t.join(","),startTimestampISO:Me(x[0]).toISOString(),endTimestampISO:"now"===p?Me().toISOString():Me(x[1]).toISOString()};_e.get("/artifacts/bulk",{params:s},{meta:{showSnackbar:!1}}).then((e=>{ge((t=>({...t,...Object.keys(e.data.artifacts).reduce(((t,s)=>(Ge.current.find((e=>e.id===s))&&(t[s]=e.data.artifacts[s]),t)),{})})))})).catch((e=>{}))},Je=({skipVessels:e=[]}={})=>{try{const t=Ge.current.filter((t=>!e.includes(t.id))).map((e=>e.id));if(0===t.length)return;const s=new Date(Date.now()-864e5).toISOString();_e.get("/vesselAis/latest",{params:{vesselIds:t.join(","),startTimestampISO:s},meta:{showSnackbar:!1}}).then((e=>{J((t=>({...t,...Object.keys(e.data).reduce(((t,s)=>(Ge.current.find((e=>e.id===s))&&(t[s]=e.data[s]),t)),{})})))})).catch((e=>{}))}catch(t){}},Ke=({skipVessels:e=[]}={})=>{const t=Ge.current.filter((t=>!e.includes(t.id))).map((e=>e.id));if(0===t.length)return;const s={vesselIds:t.join(","),startTimestamp:x[0],endTimestamp:"now"===p?Me().valueOf():x[1]};_e.get("/audios/bulk",{params:s},{meta:{showSnackbar:!1}}).then((e=>{ne((t=>({...t,...Object.keys(e.data).reduce(((t,s)=>(Ge.current.find((e=>e.id===s))&&(t[s]=e.data[s]),t)),{})})))})).catch((e=>{}))};return e.useEffect((()=>{!R.includes("/map")&&Te&&Te.close(),!R.includes("/map")&&Fe&&Fe.close(),!R.includes("/map")&&De&&De.close(),!R.includes("/map")&&Oe&&Oe.close()}),[R]),e.useEffect((()=>{if(O&&z){(async()=>{let e=null,t=null;for(const[s,n]of Object.entries(be)){const o=n.find((e=>e._id===O));if(o){e=o,t=s;break}}if(!e)for(const[s,n]of Object.entries(me)){const o=n.find((e=>e._id===O));if(o){e=o,t=s;break}}if(e||(e=await(async e=>{try{const t=await we.getArtifactDetail(e);if(t&&t.onboard_vessel_id){const e=t.onboard_vessel_id;return ge((s=>{const n={...s};return n[e]||(n[e]=[]),-1===n[e].findIndex((e=>e._id===t._id))&&(n[e]=[...n[e],{_id:t._id,location:t.location,onboard_vessel_id:t.onboard_vessel_id,super_category:t.super_category,timestamp:t.timestamp,video_exists:!!t.video_path}]),n})),t}return null}catch(t){return null}})(O),e&&(t=e.onboard_vessel_id)),e&&e.location?.coordinates){const[s,n]=e.location.coordinates,o=new P.maps.LatLng(n,s);z.panTo(o),z.setZoom(Math.max(z.getZoom(),15)),setTimeout((()=>{const e=xe.current[t];if(e){const t=e.find((e=>e.artifactId===O));t&&P.maps.event.trigger(t,"click")}}),2e3),A(null),M&&M()}else A(null),M&&M()})()}}),[O,z,P,be,me,M]),r.jsxs("div",{style:{color:"#FFFFFF",width:"100%",height:"100%"},children:[W?r.jsx(w,{animation:"wave",variant:"rectangular",height:"100%"}):r.jsx(r.Fragment,{}),r.jsx("div",{style:{display:W?"none":"flex",width:"100%",height:"100%"},children:r.jsx(ke,{mapContainerStyle:{width:"100%",height:"100%"},center:je,zoom:h,onLoad:e=>{V(e)},onUnmount:()=>{V(null),Ie.current={}},options:{mapId:"c94897bab52d6290",zoomControl:!0,streetViewControl:!1,mapTypeControl:!1,fullscreenControl:!1}})})]})},Ot=({vessels:t=[],selectedVessels:s,availableVessels:n,emptyVessels:o,errorsomeVessels:a,loadingVessels:i,handleVesselSelect:l,setFocusedVessel:c,focusedVessel:d})=>{const{devMode:u}=ve(),{regions:h}=Ye(),[f,m]=e.useState([]),[g,y]=e.useState(null);e.useEffect((()=>{if(!t.length||!h.length)return;const e=h.filter((e=>t.some((t=>t.region_group===e._id))));m(e)}),[t,h]);const w=e.useMemo((()=>t.filter((e=>e.region_group)).map((e=>({...e,region_group_object:f.find((t=>t._id===e.region_group))}))).sort(((e,t)=>{const s=e.region_group_object?.name?.toLowerCase()||"",n=t.region_group_object?.name?.toLowerCase()||"";return s<n?-1:s>n?1:(e.name?.toLowerCase()||e.id).localeCompare(t.name?.toLowerCase()||t.id)}))),[t,f]),j=e.useMemo((()=>[...new Set(w.map((e=>e.region_group_object?.name||"Ungrouped")))]),[w]),v=s.length===w.length&&w.length>0,C=s.length>0&&s.length<w.length,S=i.length>0;return r.jsxs(x,{container:!0,flexDirection:"column",sx:{p:0,m:0,color:"#fff",bgcolor:"transparent"},className:"map-step-2",minWidth:{xs:"auto",md:"auto",lg:"420px"},children:[0!==j.length&&r.jsx(x,{item:!0,sx:{borderRadius:1},children:r.jsxs(x,{container:!0,alignItems:"center",sx:{height:45,pl:2},children:[r.jsx(F,{checked:v,indeterminate:C,disabled:S,onChange:e=>{e.target.checked?w.forEach((e=>{s.some((t=>t.id===e.id))||l(e)})):s.forEach((e=>l(e)))},sx:{color:"#fff",p:0,mr:1,"&.Mui-disabled":{color:"#666"}}}),r.jsx(p,{sx:{fontSize:"20px",fontWeight:500},children:"Select All"})]})}),r.jsxs(x,{item:!0,sx:{borderRadius:1},children:[0===j.length&&r.jsx(p,{sx:{color:"#fff",textAlign:"center",p:2},children:"No vessels found"}),j.map((e=>{const t=w.filter((t=>(t.region_group_object&&t.region_group_object.name||"Ungrouped")===e)),h=t.filter((e=>s.some((t=>t.id===e.id)))).length,f=t.length,m=h===f&&f>0,j=h>0&&h<f,v=g===e,C=t.length>0&&t[0].region_group_object?t[0].region_group_object.timezone:"UTC",S=t.some((e=>i.includes(e.id)));return r.jsxs(D,{disableGutters:!0,expanded:v,onChange:()=>(e=>{y(g===e?null:e)})(e),sx:{boxShadow:"none"},children:[r.jsxs(O,{expandIcon:S?r.jsx(A,{size:20,sx:{color:"#bfc9e0"}}):v?r.jsx(M,{sx:{color:"#bfc9e0"}}):r.jsx(T,{sx:{color:"#bfc9e0"}}),sx:{height:44,pl:2,backgroundColor:"#464F59"},children:[r.jsx(F,{checked:m,indeterminate:j,disabled:S,onChange:e=>{e.target.checked?t.forEach((e=>{s.some((t=>t.id===e.id))||l(e)})):t.forEach((e=>{s.some((t=>t.id===e.id))&&l(e)}))},sx:{color:"#fff",p:0,mr:1,"&.Mui-disabled":{color:"#666"}},onClick:e=>e.stopPropagation()}),r.jsxs(p,{fontWeight:600,children:[e," (UTC ",C,")"]})]}),r.jsx(P,{sx:{p:0,m:0,maxHeight:"400px",overflowY:"auto"},children:t.map((e=>{const t=s.some((t=>t.id===e.id));let h=null;return i.includes(e.id)?h=r.jsx(A,{size:18,sx:{color:"white"}}):t&&(n.some((t=>t===e.id))?h=r.jsx(b,{onClick:t=>{t.stopPropagation(),c(e.id)},sx:{width:10,height:10},children:r.jsx(I,{sx:{color:e.id===d?"red":"white"}})}):o.some((t=>t===e.id))?h="":a.some((t=>t===e.id))&&(h=r.jsx(E,{sx:{fontSize:18,color:"yellow"}}))),r.jsxs(x,{container:!0,alignItems:"center",justifyContent:"space-between",sx:{p:"8px 16px",pl:3,"&:hover":{bgcolor:"#464F59"},cursor:i.includes(e.id)?"not-allowed":"pointer"},onClick:()=>!i.includes(e.id)&&l(e),children:[r.jsxs(x,{item:!0,display:"flex",alignItems:"center",children:[r.jsx(F,{checked:t,disabled:i.includes(e.id),sx:{color:"white",p:0,mr:1,"&.Mui-checked":{color:Pe.polylineColors[e.id]||void 0},"&.Mui-disabled":{color:"#666"}}}),r.jsx(p,{sx:{color:"white",fontWeight:500,width:{xs:"180px",sm:"220px",md:"260px",lg:"300px"},maxWidth:{xs:"180px",sm:"220px",md:"260px",lg:"300px"},whiteSpace:"normal",overflow:"hidden",textOverflow:"ellipsis",wordBreak:"break-word",display:"block"},children:u?`${e.name} (${e.unit_id||""})`:e.name})]}),r.jsx(x,{item:!0,display:"flex",alignItems:"center",children:h})]},e.id)}))})]},e)}))]})]})},At=({value:t,onChange:s,minDate:n,maxDate:o,slots:a,sx:i,iconPosition:l="end"})=>{const c=e.useRef(null),[d,u]=e.useState(!1),{user:p}=Ne(),h={width:"100%",backgroundColor:"transparent",borderRadius:"40px","& .MuiButtonBase-root":{padding:0,margin:0},"& .MuiSvgIcon-root":{color:$e.palette.custom.mediumGrey,fontSize:"18px"},fontSize:"12px",height:"40px","& .MuiInputBase-root":{display:"flex",flexDirection:"end"===l?"row":"row-reverse",padding:0,gap:"end"===l?0:"10px",border:"none",fontSize:"12px",color:$e.palette.custom.mediumGrey,justifyContent:"space-between","& input":{padding:0,maxWidth:"80px",cursor:"pointer"}}},f=()=>u(!0);e.useEffect((()=>(c.current&&c.current.addEventListener("click",f),()=>{c.current&&c.current.removeEventListener("click",f)})),[]);const m={...h,...i};return r.jsx(rt,{value:t,onChange:s,minDate:n,maxDate:o,open:d,onOpen:f,onClose:()=>u(!1),slots:a,sx:m,format:Le.dateTimeFormat(p,{exclude_hours:!0}),inputRef:c})},Mt=({journeyStart:t,journeyEnd:s,timeSlider:n,timeSliderKey:o,artifactsCategories:a,selectedArtifactsCategory:i,showDatapoints:l,showArtifacts:c,showAisData:d,showAudioData:u,disableFiltersReset:h,setJourneyStart:f,setJourneyEnd:m,setTimeSlider:g,setTimeSliderKey:y,setSelectedArtifactsCategory:j,setShowDatapoints:v,setShowArtifacts:C,setShowAisData:S,setShowAudioData:k,resetFilters:_,loadingVessels:D,selectedNumberOfArtifacts:O,setSelectedNumberOfArtifacts:A,artifactsType:M,setArtifactsType:T})=>{const{timezone:P}=ve(),{user:I}=Ne(),E=e.useMemo((()=>{const[e,t]=n,s=[];for(let n=e;n<=t;n+=864e5)s.push({value:n});return s}),[n]);return r.jsxs(x,{container:!0,flexDirection:"column",gap:1,color:"#FFFFFF",paddingX:2,paddingY:2,position:"relative",className:"map-step-4",children:[r.jsx(b,{disabled:h,disableRipple:!0,sx:{width:15,height:15,marginLeft:1,paddingX:2,position:"absolute",top:{xs:"-50px",md:"-30px"},right:{xs:"0",md:"35px"}},onClick:e=>{e.stopPropagation(),_()},children:r.jsx(L,{fontSize:"small"})}),r.jsx(p,{fontSize:"16px",fontWeight:400,textAlign:"center",children:"Date Range"}),D.length>0?r.jsxs(r.Fragment,{children:[r.jsx(x,{paddingX:4,children:r.jsx(w,{variant:"rounded",width:"100%",height:30})}),r.jsxs(x,{container:!0,justifyContent:"space-between",children:[r.jsx(x,{children:r.jsx(w,{variant:"rounded",width:100,height:30})}),r.jsx(x,{children:r.jsx(w,{variant:"rounded",width:100,height:30})})]})]}):r.jsx(r.Fragment,{children:r.jsxs(x,{container:!0,children:[r.jsx(x,{paddingX:8,size:12,children:r.jsx(R,{valueLabelFormat:e=>Me(e).tz(P).format(Le.dateTimeFormat(I,{exclude_seconds:!0})),valueLabelDisplay:"auto",defaultValue:n,min:n[0],max:n[1],step:6e4,marks:E,onChangeCommitted:(e,t)=>{f(Me(t[0])),m(Me(t[1])),Be("MapFilterApplied",{filter:"dateRange",start:t[0],end:t[1]})},sx:{color:z($e.palette.background.default,.32),"& .MuiSlider-thumb":{height:22,width:22,border:`5px solid ${$e.palette.custom.unfocused}`},"& .MuiSlider-mark":{height:"7px",width:"3px"}}},o)}),r.jsxs(x,{container:!0,justifyContent:"space-between",size:12,children:[r.jsx(x,{children:r.jsx(At,{value:t,onChange:e=>{f(e),g([e.valueOf(),"now"===s?Me().valueOf():s.valueOf()]),y((e=>e+1)),Be("MapFilterApplied",{filter:"startDate",value:e.valueOf()})},maxDateTime:Me(),slots:{openPickerIcon:V}})}),r.jsx(x,{children:r.jsx(At,{value:"now"===s?Me():s,onChange:e=>{m(e),g([t.valueOf(),e.valueOf()]),y((e=>e+1)),Be("MapFilterApplied",{filter:"endDate",value:e.valueOf()})},minDateTime:t,maxDateTime:Me(),slots:{openPickerIcon:V},iconPosition:"start"})})]})]})}),r.jsxs(x,{container:!0,justifyContent:"space-between",gap:6,children:[r.jsx(x,{children:r.jsx(W,{sx:{gap:1,margin:0},componentsProps:{typography:{fontSize:{xs:12,sm:16}}},control:r.jsx(F,{checked:l,onChange:e=>{v(e.target.checked),Be("MapFilterApplied",{filter:"showDatapoints",value:!l})},size:"small",disableRipple:!0,sx:{width:10,height:10}}),label:"Show Data Points"})}),r.jsx(x,{children:r.jsx(W,{sx:{gap:1,margin:0},componentsProps:{typography:{fontSize:{xs:12,sm:16}}},control:r.jsx(F,{checked:c,onChange:e=>{C(e.target.checked),Be("MapFilterApplied",{filter:"showArtifacts",value:!c})},size:"small",disableRipple:!0,sx:{width:10,height:10}}),label:"Show Artifacts"})})]}),r.jsxs(x,{container:!0,justifyContent:"space-between",gap:6,children:[r.jsx(x,{children:r.jsx(W,{sx:{gap:1,margin:0},componentsProps:{typography:{fontSize:{xs:12,sm:16}}},control:r.jsx(F,{checked:d,onChange:e=>{S(e.target.checked),Be("MapFilterApplied",{filter:"showAisData",value:!d})},size:"small",disableRipple:!0,sx:{width:10,height:10}}),label:"Show AIS Data"})}),r.jsx(x,{children:r.jsx(W,{sx:{gap:1,margin:0},componentsProps:{typography:{fontSize:{xs:12,sm:16}}},control:r.jsx(F,{checked:u,onChange:e=>{k(e.target.checked),Be("MapFilterApplied",{filter:"showAudioData",value:!u})},size:"small",disableRipple:!0,sx:{width:10,height:10}}),label:"Show Audio Data"})})]}),a?0===a.length?r.jsx(p,{variant:"caption",children:"No artifacts found."}):r.jsxs(x,{item:!0,container:!0,flexDirection:"column",gap:2,marginTop:2,children:[r.jsx(x,{children:r.jsxs(N,{sx:{width:"100%"},size:"small",children:[r.jsx($,{id:"artifacts-multiselect-label",sx:{color:(c?"#FFFFFF":"#9A9CA2")+" !important"},shrink:!0,children:"Artifacts"}),r.jsxs(B,{labelId:"artifacts-multiselect-label",id:"artifacts-multiselect",multiple:!0,value:i,onChange:e=>{const t=e.target.value;t.includes("All")?i.length===a.length?(j([]),Be("MapFilterApplied",{filter:"artifactsCategory",value:[]})):(j(a),Be("MapFilterApplied",{filter:"artifactsCategory",value:a})):(j(t),Be("MapFilterApplied",{filter:"artifactsCategory",value:t}))},input:r.jsx(G,{label:"Artifacts"}),native:!1,displayEmpty:!0,disabled:!c,renderValue:e=>e.length===a.length?"All":0===e.length?"None Selected":`${e.length} Selected`,sx:{color:"#FFFFFF",textTransform:"capitalize","&.Mui-disabled fieldset":{borderColor:"#9A9CA2 !important"},"&.Mui-disabled .MuiSelect-select, &.Mui-disabled svg":{cursor:"not-allowed !important",color:"#9A9CA2 !important","-webkit-text-fill-color":"#9A9CA2 !important"}},children:[r.jsxs(H,{value:"All",children:[r.jsx(F,{checked:i.length===a.length,sx:{padding:"0 10px"}}),r.jsx(U,{primary:"All"})]}),a.map(((e,t)=>r.jsxs(H,{value:e,children:[r.jsx(F,{checked:i.includes(e),sx:{padding:"0 10px"}}),r.jsx(U,{primary:e,sx:{textTransform:"capitalize"}})]},t)))]})]})}),r.jsx(x,{children:r.jsxs(N,{sx:{width:"100%"},size:"small",children:[r.jsx($,{id:"artifacts-select-label",sx:{color:(c?"#FFFFFF":"#9A9CA2")+" !important"},shrink:!0,children:"Visible"}),r.jsx(B,{labelId:"artifacts-select-label",id:"artifacts-select",value:O||100,onChange:e=>{A(e.target.value),Be("MapFilterApplied",{filter:"numberOfArtifacts",value:e.target.value})},input:r.jsx(G,{label:"Visible"}),native:!1,displayEmpty:!0,disabled:!c,renderValue:e=>e?`${e} Artifacts`:"None Selected",sx:{color:"#FFFFFF",textTransform:"capitalize","&.Mui-disabled fieldset":{borderColor:"#9A9CA2 !important"},"&.Mui-disabled .MuiSelect-select, &.Mui-disabled svg":{cursor:"not-allowed !important",color:"#9A9CA2 !important","-webkit-text-fill-color":"#9A9CA2 !important"}},children:["all",100,250,500,1e3,2e3].map(((e,t)=>r.jsx(H,{value:e,children:r.jsx(U,{primary:e,sx:{textTransform:"capitalize"}})},t)))})]})}),r.jsx(x,{children:r.jsxs(N,{sx:{width:"100%"},size:"small",children:[r.jsx($,{id:"artifacts-type-label",sx:{color:(c?"#FFFFFF":"#9A9CA2")+" !important"},shrink:!0,children:"Type"}),r.jsx(B,{labelId:"artifacts-type-label",id:"artifacts-type",value:M,onChange:e=>{T(e.target.value),Be("MapFilterApplied",{filter:"artifactsType",value:e.target.value})},input:r.jsx(G,{label:"Type"}),native:!1,displayEmpty:!0,disabled:!c,renderValue:e=>e?`${e} Artifacts`:"None Selected",sx:{color:"#FFFFFF",textTransform:"capitalize","&.Mui-disabled fieldset":{borderColor:"#9A9CA2 !important"},"&.Mui-disabled .MuiSelect-select, &.Mui-disabled svg":{cursor:"not-allowed !important",color:"#9A9CA2 !important","-webkit-text-fill-color":"#9A9CA2 !important"}},children:["image","video","both"].map(((e,t)=>r.jsx(H,{value:e,children:r.jsx(U,{primary:e,sx:{textTransform:"capitalize"}})},t)))})]})})]}):r.jsx(w,{variant:"rectangular",height:40})]})},Tt=({setDatapointsDistance:t,datapointsDistance:s})=>{const[n,o]=e.useState(s);return e.useEffect((()=>{o(s)}),[s]),r.jsx(R,{valueLabelFormat:e=>`${e} meters`,valueLabelDisplay:"auto",sx:{padding:0,color:$e.palette.custom.mediumGrey},value:n,onChange:(e,t)=>o(t),onChangeCommitted:()=>{t(n)},min:50,max:1e4})},Pt=({setInterval:t,interval:s})=>{const[n,o]=e.useState(s);return e.useEffect((()=>{o(s)}),[s]),r.jsx(R,{valueLabelFormat:e=>`${e}m`,valueLabelDisplay:"auto",value:n,onChange:(e,t)=>o(t),onChangeCommitted:(e,s)=>t(n),min:1,max:60,sx:{padding:0,color:$e.palette.custom.mediumGrey}})},It=({interval:e,datapointsDistance:t,disableAdvancedSettingsReset:s,resetAdvancedSettings:n,setInterval:o,setDatapointsDistance:a})=>r.jsxs(x,{container:!0,flexDirection:"column",color:"#FFFFFF",gap:2,paddingX:2,paddingY:2,position:"relative",className:"map-step-6",sx:{maxWidth:{xs:"none",md:380},width:"100%"},children:[r.jsx(b,{disabled:s,disableRipple:!0,sx:{width:15,height:15,marginLeft:1,paddingX:2,position:"absolute",top:{xs:"-50px",md:"-30px"},right:{xs:"0",md:"35px"}},onClick:e=>{e.stopPropagation(),n()},children:r.jsx(L,{fontSize:"small",sx:{opacity:s&&.5}})}),r.jsxs(x,{container:!0,flexDirection:"column",paddingRight:2,children:[r.jsxs(x,{justifyContent:"space-between",display:"flex",alignItems:"center",gap:1,children:[r.jsx(p,{textAlign:"center",justifyContent:"center",color:$e.palette.custom.mediumGrey,fontSize:"12px",children:"Interval"}),r.jsx(Z,{enterDelay:300,title:"Change the minimum interval between each captured coordinate. e.g., if interval is 5m, then all the points on the polyline will be at least 5 minutes apart.",children:r.jsx(Y,{sx:{color:$e.palette.custom.mediumGrey,backgroundColor:z($e.palette.custom.offline,.08),fontSize:"18px"}})})]}),r.jsx(x,{paddingLeft:2,children:r.jsx(Pt,{interval:e,setInterval:o})})]}),r.jsxs(x,{container:!0,flexDirection:"column",paddingRight:2,children:[r.jsxs(x,{justifyContent:"space-between",display:"flex",alignItems:"center",gap:1,children:[r.jsx(p,{textAlign:"center",justifyContent:"center",color:$e.palette.custom.mediumGrey,fontSize:"12px",children:"Distance b/w Data Points"}),r.jsx(Z,{enterDelay:300,title:"Change the minimum distance between data points. Decreasing this value may cause performance drop.",children:r.jsx(Y,{sx:{color:$e.palette.custom.mediumGrey,fontSize:"18px"}})})]}),r.jsx(x,{paddingLeft:2,children:r.jsx(Tt,{datapointsDistance:t,setDatapointsDistance:a})})]})]}),Et=[{title:"Vessels",imgSrc:"/icons/vessels-icon.svg",className:"map-step-1"},{title:"Filters",imgSrc:"/icons/filters-icon.svg",className:"map-step-3"},{title:"Advanced Settings",imgSrc:"/icons/advance-settings-icon.svg",className:"map-step-5"}],Lt=()=>{const{isMobile:t,selectedVessel:s,setSelectedVessel:n,devMode:o,google:a}=ve(),{vesselInfo:i,fetchVesselsInfo:l}=Ze(),{user:c}=Ne(),[d,u]=e.useState(Pe.journeyStart),[h,f]=e.useState("now"),[m,g]=e.useState(Pe.interval),[b,y]=e.useState(Pe.datapointsDistance),[j,v]=e.useState(),[C,S]=e.useState([]),[k,F]=e.useState([]),[D,O]=e.useState([]),[A,M]=e.useState([]),[T,P]=e.useState([]),[I,E]=e.useState([]),[L,R]=e.useState(),[V,W]=e.useState([]),[N,$]=e.useState(!0),[B,H]=e.useState(!0),[U,G]=e.useState([d.valueOf(),Me().valueOf()]),[Z,Y]=e.useState(0),[J,K]=e.useState(!0),[Q,ee]=e.useState(!0),[te,se]=e.useState(!0),[ne,oe]=e.useState(!0),[ae,re]=e.useState(c?.home_port_filter_mode||"ONLY_NON_HOME_PORTS"),[ie,le]=e.useState(null),[ce,de]=e.useState(""),[ue,pe]=e.useState("all"),{pathname:he}=_(),[fe]=X(),[me,ge]=e.useState("both"),[xe,be]=e.useState(!1),[ye,we]=e.useState(Pe.journeyStart),[Ce,Se]=e.useState(Me(Pe.journeyStart).add(1,"hour")),[ke,_e]=e.useState(!0),[Fe,De]=e.useState(null);e.useEffect((()=>{const e=Me(d),t=24*("now"===h?Me():Me(h)).diff(e,"day")*60,s=Math.min(60,Math.floor(t/280));g(s)}),[d,h]),e.useEffect((()=>{re(c?.home_port_filter_mode||"ONLY_NON_HOME_PORTS")}),[c?.home_port_filter_mode]),e.useEffect((()=>{he.includes("/map")||pe("all")}),[he]),e.useEffect((()=>{const e=fe.get("artifact");e&&De(e)}),[fe,he]),e.useEffect((()=>{if(s){const e=A.some((e=>e===s)),t=T.concat(I).some((e=>e===s));e?(v(s),n(null)):t&&n(null)}}),[A,T,I]),e.useEffect((()=>{if(s){const e=Array.isArray(C)&&C.find((e=>e.id===s));if(e&&F([e]),k.some((e=>e.id===s))){A.some((e=>e===s))&&v(s),n(null)}}}),[s]),e.useEffect((()=>{S(),v()}),[]),e.useEffect((()=>{i&&i.length>0&&Oe()}),[i]),e.useEffect((()=>{if(!C||!Array.isArray(C)||0===C.length)return F([]);F([C[0]])}),[C]),e.useEffect((()=>{0!==A.length&&(void 0===j||j&&A.some((e=>e===j))||v(A[0]))}),[A]),e.useEffect((()=>{d===Pe.journeyStart&&"now"===h?$(!0):$(!1)}),[d,h]),e.useEffect((()=>{m===Pe.interval&&b===Pe.datapointsDistance?H(!0):H(!1)}),[m,b]);const Oe=async()=>{de("");try{if(i&&Array.isArray(i)){const e=i.filter((e=>!!o||e.is_active));0===e.length?de("No vessel coordinates found."):(de(""),S(e.sort(((e,t)=>e.is_live&&!t.is_live?-1:1)).map((e=>({id:e.vessel_id,unit_id:e.unit_id,name:e.name||"Unregistered",region_group:e.region_group_id,vessel_id:e.vessel_id})))))}else l()}catch(e){de("Failed to fetch vessels coordinates. Please try again later. "+e?.response?.data?.message)}},Ae={"Advanced Settings":r.jsx(It,{interval:m,datapointsDistance:b,disableAdvancedSettingsReset:B,resetAdvancedSettings:()=>{g(Pe.interval),y(Pe.datapointsDistance)},setInterval:g,setDatapointsDistance:y,showFilteredCoordinates:xe,setShowFilteredCoordinates:be,fromDate:ye,setFromDate:we,toDate:Ce,setToDate:Se,showCoordinatesPoints:ke,setShowCoordinatesPoints:_e}),Filters:r.jsx(Mt,{journeyStart:d,journeyEnd:h,timeSlider:U,timeSliderKey:Z,selectedArtifactsCategory:V,showDatapoints:J,showArtifacts:Q,showAisData:te,showAudioData:ne,homePortsArtifactsMode:ae,disableFiltersReset:N,artifactsCategories:L,setJourneyStart:u,setJourneyEnd:f,setTimeSlider:G,setTimeSliderKey:Y,setSelectedArtifactsCategory:W,setShowDatapoints:K,setShowArtifacts:ee,setShowAisData:se,setShowAudioData:oe,setHomePortsArtifactsMode:re,resetFilters:()=>{u(Pe.journeyStart),f("now"),G([Pe.journeyStart.valueOf(),Me().valueOf()]),Y((e=>e+1)),re(c?.home_port_filter_mode||"ONLY_NON_HOME_PORTS")},loadingVessels:D,selectedNumberOfArtifacts:ue,setSelectedNumberOfArtifacts:pe,artifactsType:me,setArtifactsType:ge}),Vessels:r.jsx(Ot,{vessels:C,selectedVessels:k,availableVessels:A,emptyVessels:T,errorsomeVessels:I,loadingVessels:D,handleVesselSelect:e=>{const t=k.some((t=>t.id===e.id));Be("VesselSelected",{vesselId:e.id,selected:!t}),F((t=>t.some((t=>t.id===e.id))?t.filter((t=>t.id!==e.id)):[...t,e]))},setFocusedVessel:v,focusedVessel:j})};return r.jsxs(x,{container:!0,height:"100%",flexWrap:"nowrap",overflow:"auto",flexDirection:{xs:"column",lg:"row"},color:"#FFFFFF",position:"relative",children:[r.jsxs(x,{minHeight:{xs:400,lg:"auto"},position:"relative",size:"grow",children:[C&&a?r.jsx(Dt,{setAvailableVessels:M,setEmptyVessels:P,setErrorsomeVessels:E,focusedVessel:j,datapointsDistance:b,vessels:k,journeyStart:d,journeyEnd:h,initialZoom:3,interval:m,setArtifactsCategories:R,selectedArtifactsCategory:V,timeSlider:U,showDatapoints:J,showArtifacts:Q,showAisData:te,showAudioData:ne,setLoadingVessels:O,loadingVessels:D,emptyVessels:T,errorsomeVessels:I,selectedNumberOfArtifacts:ue,setSelectedNumberOfArtifacts:pe,artifactsType:me,showFilteredCoordinates:xe,fromDate:ye,toDate:Ce,showCoordinatesPoints:ke,setSelectedArtifactsCategory:W,homePortsArtifactsMode:ae,targetArtifactId:Fe,setTargetArtifactId:De,clearUrlParameter:()=>{const e=new URLSearchParams(fe);e.delete("artifact");const t=`${he}${e.toString()?"?"+e.toString():""}`;window.history.replaceState({},"",t)}}):r.jsx(w,{animation:"wave",variant:"rectange",width:"100%",height:"100%"}),ce&&r.jsx(x,{sx:{position:"absolute",bottom:16,left:16,zIndex:10,display:"flex"},size:"grow",children:r.jsx(q,{severity:"error",children:ce})})]}),r.jsx(je,{menuItems:Et,activeComponentTitle:ie,onMenuUpdate:e=>{le(e)},withPadding:!1,children:t?Et.map(((e,t)=>r.jsxs(x,{container:!0,display:{xs:"flex",lg:"none"},flexDirection:"column",gap:2,paddingTop:4,paddingX:1.2,sx:{backgroundColor:$e.palette.custom.darkBlue,paddingBottom:1.2,position:"relative",maxWidth:"calc(100vw - 10px)"},children:[r.jsx(x,{size:12,children:r.jsx(p,{fontSize:"16px",fontWeight:"600",color:"#FFFFFF",children:e.title})}),r.jsx(x,{sx:{backgroundColor:z($e.palette.primary.light,.5),borderRadius:"10px",padding:"Vessels"===e.title?0:"15px 20px"},children:Ae[e.title]})]},t))):Ae[ie]||null})]})};export{Lt as default};
